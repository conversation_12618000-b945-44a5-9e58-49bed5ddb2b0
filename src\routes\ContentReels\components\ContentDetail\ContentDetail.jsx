import { Stack, Typography } from '@mui/material';
import { CONTENT_CATEGORY, CONTENT_TYPES } from 'constants';
import dayjs from 'dayjs';

function ContentDetailTitle({ title }) {
  return (
    <Typography component="span" sx={{ fontSize: '14px' }}>
      {title}
    </Typography>
  );
}

function ContentDetailValue({ value }) {
  return (
    <Typography
      component="span"
      sx={{ fontSize: '14px', fontWeight: '500' }}
    >
      {value}
    </Typography>
  );
}

function resolveType(typeValue) {
  return CONTENT_TYPES.filter((t) => t.value === typeValue)[0].name;
}

function resolveCategory(categoryValue) {
  return CONTENT_CATEGORY.filter((c) => c.value === categoryValue)[0].name;
}

function formatDate(date) {
  return dayjs(date).format('HH:mm  YYYY-MM-DD');
}

export default function ContentDetail({
  type, category, createdAt,
}) {
  const contentType = `${resolveType(type)}/${resolveCategory(category)}`;

  return (
    <Stack direction="row" justifyContent="space-between">
      <div>
        <ContentDetailTitle title="نوع محتوا: " />
        <ContentDetailValue value={contentType} />
      </div>

      <div>
        <ContentDetailValue value={formatDate(createdAt)} />
      </div>
    </Stack>
  );
}
