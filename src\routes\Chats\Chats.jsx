import { Box, Grid, Typography, styled } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { selectChatIds, setHasUnreadMessage } from 'store/messenger';
import ChatItem from 'components/ChatItem/ChatItem';
import { useNavigate } from 'react-router-dom';
import { setPathParam, useIsDesktop } from 'utils';
import { PATHS } from 'constants';
import useLoadContacts from 'hooks/useLoadContacts';
import { useEffect, useState } from 'react';
import useChats from 'hooks/useChats';
import DesktopAppBar, {
  DESKTOP_APPBAR_HEIGHT,
} from 'components/DesktopAppBar/DesktopAppBar';
import MyBottomNavigation, {
  BOTTOM_NAV_HEIGHT,
} from 'components/MyBottomNavigation/MyBottomNavigation';
import NewFab from './components/NewFab/NewFab';
import NoChatPlaceHolder from './components/NoChatPlaceHolder/NoChatPlaceHolder';
import MyAppBar from './components/MyAppBar/MyAppBar';

const MyIframe = styled('iframe')(() => ({
  width: '100%',
  height: '100%',
  border: 'none',
}));

export default function Chats() {
  useLoadContacts();
  const isDesktop = useIsDesktop();

  const { getChats } = useChats();
  useEffect(() => getChats(), []);

  const chatIds = useSelector(selectChatIds);
  const navigate = useNavigate();

  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(setHasUnreadMessage({ hasUnreadMessage: false }));
  }, []);

  const buildChatPath = chatId => setPathParam(PATHS.chat, 'chatId', chatId);

  const [chatUrl, setChatUrl] = useState('');
  const onChatItemClick = chatId => {
    navigate(buildChatPath(chatId));
    // if (isDesktop) {
    //   setChatUrl(`${buildChatPath(chatId)}?fromDesktop=yes`);
    // } else {
    //   navigate(buildChatPath(chatId));
    // }
  };

  return (
    <Box
      sx={{
        height: '100vh',
        paddingTop: DESKTOP_APPBAR_HEIGHT,
        paddingBottom: isDesktop ? 0 : BOTTOM_NAV_HEIGHT,
      }}
    >
      {isDesktop && <DesktopAppBar />}
      {!isDesktop && <MyAppBar />}

      {chatIds.length === 0 && <NoChatPlaceHolder />}

      <Grid
        container
        sx={{ display: 'flex', justifyContent: 'center', height: '100%' }}
      >
        <Grid
          item
          xs={12}
          lg={6}
          sx={{
            overflowY: 'scroll',
            height: '100%',
            p: 2,
          }}
          direction="row"
          rowSpacing={2}
          container
        >
          {chatIds.map(chatId => (
            <ChatItem
              key={chatId}
              chatId={chatId}
              withLastMessage
              withUnreadBadge
              onClick={() => {
                onChatItemClick(chatId);
              }}
            />
          ))}
        </Grid>

        {/* {isDesktop && (
          <Grid item lg={8} sx={{ height: '100%' }}>
            {!chatUrl && (
              <Typography align="center">
                گفت‌وگویی را برای نمایش انتخاب کنید.
              </Typography>
            )}
            {chatUrl && <MyIframe src={chatUrl} title="chat" sx={{}} />}
          </Grid>
        )} */}
      </Grid>

      <NewFab />
      {!isDesktop && <MyBottomNavigation />}
    </Box>
  );
}
