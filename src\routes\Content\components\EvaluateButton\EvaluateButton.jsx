import { Button, Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { Quiz } from '@mui/icons-material';
import { PATHS } from '../../../../constants';
import { setPathParam } from '../../../../utils';

export default function EvaluateButton({
  id,
  active = false,
  myScore = 0,
  sx,
}) {
  const navigate = useNavigate();

  function onEvaluateClick() {
    navigate(setPathParam(PATHS.contentEvaluate, 'contentId', id));
  }

  return (
    <Button
      onClick={onEvaluateClick}
      variant={active ? 'contained' : ''}
      startIcon={active ? <Quiz /> : null}
      sx={{
        height: '40px',
        backgroundColor: active ? '' : '#F1F2F9',
        borderRadius: '6px',
        // border: '1px solid #D6D6D6',
        ...sx,
      }}
      disabled={!active}
    >
      <Typography sx={{ fontSize: '14px', fontWeight: 'bold', fontStyle: 'bold' }}>
        <span>{active ? 'شروع ارزیابی' : `ارزیابی انجام شده است. (${Number(myScore).toFixed(2)} از ۱۰)`}</span>
      </Typography>
    </Button>
  );
}
