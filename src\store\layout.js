import { createSlice } from '@reduxjs/toolkit';

export const layoutSlice = createSlice({
  name: 'layout',
  initialState: {
    snackbar: null,
    sidebarCollapsed: false,
  },
  reducers: {
    setTitle: (state, action) => {
      state.title = action.payload;
    },
    setAppbarMenu: (state, action) => {
      state.appbarMenu = action.payload;
    },
    setSnackbar: (state, action) => {
      state.snackbar = action.payload;
    },
    setSidebarCollapsed: (state, action) => {
      state.sidebarCollapsed = action.payload;
    },
  },
});

export const { setTitle, setAppbarMenu, setSnackbar, setSidebarCollapsed } =
  layoutSlice.actions;

export const selectTitle = state => state.layout.title;
export const selectAppbarMenu = state => state.layout.appbarMenu;
export const selectSnackbar = state => state.layout.snackbar;
export const selectSidebarCollapsed = state => state.layout.sidebarCollapsed;

export default layoutSlice.reducer;
