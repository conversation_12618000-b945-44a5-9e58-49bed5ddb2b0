import { Navigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { PATHS } from '../../constants';
import { GET_ME_URL, getMe } from '../../apis/auth';
import LoadingPage from '../LoadingPage/LoadingPage';

export default function AdminRequired({ children }) {
  const { isLoading, data } = useQuery(GET_ME_URL, () => getMe());
  if (isLoading) {
    return <LoadingPage />;
  }
  const me = data.data;
  // me.can_see_admin_panel = undefined;
  return !!me && !!me.can_see_admin_panel ? (
    children
  ) : (
    <Navigate to={PATHS.home} replace />
  );
}
