import { Navigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { PATHS } from '../../constants';
import { GET_ME_URL, getMe } from '../../apis/auth';
import LoadingPage from '../LoadingPage/LoadingPage';
import { Box, Typography } from '@mui/material';

export default function PermissionRequired({ children, requiredPermission, redirectTo = PATHS.home }) {
  const { isLoading, data } = useQuery(GET_ME_URL, () => getMe());
  
  if (isLoading) {
    return <LoadingPage />;
  }
  
  const me = data?.data;
  const hasPermission = me?.groups?.includes(requiredPermission);
  
  if (!hasPermission) {
    return (
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        <Typography variant="h6" color="error">
          شما دسترسی لازم برای مشاهده این صفحه را ندارید
        </Typography>
      </Box>
    );
  }
  
  return children;
}
