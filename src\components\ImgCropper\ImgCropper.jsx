import { Box, Stack } from '@mui/material';
import { useCallback, useState } from 'react';
import <PERSON><PERSON><PERSON> from 'react-easy-crop';
import LoadingButton from 'components/LoadingButton/LoadingButton';
import { PROFILE_DIMENTION, CONTENT_DIMENSION } from 'constants';
import {
  canvasToBlob,
  cropImage, resizeCanvas,
} from './canavsUtils';

export default function ImgCropper({
  src,
  hideCropper,
  setCroppedImg,
  cropShape = 'rect',
  aspect = 1,
}) {
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
  const [zoom, setZoom] = useState(1);
  const [loading, setLoading] = useState(false);

  const onCropComplete = useCallback(
    (croppedArea, _croppedAreaPixels) => setCroppedAreaPixels(_croppedAreaPixels),
    [],
  );

  const onConfirm = useCallback(async () => {
    setLoading(true);

    const croppedCanvas = await cropImage(src, croppedAreaPixels);

    const finalDimension = aspect === 1 ? PROFILE_DIMENTION : CONTENT_DIMENSION;
    const resizedCanvas = resizeCanvas(croppedCanvas, finalDimension);

    const resizedImage = await canvasToBlob(resizedCanvas);
    setLoading(false);

    setCroppedImg(resizedImage);
    hideCropper();
  }, [croppedAreaPixels]);

  return (
    <Box
      sx={(theme) => ({
        position: 'fixed',
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 1500,
        background: theme.palette.grey[400],
        p: 2,
      })}
      justifyContent="center"
    >
      <Cropper
        image={src}
        aspect={aspect}
        zoom={zoom}
        crop={crop}
        onCropChange={setCrop}
        onCropComplete={onCropComplete}
        onZoomChange={setZoom}
        cropShape={cropShape}
      />
      <Stack
        direction="row"
        sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          p: 2,
        }}
      >
        <LoadingButton
          fullWidth
          variant="contained"
          onClick={onConfirm}
          loading={loading}
        >
          تایید
        </LoadingButton>
      </Stack>
    </Box>
  );
}
