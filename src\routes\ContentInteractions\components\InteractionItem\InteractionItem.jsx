import { Avatar, Stack, Typography } from '@mui/material';
import MyLink from 'components/MyLink/MyLink';
import { PATHS } from 'constants';
import { setPathParam } from 'utils';

export default function InteractionItem({
  firstName, lastName, userId, avatar, username,
}) {
  const profilePath = setPathParam(PATHS.profile, 'userId', userId);

  const name = (firstName || lastName) ? `${firstName} ${lastName}` : username;

  return (
    <MyLink to={profilePath}>
      <Stack direction="row" spacing={2}>
        <Avatar src={avatar} />
        <Typography sx={{ marginTop: 'auto!important', marginBottom: 'auto!important' }}>
          {name}
        </Typography>
      </Stack>
    </MyLink>
  );
}
