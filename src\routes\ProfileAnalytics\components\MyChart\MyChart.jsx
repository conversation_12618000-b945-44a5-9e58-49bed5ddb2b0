import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Cartes<PERSON>,
  <PERSON>sponsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'recharts';
import { Box } from '@mui/material';
import style from './MyChart.module.css';

function parseDataDate(data) {
  return data.map(item => ({
    day: new Date(item.day).toLocaleDateString('fa', {
      day: 'numeric',
      month: 'numeric',
    }),
    count: item.count,
  }));
}

export default function MyChart({ data, sx }) {
  const parsedData = parseDataDate(data);
  return (
    <Box sx={{ ...sx }}>
      <ResponsiveContainer
        width="100%"
        aspect={4 / 3}
        className={style.chartContainer}
      >
        <BarChart
          data={parsedData}
          margin={{
            top: 24,
            right: 24,
            left: 0,
            bottom: 0,
          }}
        >
          {/*<Line*/}
          {/*  type="basis"*/}
          {/*  dataKey="count"*/}
          {/*  stroke="#11A6A1"*/}
          {/*  strokeWidth={3}*/}
          {/*  dot={false}*/}
          {/*/>*/}
          <Bar dataKey="count" fill="#11A6A1" maxBarSize={30} />
          <XAxis
            dataKey="day"
            tick={{ stroke: '#999999', strokeWidth: 0.2 }}
            style={{ fontSize: '10px' }}
          />
          <YAxis
            allowDecimals={false}
            tickMargin={20}
            tick={{ stroke: '#999999', strokeWidth: 0.2 }}
            style={{ fontSize: '13px' }}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: '#f5f5f5', // Change background color
              border: '1px solid #ccc', // Add a border
              borderRadius: '8px', // Rounded corners
              boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.1)', // Add shadow
            }}
            cursor={false}
            formatter={(value, name) => {
              if (name === 'count') {
                return [`${value}`, 'تعداد']; // Format the label and value
              }
              return [value, name];
            }}
          />
          <CartesianGrid stroke="#E4E4E433" vertical={false} />
        </BarChart>
      </ResponsiveContainer>
    </Box>
  );
}
