import { Stack, Typography } from '@mui/material';
import AppbarMenu from '../AppbarMenu/AppbarMenu';

export default function Title({
  title, contentId, showDeleteBottomSheet, showReportBottomSheet, isMine,
  evaluationRequested = false,
  total_score = 0,
}) {
  return (
    <Stack direction="row" justifyContent="space-between">
      <Typography sx={{ fontWeight: 700, color: 'black', fontSize: '26px' }}>
        {title}
      </Typography>
      <AppbarMenu
        contentId={contentId}
        showDeleteBottomSheet={showDeleteBottomSheet}
        showReportBottomSheet={showReportBottomSheet}
        isMine={isMine}
        evaluationRequested={evaluationRequested}
        total_score={total_score}
      />
    </Stack>
  );
}
