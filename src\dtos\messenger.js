import { MESSAGE_TYPES, MESSAGE_DELIVER_STATUS } from 'constants';
import { isAudio, isImage, isVideo } from 'utils';
import { v4 as uuidv4 } from 'uuid';
import { makeStruct } from './utils';

export const Author = new makeStruct('id, avatar, firstName, lastName, displayUsername');
export const Message = new makeStruct('author, chatId, text, type, deliveryStatus, deliveryToken, createdAt, sentByMe, uploadProgress, file, forwardedFrom, replyTo, displayText, extraData');
export const ChatBrief = new makeStruct('id, name, avatar, creator, type, displayName, displayAvatar, lastMessageCreatedAt, lastMessageText, lastMessageType, hasUnreadMessage');
export const Participant = new makeStruct('id, avatar, first_name, last_name, isOnline, lastOnline');
export const Contact = new makeStruct('id, user, userFirstName, userLastName, directChatId, isOnline, lastOnline');

export function createAuthor(me) {
  return new Author(
    me.id,
    me.avatar,
    me.first_name,
    me.last_name,
  );
}

export function createTextMessage(me, chatId, text, replyTo) {
  const author = createAuthor(me);

  return new Message(
    author,
    Number(chatId),
    text,
    MESSAGE_TYPES.TEXT,
    MESSAGE_DELIVER_STATUS.SENDING,
    uuidv4(),
    new Date().toISOString(),
    null,
    null,
    null,
    null,
    replyTo,
  );
}

export function createCallMessage(me, chatId) {
  const author = createAuthor(me);

  return new Message(
    author,
    Number(chatId),
    '',
    MESSAGE_TYPES.CALL,
    MESSAGE_DELIVER_STATUS.SENDING,
    uuidv4(),
    new Date().toISOString(),
    null,
    null,
    null,
    null,
    null,
    null,
  );
}

export function createContentMessage(me, chatId, contentId) {
  const author = createAuthor(me);

  return new Message(
    author,
    Number(chatId),
    contentId,
    MESSAGE_TYPES.CONTENT,
    MESSAGE_DELIVER_STATUS.SENDING,
    uuidv4(),
    new Date().toISOString(),
    null,
    null,
    null,
    null,
    null,
  );
}

export function createFileMessage(me, chatId, file, replyTo) {
  const author = createAuthor(me);

  return new Message(
    author,
    Number(chatId),
    file.name,
    MESSAGE_TYPES.FILE,
    MESSAGE_DELIVER_STATUS.SENDING,
    uuidv4(),
    new Date().toISOString(),
    true,
    '0',
    null,
    null,
    replyTo,
  );
}

export function createAudioMessage(me, chatId, file, replyTo) {
  const author = createAuthor(me);

  return new Message(
    author,
    Number(chatId),
    file.name,
    MESSAGE_TYPES.AUDIO,
    MESSAGE_DELIVER_STATUS.SENDING,
    uuidv4(),
    new Date().toISOString(),
    true,
    '0',
    null,
    null,
    replyTo,
  );
}

export function createMediaMessage(me, chatId, file, caption, replyTo) {
  const author = createAuthor(me);

  let messageType = null;
  if (isImage(file.type)) {
    messageType = MESSAGE_TYPES.IMAGE;
  } else if (isVideo(file.type)) {
    messageType = MESSAGE_TYPES.VIDEO;
  } else if (isAudio(file.type)) {
    messageType = MESSAGE_TYPES.AUDIO;
  }

  return new Message(
    author,
    Number(chatId),
    caption,
    messageType,
    MESSAGE_DELIVER_STATUS.SENDING,
    uuidv4(),
    new Date().toISOString(),
    true,
    '0',
    URL.createObjectURL(file),
    null,
    replyTo,
  );
}

export function genAuthorFullName(author) {
  if (!author.firstName && !author.lastName) {
    return 'بدون نام';
  }

  return `${author.firstName} ${author.lastName}`;
}

export function createContact(id, user, userFirstName, userLastName, directChatId) {
  return new Contact(id, user, userFirstName, userLastName, directChatId);
}
