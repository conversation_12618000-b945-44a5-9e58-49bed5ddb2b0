import myAxios from './myAxios';

export async function getChatParticipants({ chatId }) {
  return myAxios.get(`/messenger/chats/${chatId}/participants/`);
}

export async function getChatMessages({ chatId, page }) {
  const params = {};
  if (page) params.page = page;
  return myAxios.get(`/messenger/chats/${chatId}/messages/`, { params });
}

export async function getChat({ chatId }) {
  return myAxios.get(`/messenger/chats/${chatId}/`);
}

export async function updateChat(chatId, data) {
  return myAxios.patch(`/messenger/chats/${chatId}/`, data);
}

export async function addParticipant({ chatId, participant }) {
  return myAxios.post(`/messenger/chats/${chatId}/participants/`, { participant });
}

export async function removeParticipant({ chatId, participant }) {
  return myAxios.delete(`/messenger/chats/${chatId}/participants/`, { data: { participant } });
}

export async function addAdmin({ chatId, admin }) {
  return myAxios.post(`/messenger/chats/${chatId}/admins/`, { admin });
}

export async function removeAdmin({ chatId, admin }) {
  return myAxios.delete(`/messenger/chats/${chatId}/admins/`, { data: { admin } });
}

export function blockUser({ chatId, blocked }) {
  return myAxios.post(`/messenger/chats/${chatId}/block-user/`, { blocked });
}

export async function unblockUser({ chatId, blocked }) {
  return myAxios.delete(`/messenger/chats/${chatId}/block-user/`, { data: { blocked } });
}
