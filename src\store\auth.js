import { createSlice } from '@reduxjs/toolkit';

export const authSlice = createSlice({
  name: 'auth',
  initialState: {},
  reducers: {
    submitMobile: (state, action) => {
      state.mobile = action.payload;
    },
    setMe: (state, action) => {
      state.me = action.payload;
    },
  },
});

export const { submitMobile, setMe } = authSlice.actions;

export const selectMobile = (state) => state.auth.mobile;
export const selectMe = (state) => state.auth.me;
export const selectMyId = state => state.auth.me?.id

export default authSlice.reducer;
