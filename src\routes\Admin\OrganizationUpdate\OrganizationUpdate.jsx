import {
  Box,
  Button,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { CONTENT_CATEGORY, PATHS } from 'constants';
import { useIsDesktop } from 'utils';
import ContentPasteOutlinedIcon from '@mui/icons-material/ContentPasteOutlined';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import { useEffect, useRef, useState } from 'react';
import { useLoaderData, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import File from '../../../components/ContentForm/components/File/File';
import MyBackdrop from '../../../components/ContentForm/components/MyBackdrop/MyBackdrop';
import CircularProgressWithValue from '../../../components/CircularProgressWithValue/CircularProgressWithValue';
import { setSnackbar } from '../../../store/layout';
import { selectMe } from '../../../store/auth';
import {
  updateAnnouncement,
  getAnnouncement,
} from '../../../apis/announcement';
import MultiSelectWithTabs from '../GroupMsgCreate/components/MultiSelectWithTabs';
import uploadFile from '../../../apis/storage';
import LoadingPage from '../../../components/LoadingPage/LoadingPage';
import {
  getOrganization,
  updateOrganization,
} from '../../../apis/organization';
import OrganizationSelect from '../OrganizationCreate/components/OrganizationSelect';

export async function loader({ params }) {
  return params.organizationId;
}

export default function OrganizationUpdate() {
  const isDesktop = useIsDesktop();

  const navigate = useNavigate();

  const organizationId = useLoaderData();
  const { isLoading, data } = useQuery(
    [PATHS.admin.organizationUpdate, organizationId],
    () => getOrganization(organizationId),
  );

  const organization = isLoading ? {} : data.data;

  const descriptionRef = useRef(null);
  const formRef = useRef(null);

  const [filePreview, setFilePreview] = useState(null);
  const [fileType, setFileType] = useState(undefined);

  const [selectedOrganization, setSelectedOrganization] = useState({});

  useEffect(() => {
    if (organization) {
      setSelectedOrganization({
        id: organization?.parent?.id,
        value: organization?.parent?.name,
      });
    }
  }, [organization]);

  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();
  const abortController = useRef(null);
  const mutation = useMutation(
    async ({ event }) => {
      setLoading(true);

      const { name, description, file } = event.target;

      if (name.value === '') {
        throw { message: 'نام سازمان اجباری است', severity: 'error' };
      } else if (!selectedOrganization.id) {
        throw { message: 'سازمان مرجع را انتخاب کنید', severity: 'error' };
      }

      let uploadedFile;

      if (file?.files[0])
        uploadedFile = await uploadFile(file.files[0], setProgress);
      const data = {
        name: name.value,
        description: description.value || '',
        parent: selectedOrganization?.id,
      };

      if (uploadedFile?.data?.id) data.avatar = uploadedFile.data.id;

      return updateOrganization(organization.id, data);
    },
    {
      onSuccess: async data => {
        dispatch(
          setSnackbar({
            message: 'سازمان با موفقیت ویرایش شد',
            severity: 'success',
          }),
        );

        localStorage.setItem(
          'notification',
          JSON.stringify({
            message:
              'درخواست شما ثبت شد و سیستم درحال پردازش درخواست شماست. این تغییر دقایقی زمان خواهد برد. بعد از چند دقیقه صفحه را مجدد بارگذاری نمایید.',
            severity: 'success',
            timestamp: Date.now(), // Optional: to manage message expiration
          }),
        );

        setProgress(0);
        setLoading(false);
        formRef.current.reset();
        navigate('/admin-panel/organization/graph');
      },
      onError: error => {
        if (error?.response?.status === 400) {
          dispatch(
            setSnackbar({ message: 'خطا در ویرایش سازمان', severity: 'error' }),
          );
        } else dispatch(setSnackbar(error));

        setProgress(0);
        setLoading(false);
      },
    },
  );

  const errors = mutation.error?.response?.data || {};

  const submitForm = event => {
    abortController.current = new AbortController();
    event.preventDefault();
    mutation.mutate({ event });
  };

  const getFilePreview = () => {
    if (filePreview) return URL.createObjectURL(filePreview);
    if (organization.avatar) return organization.avatar;
    return null;
  };

  useEffect(
    () => () => {
      abortController.current?.abort();
    },
    [],
  );
  const cancelCreate = () => {
    abortController.current.abort();
  };

  const pasteDescription = async () => {
    descriptionRef.current.value = await navigator.clipboard.readText();
  };

  const DIVIDER_MY = 3;

  if (isLoading) {
    return <LoadingPage />;
  }

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        overflowY: 'scroll',
        mt: 2,
      }}
    >
      <form onSubmit={submitForm} ref={formRef}>
        <Grid container columnSpacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} lg={4}>
            <File
              file={organization.avatar}
              fileType="image/jpeg"
              errors={errors.avatar}
              disabled={mutation.isLoading}
              setPreview={setFilePreview}
              preview={getFilePreview()}
              setFileType={setFileType}
              title="بارگذاری لوگو سازمان"
            />
          </Grid>

          <Grid item xs={12} lg={8} sx={{ mt: isDesktop ? 0 : 2 }}>
            <TextField
              variant="outlined"
              required
              label="نام سازمان"
              name="name"
              defaultValue={organization.name}
              helperText={errors.name}
              error={!!errors.name}
              disabled={mutation.isLoading}
              inputProps={{ maxLength: 100 }}
              fullWidth
            />

            <TextField
              sx={{ mt: 2 }}
              label="توضیحات"
              name="description"
              defaultValue={organization.description}
              variant="outlined"
              multiline
              rows={3}
              helperText={errors.description}
              disabled={mutation.isLoading}
              fullWidth
              inputRef={descriptionRef}
              InputProps={{
                endAdornment: (
                  <IconButton edge="end" onClick={pasteDescription}>
                    <ContentPasteOutlinedIcon />
                  </IconButton>
                ),
              }}
            />

            <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

            <Typography sx={{ fontSize: 16, fontWeight: 'bold' }}>
              سازمان مرجع
            </Typography>

            <FormControl fullWidth sx={{ mt: 2 }}>
              <OrganizationSelect
                selectedOrganization={selectedOrganization}
                setSelectedOrganization={setSelectedOrganization}
              />
            </FormControl>
            <Button
              variant="contained"
              size="large"
              type="submit"
              sx={{ mt: 2, width: '100%' }}
            >
              ویرایش
            </Button>
          </Grid>
        </Grid>
      </form>

      <MyBackdrop open={loading}>
        <Stack spacing={2}>
          <CircularProgressWithValue
            variant="indeterminate"
            value={progress}
            color="inherit"
            size={84}
          />
          <Button
            color="error"
            variant="contained"
            disableElevation
            startIcon={<HighlightOffIcon />}
            onClick={cancelCreate}
          >
            لغو
          </Button>
        </Stack>
      </MyBackdrop>
    </Box>
  );
}
