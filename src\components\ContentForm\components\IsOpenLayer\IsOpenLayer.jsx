import { Checkbox, FormControlLabel, FormGroup } from '@mui/material';

export default function IsOpenLayer({ defaultChecked }) {
  return (
    <FormGroup>
      <FormControlLabel
        sx={{ justifyContent: 'space-between', m: 0, mt: 2 }}
        control={(
          <Checkbox
            defaultChecked={defaultChecked}
            sx={{ pr: 0 }}
            name="isOpenLayer"
            id="is_open_layer"
          />
        )}
        label="فایل بارگذاری شده، لایه باز است؟"
        labelPlacement="start"
      />
    </FormGroup>
  );
}
