import {
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  Box,
  Button,
  Chip,
  Stack,
  IconButton,
  Tooltip,
} from '@mui/material';
import { Visibility, GetApp, Subject } from '@mui/icons-material';
import dayjs from 'dayjs';
import { generateInstructionPDF } from 'utils/pdfGenerator';
import { useDispatch } from 'react-redux';
import { setSnackbar } from 'store/layout';
import { useState } from 'react';

// Helper function to format date to <PERSON>alali
const formatToJalali = date => {
  if (!date) return '';
  try {
    return dayjs(date).format('YYYY/MM/DD');
  } catch (error) {
    return date;
  }
};

// Helper function to truncate text
const truncateText = (text, maxLength = 50) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
};

// Helper function to check if instruction is expired
const isExpired = expirationDate => {
  if (!expirationDate) return false;
  return dayjs(expirationDate).isBefore(dayjs(), 'day');
};

export default function InstructionCard({ instruction, onView }) {
  const [downloading, setDownloading] = useState(false);
  const dispatch = useDispatch();

  const expired = isExpired(instruction.expiration_date);

  const handleDownloadPDF = async e => {
    e.stopPropagation();
    setDownloading(true);

    try {
      await generateInstructionPDF(instruction);

      dispatch(
        setSnackbar({
          message: 'فایل PDF با موفقیت دانلود شد',
          severity: 'success',
        }),
      );
    } catch (error) {
      dispatch(
        setSnackbar({
          message: 'خطا در دانلود فایل PDF',
          severity: 'error',
        }),
      );
    } finally {
      setDownloading(false);
    }
  };

  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        opacity: expired ? 0.6 : 1,
        backgroundColor: expired ? '#f5f5f5' : 'white',
        '&:hover': {
          transform: expired ? 'none' : 'translateY(-4px)',
          boxShadow: expired ? 1 : 4,
        },
      }}
      onClick={onView}
    >
      <CardContent
        sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}
      >
        {/* Instruction Number */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2,
          }}
        >
          <Chip
            label={`شماره: ${instruction.code}`}
            color="primary"
            variant="outlined"
            size="small"
          />
          {expired && (
            <Chip
              label="منقضی شده"
              color="error"
              variant="filled"
              size="small"
            />
          )}
        </Box>

        {/* Title */}
        <Typography
          variant="h6"
          sx={{
            fontWeight: 'bold',
            mb: 2,
            minHeight: '3rem',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
          }}
        >
          {instruction.title}
        </Typography>

        {/* Description */}
        {instruction.description && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ mb: 2, minHeight: '2.5rem' }}
          >
            {truncateText(instruction.description, 50)}
          </Typography>
        )}

        {/* Dates */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="caption" color="text.secondary">
            تاریخ ثبت: {formatToJalali(instruction.created_at)}
          </Typography>
          <br />
          <Typography variant="caption" color="text.secondary">
            تاریخ انقضا: {formatToJalali(instruction.expiration_date)}
          </Typography>
        </Box>

        {/* Axes Count */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Subject sx={{ fontSize: 16, mr: 0.5 }} />
          <Typography variant="body2">
            {instruction.subject?.length || 0} محور
          </Typography>
        </Box>

        {/* Actions */}
        <Stack direction="row" spacing={1} sx={{ mt: 'auto' }}>
          <Button
            variant="contained"
            startIcon={<Visibility />}
            onClick={onView}
            fullWidth
            size="small"
          >
            مشاهده
          </Button>
          <Tooltip title="دانلود PDF">
            <IconButton
              onClick={handleDownloadPDF}
              disabled={downloading}
              color="primary"
              sx={{
                border: '1px solid',
                borderColor: 'primary.main',
              }}
            >
              <GetApp />
            </IconButton>
          </Tooltip>
        </Stack>
      </CardContent>
    </Card>
  );
}
