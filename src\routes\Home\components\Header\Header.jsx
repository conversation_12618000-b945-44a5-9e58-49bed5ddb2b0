import { Grid, Stack, Typography } from '@mui/material';
import MyLink from 'components/MyLink/MyLink';
import { PATHS } from 'constants';
import { Circle } from '@mui/icons-material';
import { useQuery } from 'react-query';
import IconSax from '../../../../components/IconSax/IconSax';
import logo from './static/imgs/logo.png';
import { getMyAnnouncementsUnreadCount } from '../../../../apis/announcement';

export default function Header() {
  const { isLoading, data } = useQuery(['unreadAnnounce'], () =>
    getMyAnnouncementsUnreadCount(),
  );
  const unread = isLoading && !!data ? 0 : data?.data?.unread_number || 0;

  return (
    <Grid
      container
      direction="row"
      justifyContent="space-between"
      xs={12}
      mt={1}
    >
      <img src={logo} alt="logo" />
      <Typography
        sx={{
          fontSize: '24px',
          fontWeight: 700,
          color: 'white',
          ml: 1,
        }}
      >
        تام
      </Typography>

      <Grid flexGrow={1} />

      <MyLink to={PATHS.announcement}>
        <Stack
          direction="row"
          alignItems="center"
          sx={{ position: 'relative', mr: '10px' }}
        >
          <IconSax name="bell" sx={{ color: '#8C8C9F' }} />
          {unread > 0 && (
            <Circle
              sx={{
                position: 'absolute',
                left: 0,
                color: '#E99E0C',
                width: '10px',
                top: 0,
              }}
            />
          )}
        </Stack>
      </MyLink>

      <MyLink to={PATHS.terms}>
        <Stack direction="row" alignItems="center">
          <IconSax name="shield-tick" sx={{ color: '#8C8C9F' }} />
          <Typography
            sx={{
              fontSize: '14px',
              fontWeight: 400,
              color: '#8C8C9F',
              ml: 1,
            }}
          >
            مقررات و آموزش بهره‌برداری
          </Typography>
        </Stack>
      </MyLink>
    </Grid>
  );
}
