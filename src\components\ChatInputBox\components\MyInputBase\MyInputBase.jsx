import { InputBase } from '@mui/material';
import { forwardRef } from 'react';

function MyInputBase({
  value, setValue, onFocus, onBlur,
}, ref) {
  return (
    <InputBase
      placeholder="پیام خود را وارد کنید"
      sx={{ flexGrow: 1, pr: 1 }}
      value={value}
      onChange={(e) => setValue(e.target.value)}
      multiline
      maxRows={2}
      inputProps={{ ref }}
      onFocus={onFocus}
      onBlur={onBlur}
    />
  );
}

export default forwardRef(MyInputBase);
