import './App.css';
import { CssBaseline, ThemeProvider } from '@mui/material';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import rtlPlugin from 'stylis-plugin-rtl';
import { prefixer } from 'stylis';
import { Provider } from 'react-redux';
import { RouterProvider } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import router from './routes/router';
import store from './store/store';
import theme from './theme';
import WSConnector from 'components/WSConnector/WSConnector';
import 'utils/mydayjs';
import Firebase from 'components/Firebase/Firebase';
import { useEffect, useState } from 'react';
import UpdateAvailableBottomSheet from 'components/UpdateAvailableBottomSheet/UpdateAvailableBottomSheet';
import { useServiceWorker } from 'hooks/useServiceWorker';
import AppVersionCheck from 'components/AppVersionCheck/AppVersionCheck';

function disableLongPressMenus() {
  window.oncontextmenu = function (event) {
    event.preventDefault();
    event.stopPropagation();
    return false;
  };
}

const queryClient = new QueryClient();
function App() {
  const cacheRtl = createCache({
    key: 'muirtl',
    stylisPlugins: [prefixer, rtlPlugin],
  });

  useEffect(() => {
    disableLongPressMenus();
  }, []);

  const { waitingWorker, showReload, reloadPage } = useServiceWorker();
  const [showUpdateBS, setShowUpdateBS] = useState(false);
  useEffect(() => {
    if (showReload && waitingWorker) {
      setShowUpdateBS(true);
    }
  }, [waitingWorker, showReload, reloadPage]);

  return (
    <Provider store={store}>
      <CacheProvider value={cacheRtl}>
        <ThemeProvider theme={theme}>
          <CssBaseline />
          <WSConnector />
          {/* <Firebase /> */}
          <QueryClientProvider client={queryClient}>
            <AppVersionCheck />
            <UpdateAvailableBottomSheet
              show={showUpdateBS}
              hideBottomSheet={() => setShowUpdateBS(false)}
              update={reloadPage}
            />
            <RouterProvider router={router} />
          </QueryClientProvider>
        </ThemeProvider>
      </CacheProvider>
    </Provider>
  );
}

export default App;
