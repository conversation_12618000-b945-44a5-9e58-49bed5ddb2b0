import { Stack, TextField } from '@mui/material';
import { reportItem } from 'apis/report';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import { REPORT_ITEM_TYPE } from 'constants';
import { useRef } from 'react';
import { useDispatch } from 'react-redux';
import { setSnackbar } from 'store/layout';

export default function ReportBottomSheet({
  show,
  hideBottomSheet,
  chatId,
}) {
  const inputRef = useRef(null);
  const dispatch = useDispatch();
  function reportChat() {
    reportItem({
      itemType: REPORT_ITEM_TYPE.CHAT,
      itemId: chatId,
      description: inputRef.current.value,
    }).then(() => {
      dispatch(setSnackbar({ message: 'گفت‌وگو با موفقیت گزارش شد.', severity: 'success' }));
      inputRef.current.value = '';
    });
  }

  return (
    <BottomSheet
      title="گزارش گفت‌وگو"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        لطفا توضیح مختصری در مورد دلیل گزارش گفت‌وگو بنویسید.
      </BottomSheetMessage>
      <TextField sx={{ mt: 1 }} inputProps={{ maxLength: 100 }} inputRef={inputRef} />

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            reportChat();
            hideBottomSheet();
          }}
        >
          بله
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={hideBottomSheet}>
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
