import myAxios from './myAxios';

export async function createAnnouncement(data, onProgress, abortController) {
  return myAxios.post('/notification/notification-messages/', data, {
    signal: abortController.signal,
    headers: { 'Content-Type': 'application/json' },
    onUploadProgress: e => {
      onProgress(Math.floor((e.loaded / e.total) * 100));
    },
  });
}

export async function getAnnouncements() {
  return myAxios.get('/notification/notification-messages/');
}

export async function getMyAnnouncements() {
  return myAxios.get('/notification/notification-messages/mines/');
}

export async function getMyAnnouncementsUnreadCount() {
  return myAxios.get(
    '/notification/notification-messages/mines/unread-number/',
  );
}

export async function getAnnouncement(id) {
  return myAxios.get(`/notification/notification-messages/${id}/`);
}

export async function getAnnouncementDailyChart(id) {
  return myAxios.get(`/notification/notification-messages/${id}/read-count/`);
}

export async function getAnnouncementViewers(id) {
  return myAxios.get(`/notification/notification-messages/${id}/read-users/`);
}

export async function deleteAnnouncement(id) {
  return myAxios.delete(`/notification/notification-messages/${id}/`);
}

export async function updateAnnouncement(id, data) {
  return myAxios.patch(`/notification/notification-messages/${id}/`, data);
}

export async function readAnnouncement(id) {
  return myAxios.post(`/notification/notification-messages/mines/${id}/read/`);
}
