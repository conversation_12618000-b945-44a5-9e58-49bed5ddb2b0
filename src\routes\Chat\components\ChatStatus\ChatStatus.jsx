import {
  Avatar, Stack, Typography, styled,
} from '@mui/material';
import { GET_ME_URL, getMe } from 'apis/auth';
import { getChat } from 'apis/messenger';
import MyLink from 'components/MyLink/MyLink';
import { CHAT_TYPES, PATHS } from 'constants';
import { useQuery } from 'react-query';
import { setPathParam } from 'utils';

function genChatInfoLink(chat, myId) {
  if (!chat || !myId) return '';

  if (chat.type === CHAT_TYPES.DIRECT) {
    const otherSideOfChat = chat.participants.filter(
      (participantId) => participantId.toString() !== myId.toString(),
    );
    return setPathParam(PATHS.profile, 'userId', otherSideOfChat);
  }

  return setPathParam(PATHS.chatInfo, 'chatId', chat.id);
}

const Name = styled(Typography)(() => ({
  fontSize: '14px',
  fontWeight: 600,
  color: '#2C2C2E',
}));

export default function ChatStatus({ chatId }) {
  const { data, isSuccess } = useQuery(['get-chat', chatId], () => getChat({ chatId }));
  const chat = isSuccess ? data.data : null;

  const { data: myData } = useQuery(GET_ME_URL, getMe);
  const myId = myData?.data?.id;
  const chatInfoLink = chat ? genChatInfoLink(chat, myId) : null;

  return chat
    ? (
      <MyLink to={chatInfoLink} sx={{ margin: 'auto' }}>
        <Stack direction="row" spacing={1.5} alignItems="center">

          <Avatar sx={{ width: 32, height: 32 }} src={chat.display_avatar} />
          <Name>
            {chat.type === 'g' && (<span>گروه&nbsp;</span>)}
            {chat.type === 'c' && (<span>کانال&nbsp;</span>)}
            {chat.display_name || 'بدون نام'}
          </Name>
        </Stack>
      </MyLink>
    )
    : null;
}
