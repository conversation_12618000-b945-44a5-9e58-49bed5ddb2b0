import { Stack } from '@mui/material';
import CustomAppBar from 'components/CustomAppBar/CustomAppBar';
import ChatStatus from '../ChatStatus/ChatStatus';
import MyAppBarMenu from '../MyAppBarMenu/MyAppBarMenu';
import MyBackButton from '../MyBackButton/MyBackButton';

export default function MyAppBar({
  chatId, isCreator, isDirect,
}) {
  return (
    <CustomAppBar>
      <Stack justifyContent="space-between" direction="row" sx={{ width: '100%' }}>
        <Stack direction="row">
          <MyBackButton />
          <ChatStatus chatId={chatId} />
        </Stack>
      </Stack>
      <MyAppBarMenu chatId={chatId} isCreator={isCreator} isDirect={isDirect} />
    </CustomAppBar>
  );
}
