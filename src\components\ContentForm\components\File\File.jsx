import {
  Box,
  FormHelperText,
  Stack,
  styled,
  SvgIcon,
  Typography,
  useTheme,
} from '@mui/material';
import { useEffect, useRef, useState } from 'react';
import { ReactComponent as FolderAddIcon } from 'static/icons/folder-add.svg';
import ImgCropper from 'components/ImgCropper/ImgCropper';
import { CONTENT_ASPECT_RATIO } from 'constants';
import IconSax from 'components/IconSax/IconSax';
import { isAudio, isImage, isVideo } from 'utils';
import ContentPreview from 'components/ContentPreview/ContentPreview';
import MyReactPlayer from 'components/MyReactPlayer/MyReactPlayer';
import SelectPreviewBottomSheet from '../SelectPreviewBottomSheet/SelectPreviewBottomSheet';
import SelectVideoPreview from '../SelectVideoPreview/SelectVideoPreview';

const HiddenInput = styled('input')(() => ({
  display: 'none',
}));

function NotUploadedBox({
  title = 'برای آپلود فایل اینجا کلیک کنید',
  onClick,
}) {
  return (
    <Stack
      sx={{
        cursor: 'pointer',
        height: '100%',
      }}
      justifyContent="center"
      alignItems="center"
      onClick={onClick}
    >
      <SvgIcon
        component={FolderAddIcon}
        sx={{ fill: 'none', color: '#64676A' }}
      />
      <Typography mt={2} sx={{ color: '#2C2C2E' }}>
        {title}
      </Typography>
    </Stack>
  );
}

function Container({ children, ratio = CONTENT_ASPECT_RATIO }) {
  return (
    <Stack
      sx={{
        position: 'relative',
        borderRadius: '8px',
        border: '1px solid #D1D1D6',
        width: '100%',
        aspectRatio: `${ratio}`,
      }}
      justifyContent="center"
      alignItems="center"
    >
      {children}
    </Stack>
  );
}

function IgnoreContent({ onClick }) {
  const theme = useTheme();
  return (
    <IconSax
      name="trash"
      sx={{
        position: 'absolute',
        top: '8px',
        left: '8px',
        color: theme.palette.primary.main,
        zIndex: 1000,
      }}
      onClick={onClick}
    />
  );
}

export default function File({
  file,
  fileType,
  errors,
  disabled,
  setPreview,
  setFileType,
  preview,
  ratio = CONTENT_ASPECT_RATIO,
  title = 'برای آپلود فایل اینجا کلیک کنید',
}) {
  const fileInput = useRef(null);
  const previewInput = useRef(null);

  const [selectedFile, setSelectedFile] = useState(null);
  const [toBeCroppedImage, setToBeCroppedImage] = useState(null);
  const [showCropper, setShowCropper] = useState(false);
  const [showSelectPreviewBottomSheet, setShowSelectPreviewBottomSheet] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showSelectVideoPreview, setShowSelectVideoPreview] = useState(false);

  const clearFileInput = () => {
    fileInput.current.value = null;
    setSelectedFile(null);
  };
  const clearPreviewInput = () => {
    previewInput.current.value = null;
    setPreview(null);
  };

  const onFileSelect = async event => {
    const file = event.target.files[0];
    setSelectedFile(file);
    setFileType(file.type || 'application/octet-stream');
    setIsPlaying(false);

    if (isImage(file.type)) {
      setToBeCroppedImage(file);
      setShowCropper(true);
    } else if (isVideo(file.type)) {
      setShowSelectVideoPreview(true);
    } else {
      setShowSelectPreviewBottomSheet(true);
    }
  };

  const onPreviewSelect = event => {
    const preview = event.target.files[0];
    setToBeCroppedImage(preview);
    setShowCropper(true);
    setShowSelectPreviewBottomSheet(false);
    setShowSelectVideoPreview(false);
  };

  useEffect(() => {
    fileInput.current.addEventListener('change', onFileSelect);
    previewInput.current.addEventListener('change', onPreviewSelect);
  }, []);

  const onUploadBoxClick = () => {
    if (!disabled) {
      fileInput.current.click();
    }
  };

  const ignoreContent = () => {
    clearFileInput();
    clearPreviewInput();
  };

  const isPlayable = isVideo(fileType || selectedFile?.type)
    || isAudio(fileType || selectedFile?.type);

  const showUploadBox = !(file || selectedFile);
  const showPreview = !showUploadBox && !isPlaying;
  const showPlayer = !showUploadBox && isPlaying;

  return (
    <Box>
      <HiddenInput type="file" ref={fileInput} name="file" />
      <HiddenInput
        type="file"
        accept="image/jpeg, image/png"
        ref={previewInput}
      />

      <Container ratio={ratio}>
        {selectedFile && <IgnoreContent onClick={ignoreContent} />}

        {showUploadBox && (
          <NotUploadedBox title={title} onClick={onUploadBoxClick} />
        )}

        {showPreview && (
          <ContentPreview
            fileType={fileType || selectedFile.type}
            preview={preview}
            rounded
            withPlayIcon={isPlayable}
            onClick={() => {
              previewInput.current.click();
            }}
          />
        )}

        {showPlayer && (
          <MyReactPlayer
            url={file || URL.createObjectURL(selectedFile)}
            isAudio={isAudio(fileType) || isAudio(selectedFile.type)}
          />
        )}
      </Container>

      <FormHelperText error={!!errors}>{errors}</FormHelperText>

      {showCropper && toBeCroppedImage && (
        <ImgCropper
          src={URL.createObjectURL(toBeCroppedImage)}
          hideCropper={() => setShowCropper(false)}
          setCroppedImg={setPreview}
          aspect={ratio}
        />
      )}

      <SelectPreviewBottomSheet
        show={showSelectPreviewBottomSheet}
        hideBottomSheet={() => setShowSelectPreviewBottomSheet(false)}
        onPrimary={() => {
          previewInput.current.click();
          setShowSelectPreviewBottomSheet(false);
        }}
        onSecondary={() => {
          clearFileInput();
          setShowSelectPreviewBottomSheet(false);
        }}
        onOutSideClick={() => clearFileInput()}
      />

      {showSelectVideoPreview && (
        <SelectVideoPreview
          file={selectedFile}
          onPreviewSelect={img => {
            setPreview(img);
            setShowSelectVideoPreview(false);
          }}
          onUploadClick={() => {
            previewInput.current.click();
          }}
        />
      )}
    </Box>
  );
}
