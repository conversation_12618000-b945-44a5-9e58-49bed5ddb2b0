import {
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  IconButton,
  Stack,
  Tooltip,
  Typography,
} from '@mui/material';
import { useState } from 'react';
import { AddCircle, Delete, RemoveCircle } from '@mui/icons-material';
import SelectOrganization from './SelectOrganization';

export default function OrganFilter() {
  // todo: get user organization from server
  const userOrgan = { id: 1, label: 'سازمان مرکزی تام', avatar: '/logo.png' };

  const [selectedType, setSelectedType] = useState('organization');
  const [selectedOrganizations, setSelectedOrganizations] = useState([
    userOrgan,
  ]);
  const [selectedDate, setSelectedDate] = useState({
    from: null,
    to: null,
    index: 1,
  });
  const [withSubOrganizations, setWithSubOrganizations] = useState(true);
  const [organCount, setOrganCount] = useState(1);

  return (
    <Stack
      flex={1}
      direction="column"
      spacing={1}
      sx={{
        mt: 0,
        border: '2px solid #ccc',
        borderRadius: '8px',
        padding: '18px 8px',
      }}
    >
      <Typography sx={{ fontSize: 16, fontWeight: 'bold' }}>
        مشاهده گزارشات مربوط به
      </Typography>

      {/* <FormGroup row sx={{ justifyContent: 'start' }}> */}
      {/*  <FormControlLabel */}
      {/*    control={ */}
      {/*      <Switch */}
      {/*        checked={selectedType === 'organization'} */}
      {/*        onChange={e => */}
      {/*          e.target.checked ? setSelectedType('organization') : null */}
      {/*        } */}
      {/*      /> */}
      {/*    } */}
      {/*    label="سازمان‌ها/سازمان‌ انتخابی" */}
      {/*  /> */}
      {/*  <FormControlLabel */}
      {/*    control={ */}
      {/*      <Switch */}
      {/*        checked={selectedType === 'members'} */}
      {/*        onChange={e => */}
      {/*          e.target.checked ? setSelectedType('members') : null */}
      {/*        } */}
      {/*      /> */}
      {/*    } */}
      {/*    label="اعضا/عضو انتخابی" */}
      {/*  /> */}
      {/* </FormGroup> */}

      {/*<FormGroup row sx={{ justifyContent: 'start' }}>*/}
      {/*  <FormControlLabel*/}
      {/*    control={*/}
      {/*      <Checkbox*/}
      {/*        name="withSubOrganizations"*/}
      {/*        defaultChecked={withSubOrganizations}*/}
      {/*        size="small"*/}
      {/*        onChange={e => {*/}
      {/*          setWithSubOrganizations(e.target.checked);*/}
      {/*          setSelectedOrganizations([selectedOrganizations[0]]);*/}
      {/*          setOrganCount(1);*/}
      {/*        }}*/}
      {/*      />*/}
      {/*    }*/}
      {/*    label="انتخاب به همراه سازمان‌های زیرمجموعه"*/}
      {/*  />*/}
      {/*</FormGroup>*/}

      {selectedOrganizations.map((organization, index) => (
        <Stack
          direction="row"
          spacing={1}
          alignItems="center"
          // key={organization?.id || '00'}
        >
          {index !== organCount && organCount !== 1 && (
            <IconButton
              sx={{ display: 'flex' }}
              onClick={() => {
                const copy = [...selectedOrganizations];
                copy.splice(index, 1);
                setSelectedOrganizations(copy);
                setOrganCount(organCount - 1);
              }}
            >
              <Delete />
            </IconButton>
            // <RemoveCircle
            //   sx={{ color: '#f54646', cursor: 'pointer' }}
            //   onClick={() => {
            //     const copy = [...selectedOrganizations];
            //     copy.splice(index, 1);
            //     setSelectedOrganizations(copy);
            //     setOrganCount(organCount - 1);
            //   }}
            // />
          )}
          <FormControl fullWidth sx={{ display: 'flex', mt: 0 }}>
            <SelectOrganization
              selectedOrganizations={selectedOrganizations}
              setSelectedOrganizations={setSelectedOrganizations}
              index={index}
            />
          </FormControl>
          <FormControlLabel
            control={
              <Checkbox
                name="withSubOrganizations"
                defaultChecked={withSubOrganizations}
                size="small"
                onChange={e => {
                  // setWithSubOrganizations(e.target.checked);
                  // setSelectedOrganizations([selectedOrganizations[0]]);
                  // setOrganCount(1);
                }}
              />
            }
            label="زیرمجموعه‌ها"
          />
        </Stack>
      ))}

      <FormGroup row sx={{ justifyContent: 'end', alignItems: 'center' }}>
        <AddCircle
          sx={{
            color: '#11a6a1',
            cursor: 'pointer',
            fontSize: '40px',
          }}
          onClick={() => {
            if (organCount > 3) return;
            setSelectedOrganizations([...selectedOrganizations, userOrgan]);
            setOrganCount(organCount + 1);
          }}
        />
        <Typography>افزودن سازمان</Typography>
      </FormGroup>
    </Stack>
  );
}
