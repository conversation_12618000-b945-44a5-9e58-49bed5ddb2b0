import { Stack, Typography } from '@mui/material';
import { styled } from '@mui/system';
import logo from '../../static/imgs/logo.png';

const MyImg = styled('img')({
  width: '80%',
  margin: 'auto',
});

export default function LoginLogo() {
  return (
    <Stack>
      <MyImg src={logo} />
      <Typography
        sx={{
          fontSize: '24px',
          fontWeight: 700,
          textAlign: 'center',
          marginTop: '24px',
        }}
      >
        تام
      </Typography>
      <Typography
        variant="h4"
        align="center"
        sx={{
          fontSize: '16px',
          fontWeight: 400,
          color: '#AEAEB2',
          textAlign: 'center',
        }}
      >
        سامانه آرشیو محتوایی
      </Typography>
    </Stack>
  );
}
