import {
  FormControlLabel,
  FormGroup,
  Stack,
  Typography,
  Checkbox,
  Button,
} from '@mui/material';
import { SEARCH_PARAMS } from 'components/SearchBox/searchConfig';
import JalaliDateTimePicker from 'components/JalaliDateTimePicker/JalaliDateTimePicker';

function Title({ title, sx }) {
  return <Typography sx={{ fontSize: '14px', ...sx }}>{title}</Typography>;
}

function DateRangeFilter({ defaultFrom, defaultTo }) {
  return (
    <Stack>
      <Stack direction="row" spacing={1} sx={{ mt: 0 }}>
        <Title title="تاریخ" sx={{ width: '80px', paddingTop: '9px' }} />
        <JalaliDateTimePicker
          label="از"
          inputName={SEARCH_PARAMS.DATE_FROM}
          defaultValue={defaultFrom}
        />
        <JalaliDateTimePicker
          label="تا"
          inputName={SEARCH_PARAMS.DATE_TO}
          defaultValue={defaultTo}
        />
      </Stack>
    </Stack>
  );
}

export default function AdvancedSearch({ searchConfig }) {
  return (
    <Stack spacing={2} sx={{ mt: 1, mb: 1 }}>
      <FormGroup>
        <FormControlLabel
          componentsProps={{ typography: { fontSize: '14px' } }}
          sx={{ justifyContent: 'space-between', m: 0 }}
          control={
            <Checkbox
              defaultChecked={searchConfig.waiting}
              sx={{ pr: 0 }}
              name="waiting"
              id="waiting"
              size="small"
            />
          }
          label="در انتظار تایید"
          labelPlacement="start"
        />
      </FormGroup>
      <FormGroup>
        <FormControlLabel
          componentsProps={{ typography: { fontSize: '14px' } }}
          sx={{ justifyContent: 'space-between', m: 0 }}
          control={
            <Checkbox
              defaultChecked={searchConfig.accepted}
              sx={{ pr: 0 }}
              name="accepted"
              id="accepted"
              size="small"
            />
          }
          label="تایید شده‌ها"
          labelPlacement="start"
        />
      </FormGroup>
      <FormGroup>
        <FormControlLabel
          componentsProps={{ typography: { fontSize: '14px' } }}
          sx={{ justifyContent: 'space-between', m: 0 }}
          control={
            <Checkbox
              defaultChecked={searchConfig.rejected}
              sx={{ pr: 0 }}
              name="rejected"
              id="rejected"
              size="small"
            />
          }
          label="تایید نشده‌ها"
          labelPlacement="start"
        />
      </FormGroup>
      <FormGroup>
        <FormControlLabel
          componentsProps={{ typography: { fontSize: '14px' } }}
          sx={{ justifyContent: 'space-between', m: 0 }}
          control={
            <Checkbox
              defaultChecked={searchConfig.archive}
              sx={{ pr: 0 }}
              name="archive"
              id="archive"
              size="small"
            />
          }
          label="آرشیو"
          labelPlacement="start"
        />
      </FormGroup>

      <DateRangeFilter
        defaultFrom={searchConfig.publish_date_from}
        defaultTo={searchConfig.publish_date_to}
      />

      <Button variant="contained" type="submit">
        اعمال
      </Button>
    </Stack>
  );
}
