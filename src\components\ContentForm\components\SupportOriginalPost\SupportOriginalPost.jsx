import {
  FormControlLabel,
  FormGroup,
  IconButton,
  InputAdornment,
  Stack,
  Switch,
  TextField,
} from '@mui/material';
import ControlPointIcon from '@mui/icons-material/ControlPoint';
import { useState } from 'react';
import ContentPasteOutlinedIcon from '@mui/icons-material/ContentPasteOutlined';

function PasteButton({ setUrl }) {
  const paste = async () => {
    setUrl(await navigator.clipboard.readText());
  };

  return (
    <IconButton onClick={paste}>
      <ContentPasteOutlinedIcon />
    </IconButton>
  );
}

export default function SupportOriginalPost({ sx, addInteractionRequest }) {
  const [url, setUrl] = useState('');
  const [like, setLike] = useState(false);
  const [follow, setFollow] = useState(false);
  const [comment, setComment] = useState(false);

  const onClick = () => {
    if (!url) return;
    addInteractionRequest({
      url,
      follow_request: follow,
      like_request: like,
      comment_request: comment,
    });

    setUrl('');
    setLike(false);
    setFollow(false);
    setComment(false);
  };

  return (
    <Stack sx={sx}>
      <Stack direction="row">
        <TextField
          label="حمایت از پست مرجع"
          variant="outlined"
          fullWidth
          value={url}
          onChange={e => setUrl(e.target.value)}
          InputProps={{
            dir: 'ltr',
            startAdornment: (
              <InputAdornment position="start">
                <PasteButton setUrl={setUrl} />
              </InputAdornment>
            ),
          }}
        />

        <IconButton onClick={onClick}>
          <ControlPointIcon />
        </IconButton>
      </Stack>

      <FormGroup row sx={{ justifyContent: 'space-between' }}>
        <FormControlLabel
          control={
            <Switch checked={like} onChange={e => setLike(e.target.checked)} />
          }
          label="لایک"
        />
        <FormControlLabel
          control={
            <Switch
              checked={comment}
              onChange={e => setComment(e.target.checked)}
            />
          }
          label="کامنت"
        />
        <FormControlLabel
          control={
            <Switch
              checked={follow}
              onChange={e => setFollow(e.target.checked)}
            />
          }
          label="فالو"
        />
      </FormGroup>
    </Stack>
  );
}
