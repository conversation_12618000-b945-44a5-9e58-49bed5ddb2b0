import { CHAT_TYPES } from '../constants';
import myAxios from './myAxios';

export async function createChat(
  participants,
  name,
  avatarId,
  type,
  description,
) {
  return myAxios.post('/messenger/chats/', {
    participants,
    name,
    type,
    description,
    avatar: avatarId,
  });
}

export async function createDirectChat({ participants }) {
  return myAxios.post('/messenger/chats/', {
    participants,
    type: CHAT_TYPES.DIRECT,
  });
}

export async function deleteChat({ chatId }) {
  return myAxios.delete(`/messenger/chats/${chatId}/`);
}
