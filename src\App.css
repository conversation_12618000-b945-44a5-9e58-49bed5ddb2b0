.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.EmojiPickerReact {
  border-radius: 0!important;
  border-left: none!important;
  border-right: none!important;
}

.carousel-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 72px);
  background-color: black;
  overflow: hidden;
}

.post {
  width: 100%;
  text-align: center;
  color: white;
}

.post img {
  max-width: 100%;
  max-height: 80vh;
  object-fit: cover;
}

path.link {
  stroke: #8c8c8c;
  stroke-width: 1;
}

.highcharts-root {
  font-family: IRANSansX, sans-serif !important;
}