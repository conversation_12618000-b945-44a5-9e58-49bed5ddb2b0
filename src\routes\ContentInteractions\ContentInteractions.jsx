import { Stack } from '@mui/material';
import { useLoaderData } from 'react-router-dom';
import { useQuery } from 'react-query';
import { getContentInteractions } from 'apis/content';
import FetchStatus from 'components/FetchStatus/FetchStatus';
import { FAVORITE, LIKE } from 'constants';
import { DEFAULT_APPBAR_HEIGHT } from 'components/CustomAppBar/constants';
import MyAppBar from './components/MyAppBar/MyAppBar';
import InteractionItem from './components/InteractionItem/InteractionItem';

export async function loader({ params }) {
  return { contentId: params.contentId, interactionType: params.interactionType };
}

export default function ContentInteractions() {
  const { contentId, interactionType } = useLoaderData();
  const { isLoading, data } = useQuery(
    ['content-interaction', contentId, interactionType],
    () => getContentInteractions({ contentId, interactionType }),
  );

  let title = '';
  if (interactionType === FAVORITE) {
    title = 'لیست کاربران نشان کرده';
  } else if (interactionType === LIKE) {
    title = 'لیست کاربران پسندیده';
  }

  const interactions = data?.data;
  return (
    <>
      <MyAppBar title={title} />
      <Stack
        sx={{
          mt: DEFAULT_APPBAR_HEIGHT,
          overflowY: 'scroll',
          height: '100%',
        }}
        spacing={1}
      >
        {isLoading
          ? <FetchStatus status="loading" />
          : interactions.map((interaction) => (
            <InteractionItem
              userId={interaction.author.id}
              firstName={interaction.author.first_name}
              lastName={interaction.author.last_name}
              avatar={interaction.author.avatar}
              username={interaction.author.display_username}
            />
          ))}
      </Stack>
    </>
  );
}
