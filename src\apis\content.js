import axios from 'axios';
import myAxios from './myAxios';

export async function createContent(data, onProgress, abortController) {
  return myAxios.post('/content/content/', data, {
    signal: abortController.signal,
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: e => {
      onProgress(Math.floor((e.loaded / e.total) * 100));
    },
  });
}

export async function getContents(searchConfig) {
  return myAxios.get('/content/content/', { params: searchConfig });
}

export async function getContent(id) {
  return myAxios.get(`/content/content/${id}/`);
}

export async function deleteContent(id) {
  return myAxios.delete(`/content/content/${id}/`);
}

export async function updateContent(id, data) {
  return myAxios.patch(`/content/content/${id}/`, data);
}

export function createComment({ comment, content }) {
  return myAxios.post('/content/comment/', { comment, content });
}

export function getComments({ contentId }) {
  const params = {};
  if (contentId) params.content__uuid = contentId;
  return myAxios.get('/content/comment/', { params });
}

export function deleteComment({ commentId }) {
  return myAxios.delete(`/content/comment/${commentId}/`);
}

export function addInteraction({ contentId, interaction }) {
  return myAxios.post('/content/content_interaction/', {
    content: contentId,
    type: interaction,
  });
}

export function deleteInteraction({ contentId, interaction }) {
  const data = {
    content: contentId,
    type: interaction,
  };
  return myAxios.post('/content/content_interaction/delete/', data);
}

export function downloadFile(url, onProgress) {
  return axios.get(url, {
    responseType: 'blob',
    onDownloadProgress: e => {
      onProgress(Math.floor((e.loaded / e.total) * 100));
    },
  });
}

export function getContentAnalytics(id) {
  return myAxios.get(`/content/content/${id}/analytics/`);
}

export function getContentEvaluationReport(id) {
  return myAxios.get(`/content/content/${id}/evaluators/`);
}

export function getContentEvaluationUserResponse(id) {
  return myAxios.get(`/content-evaluation/responses/?content__uuid=${id}`);
}

export function getQuestionList() {
  return myAxios.get('/content-evaluation/questions/');
}

export function createResponse({ content, question, response }) {
  return myAxios.post('/content-evaluation/responses/', {
    content,
    question,
    response,
  });
}

export function getContentInteractions({ contentId, interactionType }) {
  return myAxios.get(
    `/content/content/${contentId}/interactions/?type=${interactionType}`,
  );
}

export function updateContentElection({ contentId, elected }) {
  return myAxios.patch(`/content/content/${contentId}/elect/`, { elected });
}

export function updateContentEvaluation({ contentId, evaluation_requested }) {
  return myAxios.patch(`/content/content/${contentId}/request-evaluation/`, {
    evaluation_requested,
  });
}

export function createInteractionRequest({
  contentId,
  url,
  followRequest,
  likeRequest,
  commentRequest,
}) {
  return myAxios.post(`/content/content/${contentId}/interaction-request/`, {
    url,
    content_id: contentId,
    follow_request: followRequest,
    like_request: likeRequest,
    comment_request: commentRequest,
  });
}

export function deleteInteractionRequest(contentId, requestId) {
  return myAxios.delete(
    `/content/content/${contentId}/interaction-request/${requestId}`,
  );
}

export function createInteractionResponse({
  interactionRequestId,
  interactionType,
  note = '',
}) {
  return myAxios.post('/content/interaction-responses/', {
    interaction_request: interactionRequestId,
    interaction_type: interactionType,
    note,
  });
}

export function updateInteractionResponse({ interactionResponseId, note }) {
  return myAxios.patch(
    `/content/interaction-responses/${interactionResponseId}/`,
    {
      note,
    },
  );
}

export function getContentSupportInfo({ contentId, interactionType }) {
  return myAxios.get(
    `/content/interaction-responses/?interaction_type=${interactionType}&interaction_request__content=${contentId}`,
  );
}

export async function getContentForReels(currentContentId, pageSize = 11) {
  // Fetch content with the current content in the middle
  // This will get approximately 5 before and 5 after the current content
  return myAxios.get('/content/content/', {
    params: {
      page_size: pageSize,
      ordering: '-created_at', // Most recent first
      status: 'A', // Only approved content
    },
  });
}

export async function getContentAroundId(
  currentContentId,
  beforeCount = 5,
  afterCount = 5,
) {
  try {
    // First, get the current content to understand its position
    const currentContent = await getContent(currentContentId);

    // Get content created before the current content (older content)
    const beforeContent = await myAxios.get('/content/content/', {
      params: {
        page_size: beforeCount,
        ordering: '-created_at',
        status: 'A',
        created_at__lt: currentContent.data.created_at.slice(0, 10),
      },
    });

    // Get content created after the current content (newer content)
    const afterContent = await myAxios.get('/content/content/', {
      params: {
        page_size: afterCount,
        ordering: 'created_at', // Ascending to get the next newer content
        status: 'A',
        created_at__gt: currentContent.data.created_at.slice(0, 10),
      },
    });

    // Combine all content: before + current + after
    const allContent = [
      ...beforeContent.data.results.reverse(), // Reverse to maintain chronological order
      currentContent.data,
      ...afterContent.data.results.reverse(), // Reverse to maintain chronological order
    ];

    return {
      data: {
        results: allContent,
        currentIndex: beforeContent.data.results.length, // Index of current content
        count: allContent.length,
      },
    };
  } catch (error) {
    // Fallback: just get recent content if the above fails
    const fallbackContent = await myAxios.get('/content/content/', {
      params: {
        page_size: beforeCount + afterCount + 1,
        ordering: '-created_at',
        status: 'A',
      },
    });

    // Try to find current content in the results
    const currentIndex = fallbackContent.data.results.findIndex(
      content => content.id === currentContentId,
    );

    return {
      data: {
        results: fallbackContent.data.results,
        currentIndex: currentIndex >= 0 ? currentIndex : 0,
        count: fallbackContent.data.results.length,
      },
    };
  }
}
