import { Box, SvgIcon } from '@mui/material';
import { ReactComponent as LoadingIcon } from 'static/icons/loading.svg';

export default function LoadingPage() {
  return (
    <Box
      sx={{
        width: '100%',
        height: '82vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <SvgIcon
        component={LoadingIcon}
        sx={{
          fontSize: '40px',
          color: 'black',
          animation: 'spin 1.5s linear infinite',
          '@keyframes spin': {
            '0%': {
              transform: 'rotate(0deg)',
            },
            '100%': {
              transform: 'rotate(360deg)',
            },
          },
        }}
      />
    </Box>
  );
}
