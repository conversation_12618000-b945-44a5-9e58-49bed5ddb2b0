import { useState } from 'react';
import {
  Autocomplete,
  Checkbox,
  Avatar,
  TextField,
  Chip,
  ListItem,
  ListItemAvatar,
  ListItemText,
  CircularProgress,
} from '@mui/material';
import { CheckBoxOutlineBlank, CheckBox, Cancel } from '@mui/icons-material';

// sample options: [{ id: 1, label: 'Alice', avatar: 'https://randomuser.me/api/portraits/women/1.jpg' }]
export default function MultiSelectDropdown({
  options,
  defaultOptions = [],
  handleSelect,
  placeholder = 'انتخاب کنید',
  loading = false,
  setQuery = value => {},
}) {
  const [selectedOptions, setSelectedOptions] = useState(defaultOptions);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const handleChange = (event, newValue) => {
    // Handle "Select All" logic
    if (newValue.some(option => option.id === 'select-all')) {
      const allSelected = selectedOptions.length === options.length;
      if (allSelected) {
        setSelectedOptions([]);
        handleSelect([]);
      } else {
        setSelectedOptions(options);
        handleSelect(options);
      }
    } else {
      setSelectedOptions(newValue);
      handleSelect(newValue);
    }
  };

  const renderTags = (value, getTagProps) => {
    const displayedTags = value.slice(0, 5);
    return [
      ...displayedTags
        .reverse()
        .map((option, index) => (
          <Chip
            key={option.label}
            avatar={<Avatar src={option.avatar} />}
            label={option.label}
            {...getTagProps({ index })}
            deleteIcon={<Cancel />}
          />
        )),
      value.length > 5 && <Chip key="more" label={`+${value.length - 5}`} />,
    ];
  };

  const enhancedOptions = [
    { id: 'select-all', label: 'انتخاب همه' },
    ...options,
  ];

  return (
    <Autocomplete
      multiple
      options={enhancedOptions}
      disableCloseOnSelect
      getOptionLabel={option => (option.label ? option.label : 'بدون نام')}
      value={selectedOptions}
      onChange={handleChange}
      renderTags={renderTags}
      size="small"
      loading={loading}
      loadingText="در حال بارگذاری..."
      open={!loading && dropdownOpen} // Prevent dropdown when loading
      onOpen={() => {
        if (!loading) setDropdownOpen(true); // Open only if not loading
      }}
      onInputChange={(event, value) => {
        if (value.length >= 2) {
          setQuery(value); // Only set the query if input has at least 2 characters
        } else {
          setQuery(''); // Clear the query for less than 2 characters
        }
      }}
      onClose={() => setDropdownOpen(false)}
      noOptionsText="موردی یافتن نشد"
      isOptionEqualToValue={(option, value) => option.id === value.id}
      renderOption={(props, option, { selected }) => {
        props.key = props.id;
        return (
          <li {...props}>
            <Checkbox
              icon={<CheckBoxOutlineBlank />}
              checkedIcon={<CheckBox />}
              style={{ marginRight: 8 }}
              checked={selected}
            />
            {option.label && (
              <ListItem>
                {option.avatar && (
                  <ListItemAvatar>
                    <Avatar src={option.avatar} />
                  </ListItemAvatar>
                )}
                <ListItemText primary={option.label} />
              </ListItem>
            )}
          </li>
        );
      }}
      renderInput={params => (
        <TextField
          {...params}
          variant="outlined"
          placeholder={placeholder}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {loading ? (
                  <>
                    در حال بارگذاری...
                    <CircularProgress
                      color="inherit"
                      size={20}
                      sx={{ color: '#11a6a1' }}
                    />
                  </>
                ) : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
        />
      )}
    />
  );
}
