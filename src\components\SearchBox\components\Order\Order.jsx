import {
  FormControlLabel, Radio, RadioGroup,
} from '@mui/material';
import { SEARCH_PARAMS } from 'components/SearchBox/searchConfig';
import { ORDERING } from 'constants';

export default function Order({ searchConfig }) {
  return (
    <RadioGroup
      value={searchConfig[SEARCH_PARAMS.ORDERING]}
      sx={{ mt: 2 }}
      name="ordering"
      row
    >
      {ORDERING.map((label) => (
        <FormControlLabel
          xs
          key={label.key}
          control={<Radio inputProps={{ type: 'submit' }} />}
          label={label.value}
          value={label.key}
          sx={{
            mt: 1,
            mb: 1,
          }}
        />
      ))}
    </RadioGroup>
  );
}
