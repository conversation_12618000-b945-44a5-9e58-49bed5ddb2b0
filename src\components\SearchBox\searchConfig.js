import { SEARCH_LOCATIONS } from 'constants';
import dayjs from 'dayjs';
import { jalaliToGregorian } from 'utils';

export const SEARCH_PARAMS = {
  DATE_FROM: 'created_at__gt',
  DATE_TO: 'created_at__lt',
  LIKES_FROM: 'likes_count__gt',
  LIKES_TO: 'likes_count__lt',
  DOWNLOADS_TO: 'downloads_count__lt',
  DOWNLOADS_FROM: 'downloads_count__gt',
  ORDERING: 'ordering',
  TYPE: 'type',
  SEARCH: 'search',
  PAGE_SIZE: 'page_size',
  PAGE: 'page',
  STATUS: 'status',
  SEARCH_FIELDS: 'search_fields',
  VIEW_MODE: 'view_mode',
  ELECTED: 'elected',
  SUPPORT_REQUEST: 'support_request',
  EVALUATE: 'evaluation_requested',
};

function buildSearchConfig(
  dateFrom,
  dateTo,
  likesFrom,
  likesTo,
  downloadsFrom,
  downloadsTo,
  ordering,
  contentType,
  search,
  viewMode,
  elected,
  supportRequest,
  evaluate,
  searchFields,
) {
  return {
    [SEARCH_PARAMS.DATE_FROM]: dateFrom,
    [SEARCH_PARAMS.DATE_TO]: dateTo,
    [SEARCH_PARAMS.LIKES_FROM]: likesFrom,
    [SEARCH_PARAMS.LIKES_TO]: likesTo,
    [SEARCH_PARAMS.DOWNLOADS_FROM]: downloadsFrom,
    [SEARCH_PARAMS.DOWNLOADS_TO]: downloadsTo,
    [SEARCH_PARAMS.ORDERING]: ordering || '-created_at',
    [SEARCH_PARAMS.TYPE]: contentType || '',
    [SEARCH_PARAMS.SEARCH]: search,
    [SEARCH_PARAMS.SEARCH_FIELDS]: searchFields || 'labels,description,title',
    [SEARCH_PARAMS.VIEW_MODE]: viewMode || 'tile',
    [SEARCH_PARAMS.ELECTED]: elected,
    [SEARCH_PARAMS.SUPPORT_REQUEST]: supportRequest,
    [SEARCH_PARAMS.EVALUATE]: evaluate,
  };
}

function removeExtraCharFromDateQueryParam(date) {
  return date.replaceAll(/⁩/g, '').replaceAll(/⁨/g, '').replaceAll(/⁦/g, '');
}

function parseQueryDate(date) {
  return dayjs(removeExtraCharFromDateQueryParam(date), {
    jalali: true,
  });
}

function parseSearchFields(url) {
  const locations = SEARCH_LOCATIONS.filter((location) => {
    const locationValue = url.searchParams.get(location.value);
    return !!locationValue && locationValue === 'on';
  });

  return locations.reduce(
    (accValue, location) => `${accValue},${location.value}`,
    '',
  );
}

export function buildSearchConfigFromURL(url) {
  const fromDateQuery = url.searchParams.get(SEARCH_PARAMS.DATE_FROM);
  const fromDate = fromDateQuery
    ? jalaliToGregorian(parseQueryDate(fromDateQuery))
    : null;

  const toDateQuery = url.searchParams.get(SEARCH_PARAMS.DATE_TO);
  const toDate = toDateQuery
    ? jalaliToGregorian(parseQueryDate(toDateQuery))
    : null;

  return buildSearchConfig(
    fromDate,
    toDate,
    url.searchParams.get(SEARCH_PARAMS.LIKES_FROM),
    url.searchParams.get(SEARCH_PARAMS.LIKES_TO),
    url.searchParams.get(SEARCH_PARAMS.DOWNLOADS_FROM),
    url.searchParams.get(SEARCH_PARAMS.DOWNLOADS_TO),
    url.searchParams.get(SEARCH_PARAMS.ORDERING),
    url.searchParams.get(SEARCH_PARAMS.TYPE),
    url.searchParams.get(SEARCH_PARAMS.SEARCH),
    url.searchParams.get(SEARCH_PARAMS.VIEW_MODE),
    url.searchParams.get(SEARCH_PARAMS.ELECTED),
    url.searchParams.get(SEARCH_PARAMS.SUPPORT_REQUEST),
    url.searchParams.get(SEARCH_PARAMS.EVALUATE),
    parseSearchFields(url),
  );
}
