import { Button, IconButton, Tooltip, tooltipClasses } from '@mui/material';
import ShareOutlinedIcon from '@mui/icons-material/ShareOutlined';
import { createIntentUrl, useIsDesktop } from 'utils';
import { Box, styled } from '@mui/system';
import { useState } from 'react';
import TelegramLogo from '../../../../assets/icons/TelegramLogo.png';
import WhatsappLogo from '../../../../assets/icons/WhatsappLogo.png';
import XTwitter from '../../../../assets/icons/XTwitter.png';
import Bale from '../../../../assets/icons/Bale.png';
import eeta from '../../../../assets/icons/eeta.png';

const ShareTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
  backgroundColor: 'red',
  color: 'black',
  boxShadow: 3,
  padding: 1,
  borderRadius: '6px',
  '& .MuiTooltip-arrow': {
    color: 'white',
  },
  '& .MuiTooltip-tooltip': {
    backgroundColor: 'white',
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: theme.palette.common.black,
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.common.red,
  },
}));

export default function ShareButton({ url, type, description, labels, sx }) {
  const isDesktop = useIsDesktop();

  const intentURL = createIntentUrl('share', 'share', {
    mime_type: type,
    url,
    description: encodeURIComponent(description),
    labels: encodeURIComponent(labels.map(label => `#${label}`).join()),
  });

  const [showTooltip, setShowTooltip] = useState(false);

  const handleToggleTooltip = () => {
    setShowTooltip(!showTooltip);
  };

  const handleTwitterShare = () => {
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
    window.open(twitterUrl, '_blank');
  };

  const handleTelegramShare = () => {
    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
    window.open(telegramUrl, '_blank');
  };

  const handleWhatsAppShare = () => {
    const whatsappUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(`${text} ${url}`)}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleEetaShare = () => {
    const eitaaUrl = `https://eitaa.com/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
    window.open(eitaaUrl, '_blank');
  };

  const handleBaleShare = () => {
    const baleUrl = `https://ble.ir/share/url?url=${encodeURIComponent(url)}`;
    window.open(baleUrl, '_blank');
  };

  return (
    <Button
      sx={{
        height: '40px',
        borderRadius: '6px',
        ...sx,
      }}
      variant="contained"
      startIcon={<ShareOutlinedIcon />}
      disableElevation
      onClick={isDesktop ? handleToggleTooltip : null}
      href={isDesktop ? null : intentURL}
    >
      اشتراک گذاری
      {/*{showTooltip && (*/}
      {/*// <SharePopup isOpen={showTooltip} onClose={() => setShowTooltip(false)} />*/}
      {/*<ShareTooltip*/}
      {/*  title={(*/}
      {/*    <Box sx={{ display: 'flex', gap: 1 }}>*/}
      {/*      <IconButton onClick={handleTelegramShare} aria-label="Share on Telegram">*/}
      {/*        <img src={TelegramLogo} alt="Telegram" style={{ width: 24, height: 24 }} />*/}
      {/*      </IconButton>*/}
      {/*      <IconButton onClick={handleWhatsAppShare} aria-label="Share on WhatsApp">*/}
      {/*        <img src={WhatsappLogo} alt="WhatsApp" style={{ width: 24, height: 24 }} />*/}
      {/*      </IconButton>*/}
      {/*      <IconButton onClick={handleTwitterShare} aria-label="Share on WhatsApp">*/}
      {/*        <img src={XTwitter} alt="Twitter" style={{ width: 24, height: 24 }} />*/}
      {/*      </IconButton>*/}
      {/*      <IconButton onClick={handleEetaShare} aria-label="Share on Eeta">*/}
      {/*        <img src={eeta} alt="Eeta" style={{ width: 24, height: 24 }} />*/}
      {/*      </IconButton>*/}
      {/*      <IconButton onClick={handleBaleShare} aria-label="Share on Bale">*/}
      {/*        <img src={Bale} alt="Bale" style={{ width: 24, height: 24 }} />*/}
      {/*      </IconButton>*/}
      {/*    </Box>*/}
      {/*      )}*/}
      {/*  open*/}
      {/*  placement="top"*/}
      {/*  sx={{*/}
      {/*    backgroundColor: 'transparent',*/}
      {/*    color: 'black',*/}
      {/*    boxShadow: 0,*/}
      {/*    padding: 1,*/}
      {/*    borderRadius: '6px',*/}
      {/*    '& .MuiTooltip-arrow': {*/}
      {/*      color: '#11a6a1',*/}
      {/*    },*/}
      {/*    '& .MuiTooltip-tooltip': {*/}
      {/*      backgroundColor: '#F1F2F9',*/}
      {/*      // border: '1px solid #11a6a1',*/}
      {/*      borderRadius: '7px',*/}
      {/*      boxShadow: 2,*/}
      {/*    },*/}
      {/*  }}*/}
      {/*  PopperProps={{*/}
      {/*    modifiers: [*/}
      {/*      {*/}
      {/*        name: 'offset',*/}
      {/*        options: {*/}
      {/*          offset: [50, 10],*/}
      {/*        },*/}
      {/*      },*/}
      {/*    ],*/}
      {/*  }}*/}
      {/*  onClose={() => setShowTooltip(false)}*/}
      {/*>*/}
      {/*  <Box sx={{ position: 'relative' }} />*/}
      {/*</ShareTooltip>*/}
      {/*)}*/}
    </Button>
  );
}
