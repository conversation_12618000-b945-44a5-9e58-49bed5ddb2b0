import { Box, Stack, Typography } from '@mui/material';
import { CONTENT_STATUS } from 'constants';

function getStatusText(status) {
  if (status === CONTENT_STATUS.WAITING) {
    return 'در انتظار تایید';
  }
  if (status === CONTENT_STATUS.REJECTED) {
    return 'رد شده';
  }
  return '';
}

function getStatusColor(status) {
  if (status === CONTENT_STATUS.WAITING) {
    return '#F6A925';
  }
  if (status === CONTENT_STATUS.REJECTED) {
    return '#E90303';
  }
  return '';
}

function Circle({ status }) {
  const color = getStatusColor(status);
  return (
    <Box sx={{
      backgroundColor: color,
      borderRadius: '50%',
      width: '16px',
      height: '16px',
    }}
    />
  );
}

export default function StatusBadge({ status }) {
  return (
    <Stack
      direction="row"
      sx={{
        background: 'white',
        position: 'absolute',
        bottom: '32px',
        padding: '4px',
        paddingTop: '2px',
        paddingBottom: '2px',
        borderRadius: '16px',
        boxShadow: '0px 0px 8px rgba(44, 44, 46, 0.1)',
      }}
      spacing={0.5}
    >
      <Circle status={status} />
      <Typography sx={{ fontWeight: 400, fontSize: '12px' }}>
        {getStatusText(status)}
      </Typography>
    </Stack>
  );
}
