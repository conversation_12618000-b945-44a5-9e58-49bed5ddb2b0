import { Stack } from '@mui/material';
import FetchStatus from 'components/FetchStatus/FetchStatus';
import { useQuery } from 'react-query';
import { getContacts } from 'apis/contacts';
import { useState } from 'react';
import Contacts from 'components/Contacts/Contacts';
import { useSearchParams } from 'react-router-dom';
import MyAppBar from './components/MyAppBar/MyAppBar';

const APPBAR_HEIGHT = '128px';

export default function CreateChatSelectContacts() {
  const { isLoading, data, isError } = useQuery('contacts', getContacts);
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [searchParams] = useSearchParams();
  const [searchValue, setSearchValue] = useState('');

  const handleSearchChange = event => {
    setSearchValue(event.target.value);
  };

  const onContactClick = contact => {
    setSelectedContacts(prevSelectedContacts => {
      const prevLen = prevSelectedContacts.length;
      const removedSelectedContact = prevSelectedContacts.filter(
        c => c.id !== contact.id,
      );
      const newLen = removedSelectedContact.length;

      // Contact didn't exist
      if (prevLen === newLen) {
        return [...prevSelectedContacts, contact];
      }

      // Contact existed and removed
      return removedSelectedContact;
    });
  };

  return (
    <>
      <MyAppBar
        selectedContacts={selectedContacts}
        chatType={searchParams.get('chatType')}
        onSearchChange={handleSearchChange}
      />
      <Stack
        sx={{
          paddingTop: APPBAR_HEIGHT,
          pb: 2,
          overflowY: 'scroll',
          height: '100%',
        }}
      >
        {isLoading && <FetchStatus status="loading" />}
        {isError && <FetchStatus status="error" />}
        {!(isLoading || isError) && (
          <Contacts
            contacts={data.data?.results || []}
            selectedContacts={selectedContacts}
            selectable
            onContactClick={onContactClick}
            searchValue={searchValue}
          />
        )}
      </Stack>
    </>
  );
}
