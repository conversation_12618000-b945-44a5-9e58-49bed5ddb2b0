import { Stack } from '@mui/material';
import { deleteComment } from 'apis/content';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import { useQueryClient } from 'react-query';
import { useDispatch } from 'react-redux';
import { setSnackbar } from 'store/layout';

export default function DeleteCommentBottomSheet({
  show,
  hideBottomSheet,
  commentId,
  contentId,
}) {
  const dispatch = useDispatch();

  const queryClient = useQueryClient();
  const onConfirm = () => {
    deleteComment({ commentId }).then(() => {
      dispatch(setSnackbar({ message: 'نظر با موفقیت حذف شد.', severity: 'success' }));
      hideBottomSheet();
      queryClient.invalidateQueries({ queryKey: ['comments', contentId] });
    });
  };

  return (
    <BottomSheet
      title="حذف نظر"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        آیا تمایل به حذف نظر خود دارید؟
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={onConfirm}
        >
          بله
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={hideBottomSheet}>
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
