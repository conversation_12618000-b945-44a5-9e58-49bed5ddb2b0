importScripts('https://www.gstatic.com/firebasejs/9.2.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.2.0/firebase-messaging-compat.js');

firebase.initializeApp({
  apiKey: 'AIzaSyBPRRhvxvH--tIt0ZsDlEJuyIaCh6q-WT8',
  authDomain: 'haghighatgram-8901e.firebaseapp.com',
  projectId: 'haghighatgram-8901e',
  storageBucket: 'haghighatgram-8901e.appspot.com',
  messagingSenderId: '514478643516',
  appId: '1:514478643516:web:26289494ff45b3b1372639',
  measurementId: 'G-25RB2EV635',
});

const messaging = firebase.messaging();

self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  event.waitUntil(
    clients.matchAll({
      type: 'window',
    })
      .then((clientList) => {
        if (clientList.length) {
          clientList[0].focus();
        }
      }),
  );
});
