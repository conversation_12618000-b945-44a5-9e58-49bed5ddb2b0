import {
  Box, Grid, Tab, Tabs, Typography,
} from '@mui/material';
import { useIsDesktop } from 'utils';
import { useState } from 'react';
import { useLoaderData, useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { PATHS } from '../../../constants';
import { getAnnouncement } from '../../../apis/announcement';
import ChartSection from './components/ChartSection/ChartSection';
import SampleAnnounceSection from './components/SampleAnnounceSection/SampleAnnounceSection';
import ViewersSection from './components/ViewersSection/ViewersSection';

export async function loader({ params }) {
  return params.groupMsgId;
}

export default function GroupMsgView() {
  const isDesktop = useIsDesktop();

  const navigate = useNavigate();

  const announcementId = useLoaderData();
  const query = useQuery([PATHS.admin.announcementView, announcementId], () => getAnnouncement(announcementId));

  const [selectedTab, setSelectedTab] = useState('tab1');
  const tabs = [
    {
      value: 'tab1',
      label: 'روند بازدید',
    },
    {
      value: 'tab2',
      label: 'لیست بازدید',
    },
    {
      value: 'tab3',
      label: 'نمونه اعلان',
    },
  ];

  return (
    <Grid container xs={12} sx={{ height: '100%', overflowY: 'scroll' }}>

      {isDesktop ? (
        <>
          <ChartSection />

          <Grid container>
            <ViewersSection data={[]} />
            <SampleAnnounceSection announce={{}} />
          </Grid>
        </>
      ) : (
        <>
          <Box sx={{ width: '100%', marginBottom: '20px' }}>
            <Tabs
              variant="fullWidth"
              scrollButtons="auto"
              value={selectedTab}
              onChange={(e, newValue) => setSelectedTab(newValue)}
            >
              {tabs.map((tab) => (
                <Tab
                  key={tab.value}
                  label={<Typography sx={{ fontSize: '14px' }} noWrap>{tab.label}</Typography>}
                  value={tab.value}

                />
              ))}
            </Tabs>
          </Box>

          {selectedTab === 'tab1' && <ChartSection />}
          {selectedTab === 'tab2' && <ViewersSection data={[]} />}
          {selectedTab === 'tab3' && <SampleAnnounceSection announce={{}} />}
        </>
      )}

    </Grid>
  );
}
