import {
  Box, Stack, SvgIcon, Typography, useMediaQuery, useTheme,
} from '@mui/material';
import MyLink from 'components/MyLink/MyLink';
import { CONTENT_TYPES, PATHS } from 'constants';
import { useRef } from 'react';
import RightArrowButton from 'components/RightArrowButton/RightArrowButton';
import LeftArrowButton from 'components/LeftArrowButton/LeftArrowButton';
import IconBg from '../IconBg/IconBg';

function getViewWidth() {
  /**
   * Get width of view
   */

  const theme = useTheme();
  const ARROW_BUTTONS_WIDTH = 64;
  return Math.min(theme.breakpoints.values.sm, window.innerWidth) - ARROW_BUTTONS_WIDTH;
}

function calcItemSize() {
  return getViewWidth() * 0.2;
}

function calcSpacing() {
  /**
     * A little wierd!!
     *
     * We want to adjust spacing to have 4 items in the screen.
     */
  const theme = useTheme();
  const viewWidth = getViewWidth();
  // const ITEM_SIZE = window.screen.width * 0.2;

  const isSmUp = useMediaQuery(theme.breakpoints.up('sm'));
  const PAGE_PADDING = isSmUp ? 48 : 32;

  const SPACE_IN_PIXEL = 8;
  const SPACE_NUM = 3;
  return (
    (viewWidth - PAGE_PADDING - 4 * calcItemSize())
    / (SPACE_NUM * SPACE_IN_PIXEL)
  );
}

function calcScrollSize() {
  return calcItemSize() + calcSpacing() * 8;
}

function TypeItem({
  type, icon, color, value,
}) {
  const link = `${PATHS.search}?type=${value}`;
  return (
    <MyLink to={link}>
      <Stack>
        <IconBg size={calcItemSize()}>
          <SvgIcon
            component={icon}
            sx={{
              fill: 'none',
              width: '50%',
              height: '50%',
              color,
            }}
          />
        </IconBg>

        <Typography
          noWrap
          color="white"
          align="center"
          sx={{ width: calcItemSize(), fontSize: '14px' }}
        >
          {type}
        </Typography>
      </Stack>
    </MyLink>
  );
}

export default function MobileContentTypeMenu({ sx }) {
  const scrollContainer = useRef(null);

  return (
    <Box sx={{ ...sx, width: '100%' }}>
      <Stack direction="row">
        <RightArrowButton scrollContainer={scrollContainer} scrollSize={calcScrollSize()} />

        <Stack
          direction="row"
          sx={{ overflowX: 'scroll' }}
          spacing={calcSpacing()}
          ref={scrollContainer}
        >
          {CONTENT_TYPES.map((type) => (
            <TypeItem
              key={type.name}
              type={type.name}
              icon={type.icon}
              color={type.color}
              value={type.value}
            />
          ))}
        </Stack>

        <LeftArrowButton scrollContainer={scrollContainer} scrollSize={calcScrollSize()} />
      </Stack>
    </Box>
  );
}
