import { Box, Grid, Typography } from '@mui/material';
import { useIsDesktop } from '../../../../../utils';

function SampleAnnounceSection({ announce }) {
  const isDesktop = useIsDesktop();

  return (
    <Grid sx={{
      py: 2,
      px: 3,
      mb: 3,
      ml: '10px',
      borderRadius: '8px',
      background: 'white',
      boxShadow: '0px 2px 20px 0px #00000012',
      width: isDesktop ? 'calc(50% - 5px)' : '100%',
    }}
    >

      <Box display="flex" width="100%" justifyContent="space-between" mb={5}>
        <Typography variant="h6" fontWeight="bold" fontSize={16}>نمونه پیام</Typography>
      </Box>
      <Box display="flex flex-row" width="100%">
        <Box style={{ borderRadius: '6px' }}><img src="#" alt="test" style={{ width: '100%' }} /></Box>
        <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: 'bold' }}>عنوان اعلان که می‌تواند به اندازه کافی طولانی باشد و در دو خط برای مثال جا بگیرد</Typography>
        <Typography sx={{ fontSize: '14px', color: '#A0A0A0', mb: 2 }}>۱۴۰۲/۱۰/۲۲ ۱۲:۴۹</Typography>
        <Typography sx={{ fontSize: '14px' }}>متن اعلانات که در مورد یک موضوعی یک محتوایی در آن نوشته شده است. این محتوا می‌تواند بسیار کوتاه باشد می‌تواند بسیار بلند باشد. با زدن بر روی دکمه نمایش بیش‌تر می‌توان بقیه مطلب را مطالعه کرد. متن اعلانات که در مورد یک موضوعی یک محتوایی در آن نوشته شده است. این محتوا می‌تواند بسیار کوتاه باشد می‌تواند بسیار بلند باشد. با زدن بر روی دکمه نمایش بیش‌تر می‌توان بقیه مطلب را مطالعه کرد</Typography>
      </Box>
    </Grid>
  );
}

export default SampleAnnounceSection;
