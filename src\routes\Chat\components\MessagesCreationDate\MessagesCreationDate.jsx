import { Stack, Typography } from '@mui/material';
import dayjs from 'dayjs';

export default function MessagesCreationDate({ createdAt }) {
  return (
    <Stack alignItems="center">
      <Typography
        sx={(theme) => ({
          pr: 2,
          pl: 2,
          pt: 0.5,
          pb: 0.5,

          background: theme.palette.secondary.main,
          borderRadius: theme.shape.borderRadius,
          color: theme.palette.secondary.dark,
          fontSize: '12px',
        })}
      >
        {dayjs(createdAt).format('YYYY/MM/DD')}
      </Typography>
    </Stack>
  );
}
