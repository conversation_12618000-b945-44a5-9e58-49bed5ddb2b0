import { Stack } from '@mui/material';
import TextProgress from 'components/TextProgress/TextProgress';

const StatusText = {
  loading: 'در حال بارگذاری ...',
  error: 'خطا در بارگذاری.',
};

export default function FetchStatus({ status }) {
  return (
    <Stack
      justifyContent="center"
      alignItems="center"
      sx={{ height: '100%' }}
    >
      <TextProgress status={StatusText[status]} />
    </Stack>
  );
}
