import CloseIcon from '@mui/icons-material/Close';
import {
  Box, IconButton, Stack, Typography, styled,
} from '@mui/material';

const Container = styled(Stack)({
  borderTopLeftRadius: '16px',
  borderTopRightRadius: '16px',
  padding: '8px',
  width: '100%',
});

const VerticalLine = styled(Box)({
  height: '40px',
  width: '3px',
  backgroundColor: '#3390ec',
  borderRadius: '2px',
});

const Title = styled(Typography)({ color: '#3390ec', fontSize: '12px' });

const MessageBrief = styled(Typography)({ fontSize: '12px' });

export default function MessagePreview({
  title,
  message,
  withClose,
  onClose,
  bgColor,
  sx,
}) {
  return (
    <Container
      direction="row"
      justifyContent="space-between"
      alignItems="stretch"
      spacing={1}
      sx={{ backgroundColor: bgColor, ...sx }}
    >
      <VerticalLine />

      <Stack direction="column" flexGrow sx={{ flexGrow: 1, overflow: 'hidden' }}>
        <Title>{title}</Title>
        <MessageBrief noWrap>{message.text}</MessageBrief>
      </Stack>

      {withClose && (
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      )}
    </Container>
  );
}
