import { Button, Grid, Paper, Typography } from '@mui/material';
import { Box } from '@mui/system';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useEffect, useState } from 'react';
import ButtonGroup from '@mui/material/ButtonGroup';
import CardTitle from './CardTitle';

export default function CustomBarChart() {
  const [chartOptions, setChartOptions] = useState({});

  useEffect(() => {
    setChartOptions({
      chart: {
        type: 'column',
        height: '300px',
        backgroundColor: '#fff',
        style: {
          borderRadius: '8px',
        },
      },
      title: {
        text: '',
      },
      legend: {
        enabled: false,
      },
      credits: {
        enabled: false,
      },
      xAxis: {
        categories: ['A', 'B', 'C', 'D'],
        // labels: {
        //   formatter: function () {
        //     return toPersianNumber(this.value.toString());
        //   },
        // },
      },
      yAxis: {
        min: 0,
        title: {
          text: null,
        },
        labels: {
          // formatter: function () {
          //   return toPersianNumber(formatShortNumber(this.value));
          // },
        },
      },
      tooltip: {
        enabled: true,
        shared: true,
        useHTML: true,
        // formatter: function () {
        //   let tooltipHTML = `<div style="font-family:'iranyekan',serif;font-size: 11px; direction: rtl">`;
        //   this.points.forEach(point => {
        //     tooltipHTML += `
        //       <div style="display: flex; align-items: center; margin-bottom: 5px;">
        //         <div style="width: 10px; height: 10px; border-radius: 50%; background-color: ${
        //           point.color
        //         }; margin-left: 3px;"></div>
        //         <span>${shortner(point.series.name)}:</span>
        //         <span style="color: #333; margin-right: 5px;">${toPersianNumber(formatShortNumber(point.y))}</span>
        //       </div>`;
        //   });
        //   tooltipHTML += '</div>';
        //   return tooltipHTML;
        // },
      },
      plotOptions: {
        column: {
          borderWidth: 0,
        },
        series: {
          enableMouseTracking: true,
          borderRadius: '50%',
        },
      },
      series: [
        { name: 'مرد', data: [12, 15, 17, 27, 2, 45, 12], color: '#1C60B0' },
        { name: 'پرونده', data: [6, 33, 45, 12, 34, 12, 18], color: '#38CAB3' },
        { name: 'زن', data: [23, 34, 11, 11, 23, 17, 19], color: '#E052B8' },
      ],
    });
  }, []);
  return (
    <Paper
      display="flex"
      justifyContent="center"
      alignItems="center"
      textAlign="center"
      flexDirection="column"
      elevation={2}
      sx={{ flexGrow: 1, width: '100%', padding: '20px' }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <CardTitle title="عنوان نمونه" />
        <ButtonGroup variant="contained" aria-label="Basic button group">
          <Button size="small">روزانه</Button>
          <Button size="small">هفتگی</Button>
          <Button size="small">ماهانه</Button>
        </ButtonGroup>
      </Box>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} key={1} />
    </Paper>
  );
}
