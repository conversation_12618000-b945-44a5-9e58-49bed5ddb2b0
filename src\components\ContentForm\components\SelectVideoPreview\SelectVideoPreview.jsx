import {
  Button,
  CircularProgress,
  Grid,
  Stack,
  Typography,
  styled,
} from '@mui/material';
import { useCallback, useEffect, useState } from 'react';
import { CONTENT_ASPECT_RATIO, CONTENT_DIMENSION } from 'constants';
import <PERSON>ropper from 'react-easy-crop';
import {
  canvasToBlob,
  cropImage,
  resizeCanvas,
} from 'components/ImgCropper/canavsUtils';
import { extractTenFrames } from './video';

const MyImg = styled('img')({
  width: '100%',
  height: '100%',

  overflow: 'hidden',
  objectFit: 'cover',

  border: '1px solid white',
});

const Container = styled(Grid)({
  position: 'fixed',
  top: 0,
  bottom: 0,
  left: 0,
  right: 0,
  backgroundColor: 'white',
  zIndex: 10000,
  padding: '16px',
});

export default function SelectVideoPreview({
  file,
  onPreviewSelect,
  onUploadClick,
}) {
  const [images, setImages] = useState([]);
  const [selectedImage, setSelectedImage] = useState(null);

  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);

  const [loading, setLoading] = useState(true);

  const onCropComplete = useCallback(
    (croppedArea, _croppedAreaPixels) =>
      setCroppedAreaPixels(_croppedAreaPixels),
    [],
  );

  const onConfirm = useCallback(async () => {
    const croppedCanvas = await cropImage(selectedImage, croppedAreaPixels);

    const resizedCanvas = resizeCanvas(croppedCanvas, CONTENT_DIMENSION);

    const resizedImage = await canvasToBlob(resizedCanvas);

    onPreviewSelect(resizedImage);
  }, [croppedAreaPixels]);

  useEffect(() => {
    extractTenFrames(file).then(frames => {
      setImages(frames.map(img => URL.createObjectURL(img)));
      setLoading(false);
    });
  }, [file]);

  if (loading) {
    return (
      <Container container justifyContent="center" alignItems="center">
        <CircularProgress />
      </Container>
    );
  }

  return (
    <Container container>
      <Grid
        item
        xs={12}
        lg={6}
        sx={{ position: 'relative', height: '60%', width: '100%' }}
      >
        {selectedImage && (
          <Cropper
            image={selectedImage}
            crop={crop}
            zoom={zoom}
            onCropChange={setCrop}
            onZoomChange={setZoom}
            cropShape="rect"
            aspect={CONTENT_ASPECT_RATIO}
            onCropComplete={onCropComplete}
          />
        )}
      </Grid>

      <Stack
        direction="column"
        sx={{ width: '100%' }}
        justifyContent="space-between"
      >
        <Stack xs={12} direction="column">
          <Stack direction="row" sx={{ height: '100px', width: '100%' }}>
            {images.map(image => (
              <MyImg src={image} onClick={() => setSelectedImage(image)} />
            ))}
          </Stack>

          <Typography align="center" xs={12} mt={1}>
            یا
          </Typography>
          <Button onClick={onUploadClick}>آپلود عکس</Button>
        </Stack>

        <Button variant="contained" onClick={onConfirm}>
          ثبت
        </Button>
      </Stack>
    </Container>
  );
}
