import { AppB<PERSON>, Box, Stack, SvgIcon, Tooltip } from '@mui/material';
import HomeOutlinedIcon from '@mui/icons-material/HomeOutlined';
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined';
import GppMaybeOutlinedIcon from '@mui/icons-material/GppMaybeOutlined';
import QuestionAnswerOutlinedIcon from '@mui/icons-material/QuestionAnswerOutlined';
import AccountBoxOutlinedIcon from '@mui/icons-material/AccountBoxOutlined';
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';
import { useLocation } from 'react-router-dom';
import { PATHS } from 'constants';
import { useSelector } from 'react-redux';
import { selectMe } from 'store/auth';
import { setPathParam } from 'utils';
import { Circle } from '@mui/icons-material';
import { useQuery } from 'react-query';
import { ReactComponent as CrownIcon } from 'static/icons/crown.svg';
import Logo from './components/Logo/Logo';
import MyButton from './components/MyButton/MyButton';
import IconSax from '../IconSax/IconSax';
import MyLink from '../MyLink/MyLink';
import { getMyAnnouncementsUnreadCount } from '../../apis/announcement';
import { ReactComponent as Bell } from 'static/icons/Bell.svg';

export const DESKTOP_APPBAR_HEIGHT = 7;

export default function DesktopAppBar() {
  const location = useLocation();

  const isHome = location.pathname === '/';
  const isSearch = location.pathname === PATHS.search;
  const isSettings = location.pathname === PATHS.settings;
  const isProfile = location.pathname.startsWith('/profile');
  const isTerms = location.pathname === PATHS.terms;
  const isChats = location.pathname === PATHS.chats;

  const me = useSelector(selectMe);
  const myName = `${me?.first_name} ${me?.last_name}`;
  const profileLink = setPathParam(PATHS.profile, 'userId', me?.id);

  const { isLoading, data } = useQuery(['unreadAnnounce'], () =>
    getMyAnnouncementsUnreadCount(),
  );
  const unread = isLoading ? 0 : data?.data?.unread_number || 0;

  return (
    <AppBar sx={{ background: '#FFFFFF' }}>
      <Box
        sx={{
          width: '100%',
          margin: 'auto',
          p: 1,
        }}
      >
        <Stack
          direction="row"
          sx={{
            margin: 'auto',
            width: '100%',
          }}
        >
          <Logo />

          <Stack direction="row" spacing={2} ml={3}>
            <MyButton
              text="خانه"
              icon={<HomeOutlinedIcon />}
              active={isHome}
              link="/"
            />
            <MyButton
              text="کاوش و جست‌وجو"
              icon={<SearchOutlinedIcon />}
              active={isSearch}
              link={PATHS.search}
            />
            <MyButton
              text="پیام‌رسان"
              icon={<QuestionAnswerOutlinedIcon />}
              active={isChats}
              link={PATHS.chats}
            />
            <MyButton
              text="مقررات و آموزش بهره‌برداری"
              icon={<GppMaybeOutlinedIcon />}
              active={isTerms}
              link={PATHS.terms}
            />
          </Stack>

          <Stack
            direction="row"
            spacing={2}
            sx={{ flexGrow: 1 }}
            justifyContent="end"
            alignItems="center"
          >
            {me && (
              <>
                {!!me.can_see_admin_panel && (
                  <MyLink to={PATHS.admin.homepage}>
                    <Stack
                      direction="row"
                      alignItems="center"
                      sx={{ position: 'relative' }}
                    >
                      <Tooltip title="پنل مدیریت" arrow>
                        <SvgIcon
                          component={CrownIcon}
                          sx={{
                            width: '40px',
                            height: '40px',
                            padding: '8px',
                            color: 'red',
                          }}
                        />
                      </Tooltip>
                    </Stack>
                  </MyLink>
                )}

                <MyLink to={PATHS.announcement}>
                  <Stack
                    direction="row"
                    alignItems="center"
                    sx={{ position: 'relative' }}
                  >
                    <Tooltip title="اعلانات" arrow>
                      <SvgIcon
                        component={Bell}
                        sx={{ fill: 'none', color: '#8C8C9F' }}
                      />
                    </Tooltip>
                    {unread > 0 && (
                      <Circle
                        sx={{
                          position: 'absolute',
                          left: 0,
                          color: '#E99E0C',
                          width: '10px',
                          top: 0,
                        }}
                      />
                    )}
                  </Stack>
                </MyLink>

                <MyButton
                  text={myName}
                  icon={<AccountBoxOutlinedIcon />}
                  active={isProfile}
                  link={profileLink}
                />
              </>
            )}

            <MyButton
              text="تنظیمات"
              icon={<SettingsOutlinedIcon />}
              active={isSettings}
              link={PATHS.settings}
            />
          </Stack>
        </Stack>
      </Box>
    </AppBar>
  );
}
