import {
  alpha,
  Avatar,
  Box,
  CircularProgress,
  Stack,
  styled,
} from '@mui/material';
import {
  useCallback, useEffect, useRef, useState,
} from 'react';
import { useMutation } from 'react-query';
import ImgCropper from '../../../../components/ImgCropper/ImgCropper';
import { updateUserProfile } from '../../../../apis/auth';

const SIZE = 128;

const HiddenInput = styled('input')(() => ({
  display: 'none',
}));

function Loading() {
  return (
    <Stack
      sx={(theme) => ({
        width: SIZE,
        height: SIZE,
        background: alpha(theme.palette.grey[400], 0.4),
        borderRadius: '50%',
        position: 'absolute',
        top: 0,
      })}
      justifyContent="center"
      alignItems="center"
    >
      <CircularProgress color="secondary" />
    </Stack>
  );
}

export default function EditableAvatar({ avatar, userId }) {
  const inputRef = useRef(null);
  const [selectedImg, setSelectedImg] = useState(null);
  const [croppedImg, setCroppedImg] = useState(null);
  const [showCropper, setShowCropper] = useState(false);
  const [uploading, setUploading] = useState(false);

  const mutation = useMutation(
    (avatarImg) => {
      const formData = new FormData();
      formData.append('avatar', avatarImg, selectedImg.name);

      return updateUserProfile(userId, formData, true);
    },
    {
      onMutate: () => setUploading(true),
      onSettled: () => setUploading(false),
      onError: () => setCroppedImg(null),
    },
  );

  useEffect(() => {
    inputRef.current.addEventListener('change', (event) => {
      setSelectedImg(event.target.files[0]);
    });
  }, []);

  useEffect(() => {
    if (selectedImg) {
      setShowCropper(true);
    }
  }, [selectedImg]);

  useEffect(() => {
    if (croppedImg) {
      mutation.mutate(croppedImg);
    }
  }, [croppedImg]);

  const onAvatarClick = useCallback(() => {
    if (!uploading) {
      inputRef.current.click();
    }
  }, [uploading]);

  const hideCropper = useCallback(() => setShowCropper(false), []);

  const croppedImgURL = croppedImg ? URL.createObjectURL(croppedImg) : null;

  return (
    <Box sx={{ position: 'relative' }}>
      <HiddenInput ref={inputRef} type="file" accept="image/*" />
      <Avatar
        sx={(theme) => ({
          width: SIZE,
          height: SIZE,
          fontSize: 48,
          bgcolor: theme.palette.primary.dark,
        })}
        src={croppedImgURL || avatar}
        onClick={onAvatarClick}
      />
      {showCropper && (
        <ImgCropper
          src={URL.createObjectURL(selectedImg)}
          hideCropper={hideCropper}
          setCroppedImg={setCroppedImg}
          cropShape="round"
        />
      )}

      {uploading && <Loading />}
    </Box>
  );
}
