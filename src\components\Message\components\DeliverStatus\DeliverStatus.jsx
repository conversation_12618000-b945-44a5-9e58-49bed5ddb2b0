import { CircularProgress, Stack } from '@mui/material';
import IconSax from 'components/IconSax/IconSax';
import { MESSAGE_DELIVER_STATUS } from 'constants';

function Loading() {
  return <CircularProgress sx={{ width: '8px', height: '8px' }} size={12} />;
}

function Tick({ color = 'secondary' }) {
  return (
    <IconSax
      name="tick"
      sx={{ width: '8px', height: '6px' }}
      viewBox="0 0 8 6"
      color={color}
    />
  );
}

function Sent(sx) {
  return <Stack direction="row" sx={sx} alignItems="center"><Tick /></Stack>;
}

function Received(sx) {
  return (
    <Stack direction="row" sx={sx} alignItems="center">
      <Tick />
      <Tick />
    </Stack>
  );
}

function Read(sx) {
  return (
    <Stack direction="row" sx={sx} alignItems="center">
      <Tick color="primary" />
      <Tick color="primary" />
    </Stack>
  );
}

export default function DeliverStatus({ status, sx }) {
  return (
    <>
      {status === MESSAGE_DELIVER_STATUS.SENDING && <Loading />}
      {status === MESSAGE_DELIVER_STATUS.SENT && <Sent />}
      {status === MESSAGE_DELIVER_STATUS.RECEIVED && <Received sx={sx} />}
      {status === MESSAGE_DELIVER_STATUS.READ && <Read sx={sx} />}
    </>

  );
}
