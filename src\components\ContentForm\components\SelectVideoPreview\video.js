export function loadVideo(file) {
  return new Promise((resolve, reject) => {
    try {
      const video = document.createElement('video');
      video.preload = 'metadata';

      video.onloadedmetadata = () => {
        resolve(video);
      };

      video.onerror = e => {
        reject(e);
      };

      video.src = window.URL.createObjectURL(file);
    } catch (e) {
      reject(e);
    }
  });
}

/* eslint-disable prefer-promise-reject-errors */
export function extractFrameAt(video, frameAt = 0.0) {
  return new Promise(resolve => {
    video.currentTime = frameAt;
    video.addEventListener('seeked', () => {
      // define a canvas to have the same dimension as the video
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      // draw the video frame to canvas
      const ctx = canvas.getContext('2d');
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      // return the canvas image as a blob
      ctx.canvas.toBlob(
        blob => {
          resolve(blob);
        },
        'image/jpeg',
        0.75 /* quality */,
      );
    });
  });
}

export async function extractNoneEmptyFrameAt(video, frameAt, step = 0.1) {
  let currentFrameAt = frameAt;
  let frame = null;
  while (!frame && currentFrameAt < video.duration) {
    // eslint-disable-next-line no-await-in-loop
    frame = await extractFrameAt(video, currentFrameAt);
    if (frame.size > 0) {
      return frame;
    }
    currentFrameAt += step;
  }

  return frame;
}

export async function extractTenFrames(file) {
  const MAX_FRAME_NUM = 7;
  const MAX_FRAME_SPAN = 10;

  const video = await loadVideo(file);
  const frameSpan = Math.min(MAX_FRAME_SPAN, video.duration);
  const frameStepLength = Math.floor(frameSpan / MAX_FRAME_NUM);

  let frameAt = 0;
  const frames = [];
  while (frameAt < video.duration && frames.length < MAX_FRAME_NUM) {
    // eslint-disable-next-line no-await-in-loop
    frames.push(await extractNoneEmptyFrameAt(video, frameAt));
    frameAt += frameStepLength;
  }

  return frames;
}
