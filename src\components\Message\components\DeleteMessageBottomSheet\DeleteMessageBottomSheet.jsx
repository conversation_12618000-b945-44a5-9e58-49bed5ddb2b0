import { Stack } from '@mui/material';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import useSendDeleteMessage from 'hooks/useSendDeleteMessage';

export default function DeleteMessageBottomSheet({
  show,
  hideBottomSheet,
  messageDeliveryToken,
}) {
  const sendDeleteMessage = useSendDeleteMessage();
  const deleteMessage = () => {
    sendDeleteMessage(messageDeliveryToken);
  };

  return (
    <BottomSheet
      title="حذف پیام"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        آیا می‌خواهید پیام را حذف کنید؟
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            deleteMessage();
            hideBottomSheet();
          }}
        >
          بله
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={hideBottomSheet}>
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
