import { IconButton } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';

export default function LeftArrowButton({ scrollContainer, scrollSize }) {
  const scroll = () => {
    /* eslint  no-param-reassign: "error" */
    scrollContainer.current.scrollLeft -= scrollSize;
  };
  return (
    <IconButton
      sx={{
        color: 'white', m: 0, pl: 1, pr: 0,
      }}
      edge="end"
      onClick={scroll}
    >
      <ChevronLeftIcon />
    </IconButton>
  );
}
