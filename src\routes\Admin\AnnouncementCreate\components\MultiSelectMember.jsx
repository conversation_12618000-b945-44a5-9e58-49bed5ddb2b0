import { useState } from 'react';
import { useQuery } from 'react-query';
import { getOrganizationUsers } from '../../../../apis/organization';
import MultiSelectDropdown from '../../../../components/UI/MultiSelectDrowDown';

function MultiSelectMembers({ selectedMembers = [], setSelectedMembers }) {
  const [options, setOptions] = useState([]);

  const transformData = data => {
    return data.map(item => ({
      id: item.id,
      label:
        item.first_name === '' && item.last_name === ''
          ? item.display_username === ''
            ? 'بدون نام'
            : item.display_username
          : `${item.first_name} ${item.last_name}`,
      avatar: item.avatar || '/logo.png',
    }));
  };

  const { isLoading } = useQuery({
    queryKey: ['getOrganizationUsers'],
    queryFn: () => getOrganizationUsers(),
    onSuccess: data => {
      if (data.status === 200) {
        setOptions(transformData(data.data));
      }
    },
  });

  return (
    <MultiSelectDropdown
      defaultOptions={selectedMembers}
      handleSelect={setSelectedMembers}
      options={options}
      placeholder="انتخاب عضو/اعضا"
      loading={isLoading}
    />
  );
}

export default MultiSelectMembers;
