import { Alert, Snackbar } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { selectSnackbar, setSnackbar } from 'store/layout';

const AUTO_HIDE_DURATION = 3000;

export default function MySnackbar() {
  const snackbar = useSelector(selectSnackbar);

  const dispatch = useDispatch();
  const onClose = () => {
    dispatch(setSnackbar(null));
  };

  return (
    <Snackbar
      open={!!snackbar}
      autoHideDuration={AUTO_HIDE_DURATION}
      onClose={onClose}
    >
      <Alert
        severity={snackbar?.severity}
        sx={{ width: '100%' }}
        elevation={6}
        variant="filled"
      >
        {snackbar?.message}
      </Alert>
    </Snackbar>
  );
}
