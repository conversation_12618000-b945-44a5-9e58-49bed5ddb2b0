import {
  IconButton, InputAdornment, SvgIcon, TextField,
} from '@mui/material';
import { ReactComponent as SearchIcon } from 'static/icons/search-normal.svg';

export default function ContactSearchBox({ defaultValue, onSearchChange }) {
  return (
    <TextField
      fullWidth
      label="نام کاربر را جهت جست‌وجو وارد نمایید"
      defaultValue={defaultValue}
      InputProps={{
        endAdornment: (
          <InputAdornment position="start">
            <IconButton type="submit" edge="end">
              <SvgIcon
                component={SearchIcon}
                sx={{ fill: 'none' }}
              />
            </IconButton>
          </InputAdornment>
        ),
        name: 'search',
      }}
      onChange={onSearchChange}
      value={defaultValue}
      sx={{
        background: '#F3F3F3',
      }}
    />
  );
}
