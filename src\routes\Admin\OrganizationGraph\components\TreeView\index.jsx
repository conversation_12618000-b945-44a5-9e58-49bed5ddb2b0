import React, { useEffect, useRef, useState } from 'react';
import { Box } from '@mui/material';
import OrgChartComponent from './OrgChartComponent';

export function convertData(data) {
  if (!data) return [];

  return (
    data?.map(item => ({
      parentId: item.parent,
      ...item,
    })) || []
  );
}

function TreeView({ data, handleMenuItemClick }) {
  const CardRef = useRef(null);
  const [convertedData, setConvertedData] = useState([]);

  useEffect(() => {
    setConvertedData(convertData(data));
  }, [data]);

  return (
    <Box
      ref={CardRef}
      sx={{
        // mb: 3,
        borderRadius: '8px',
        background: 'white',
        boxShadow: '0px 2px 20px 0px #00000012',
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        maxHeight: '85vh',
        minHeight: '50vh',
        p: 2,
      }}
    >
      <OrgChartComponent
        data={convertedData}
        handleMenuItemClick={handleMenuItemClick}
      />
    </Box>
  );
}

export default TreeView;
