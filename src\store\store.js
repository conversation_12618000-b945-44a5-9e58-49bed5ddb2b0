import { configureStore } from '@reduxjs/toolkit';
import authReducer from './auth';
import layoutReducer from './layout';
import messengerReducer from './messenger';
import chatPlayerReducer from './chatPlayer';

export default configureStore({
    reducer: {
        auth: authReducer,
        layout: layoutReducer,
        messenger: messengerReducer,
        chatPlayer: chatPlayerReducer,
    },
    middleware: (getDefaultMiddleware) => getDefaultMiddleware({ serializableCheck: false, })
});
