import {
  Avatar, Divider, Grid, Stack, styled, Typography,
} from '@mui/material';
import {
  getContent, getContentEvaluationReport,
} from 'apis/content';
import LoadingPage from 'components/LoadingPage/LoadingPage';
import { PATHS } from 'constants';
import { useQuery } from 'react-query';
import { useLoaderData, useNavigate } from 'react-router-dom';
import { useIsDesktop } from 'utils';
import { useState } from 'react';
import { DEFAULT_APPBAR_HEIGHT } from 'components/CustomAppBar/constants';
import DesktopAppBar from 'components/DesktopAppBar/DesktopAppBar';
import { useSelector } from 'react-redux';
import { selectMe } from '../../store/auth';
import MyAppBar from './components/MyAppBar/MyAppBar';

const Name = styled(Typography)(() => ({
  display: 'flex',
  alignItems: 'center',
  fontSize: '15px',
  fontWeight: 'bold',
  color: '#2C2C2E',
  flexGrow: 1,
}));

const LastTime = styled(Typography)(() => ({
  fontSize: '12px',
  fontWeight: 500,
  color: '#64676A',
  // direction: 'rtl',
}));

export async function loader({ params }) {
  return params.contentId;
}

export default function ContentEvaluateReport() {
  const me = useSelector(selectMe);
  const navigate = useNavigate();
  // if (!me || !me.can_elect_content) navigate(PATHS.home);

  const [isLoading, setIsLoading] = useState(true);
  const [rate, setRate] = useState(0);
  const [count, setCount] = useState(0);
  const [list, setList] = useState([]);

  const isDesktop = useIsDesktop();

  const contentId = useLoaderData();

  useQuery(
    [PATHS.content, contentId],
    () => getContent(contentId),
    {
      onSuccess: (data) => {
        if (data.status !== 200) return;

        setCount(data?.data?.evaluator_count || 0);
        setRate(data?.data?.total_score || 0);
      },
    },
  );

  useQuery(
    ['contentSupport', contentId],
    () => getContentEvaluationReport(contentId),
    {
      onSuccess: (data) => {
        setIsLoading(false);
        console.log(data);
        if (data.status !== 200) return;
        setList(data.data);
      },
    },
  );

  // setCount(data?.data?.evaluator_count || 0);
  // setRate(data?.data?.total_score || 0);

  // console.log(data.data);

  if (isLoading) return <LoadingPage />;

  return (
    <>
      {!isDesktop && <MyAppBar title="" />}
      {isDesktop && <DesktopAppBar />}

      <Grid
        container
        sx={{
          height: '100%', overflowY: 'scroll', pb: 2, mt: DEFAULT_APPBAR_HEIGHT, p: 2, pt: 0,
        }}
        columnSpacing={2}
      //   sx={{
      //   overflowY: 'scroll', mt: DEFAULT_APPBAR_HEIGHT, p: 2, pt: 0,
      // }}
      >
        <Grid item xs={12} lg={6}>
          <Stack direction="row" justifyContent="center" sx={{ mt: 1 }}>
            <Typography sx={{ color: '#2C2C2E', fontSize: '16px', fontWeight: 700 }} mr={2}>ارزیابی شده توسط</Typography>
            <Typography sx={{ color: '#2C2C2E', fontSize: '16px', fontWeight: 700 }}>{`(${count} نفر)`}</Typography>
          </Stack>
        </Grid>

        <Grid item xs={12} lg={6}>
          <Stack direction="row" justifyContent="center" sx={{ mt: 1 }}>
            <Typography sx={{ color: '#2C2C2E', fontSize: '16px', fontWeight: 700 }} mr={2}>نمره ارزیابی این محتوا</Typography>
            <Typography sx={{ color: '#2C2C2E', fontSize: '16px', fontWeight: 700 }}>{`(${rate} از ۱۰)`}</Typography>
          </Stack>
        </Grid>

      </Grid>
      <Divider />
      <Stack spacing={2}>
        <Grid
          container
          sx={{ height: '100%' }}
          flex
          justifyContent="center"
        >
          <Grid
            item
            xs={12}
            lg={6}
            sx={{
              overflowY: 'scroll', height: '100%', p: 2, textAlign: 'center',
            }}
            direction="row"
            rowSpacing={2}
            container
          >

            {list.map((item) => (
              <Grid item xs={12} pt={1} pb={1} borderBottom="1px solid #ccc">
                <Stack direction="row" spacing={2}>
                  <Avatar src={item.responder.avatar} />
                  <Stack
                    direction="column"
                    sx={{ minWidth: 0, flexGrow: 1 }}
                    spacing={1}
                  >
                    <Stack direction="row" alignItems="center" sx={{ flexGrow: 1 }}>
                      <Name>
                        <span>{`${item.responder.first_name} ${item.responder.last_name}`}</span>
                      </Name>

                      <LastTime>
                        <span>{`(${item.score} از ۱۰)`}</span>
                      </LastTime>
                      {/* <LastTime> */}
                      {/*  {dayjs(item.created_at).format('YYYY-MM-DD HH:mm')} */}
                      {/* </LastTime> */}
                    </Stack>
                  </Stack>
                </Stack>
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Stack>
    </>
  );
}
