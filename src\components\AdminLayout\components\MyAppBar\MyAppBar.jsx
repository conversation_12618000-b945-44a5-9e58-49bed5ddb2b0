import {
  AppBar, IconButton, Toolbar, Typography,
} from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { selectAppbarMenu } from '../../../../store/layout';
import Logo from '../Logo/Logo';

export default function MyAppBar({
  title,
  titleCenter,
  titleVariant,
  hasBack,
  hasLogo,
}) {
  const navigate = useNavigate();
  const menu = useSelector(selectAppbarMenu);

  return (
    <AppBar sx={{ background: 'white' }}>
      <Toolbar sx={{ margin: titleCenter ? 'auto' : '' }}>
        {hasBack && (
        <IconButton edge="start">
          <NavigateNextIcon
            sx={{ color: 'black' }}
            onClick={() => navigate(-1)}
          />
        </IconButton>
        )}

        {hasLogo && <Logo sx={{ mr: 1 }} />}

        <Typography
          variant={titleVariant ? 'h6' : ''}
          sx={(theme) => ({
            fontWeight: 'bold',
            color: theme.palette.text.primary,
            flexGrow: 1,
          })}
        >
          {title}
        </Typography>

        {menu}
      </Toolbar>
    </AppBar>
  );
}
