function isCreator(userId, creator) {
  return userId === creator;
}

function isAdmin(userId, admins) {
  return admins.includes(userId);
}

export function canRemoveUser(userId, admins, creator, toBeDeletedUserId) {
  const isOtherUserAdmin = isAdmin(toBeDeletedUserId, admins);
  const isOtherUserCreator = isCreator(toBeDeletedUserId, creator);

  return (
    (isCreator(userId, creator) ||
      (isAdmin(userId, admins) && !isOtherUserAdmin && !isOtherUserCreator)) &&
    userId !== toBeDeletedUserId &&
    !isOtherUserAdmin
  );
}

export function canMakeAdmin(userId, admins, creator, otherUser) {
  return isCreator(userId, creator) && !isAdmin(otherUser, admins);
}

export function canMakeUnAdmin(userId, admins, creator, otherUser) {
  return (
    isCreator(userId, creator) &&
    isAdmin(otherUser, admins) &&
    userId !== otherUser
  );
}

export function canBlockUser(userId, admins, otherUser, alreadyBlocked) {
  return (
    isAdmin(userId, admins) && !isAdmin(otherUser, admins) && !alreadyBlocked
  );
}

export function canUnBlockUser(userId, admins, alreadyBlocked) {
  return isAdmin(userId, admins) && alreadyBlocked;
}
