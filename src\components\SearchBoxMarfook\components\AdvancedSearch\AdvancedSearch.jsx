import {
  FormControlLabel,
  FormGroup,
  Stack,
  Typography,
  Checkbox,
  Button,
} from '@mui/material';
import { SEARCH_PARAMS } from 'components/SearchBox/searchConfig';
import JalaliDateTimePicker from 'components/JalaliDateTimePicker/JalaliDateTimePicker';
import { useState } from 'react';
import JalaliDatePicker from '../JalaliDatePicker/JalaliDatePicker';

function Title({ title, sx }) {
  return <Typography sx={{ fontSize: '14px', ...sx }}>{title}</Typography>;
}

function DateRangeFilter({ dateFrom, dateTo, onDateChange }) {
  return (
    <Stack>
      <Stack direction="row" spacing={1} sx={{ mt: 0 }}>
        <Title title="تاریخ" sx={{ width: '80px', paddingTop: '9px' }} />
        <JalaliDatePicker
          label="از"
          inputName="publish_date_from__gt"
          value={dateFrom}
          onChange={value => onDateChange('publish_date_from__gt', value || '')}
        />
        <JalaliDatePicker
          label="تا"
          inputName="publish_date_to__lt"
          value={dateTo}
          onChange={value => onDateChange('publish_date_to__lt', value || '')}
        />
      </Stack>
    </Stack>
  );
}

export default function AdvancedSearch({ searchConfig, setFilter }) {
  const [status, setStatus] = useState(searchConfig.status || []); // Initialize with searchConfig.status or empty array
  const [dateRange, setDateRange] = useState({
    publish_date_from__gt: searchConfig.publish_date_from__gt || null,
    publish_date_to__lt: searchConfig.publish_date_to__lt || null,
  });

  // Handle checkbox change
  const handleCheckboxChange = event => {
    const { name, checked } = event.target;
    const statusValue =
      name === 'waiting'
        ? 'MW'
        : name === 'accepted'
        ? 'MA'
        : name === 'rejected'
        ? 'MR'
        : name === 'archive'
        ? 'MC'
        : '';

    setStatus(prevStatus => {
      if (checked) {
        // Add status if checked and not already included
        return prevStatus.includes(statusValue)
          ? prevStatus
          : [...prevStatus, statusValue];
      } else {
        // Remove status if unchecked
        return prevStatus.filter(item => item !== statusValue);
      }
    });
  };

  const handleDateChange = (name, value) => {
    setDateRange(prev => ({
      ...prev,
      [name]: new Date(value).toISOString(), // Update the specific date field
    }));
  };

  // Handle form submission
  const handleSubmit = () => {
    setFilter({
      ...searchConfig,
      status,
      publish_date_from__gt: dateRange?.publish_date_from__gt || '',
      publish_date_to__lt: dateRange?.publish_date_to__lt || '',
    });
  };
  
  return (
    <Stack spacing={2} sx={{ mt: 1, mb: 1 }}>
      <FormGroup style={{ marginTop: '0px' }}>
        <FormControlLabel
          componentsProps={{ typography: { fontSize: '14px' } }}
          sx={{ justifyContent: 'space-between', m: 0 }}
          control={
            <Checkbox
              defaultChecked={status.includes('MW')}
              sx={{ pr: 0 }}
              name="waiting"
              id="waiting"
              size="small"
              onChange={handleCheckboxChange}
            />
          }
          label="در انتظار تایید"
          labelPlacement="start"
        />
      </FormGroup>
      <FormGroup style={{ marginTop: '2px' }}>
        <FormControlLabel
          componentsProps={{ typography: { fontSize: '14px' } }}
          sx={{ justifyContent: 'space-between', m: 0 }}
          control={
            <Checkbox
              defaultChecked={status.includes('MA')}
              sx={{ pr: 0 }}
              name="accepted"
              id="accepted"
              size="small"
              onChange={handleCheckboxChange}
            />
          }
          label="تایید شده‌ها"
          labelPlacement="start"
        />
      </FormGroup>
      <FormGroup style={{ marginTop: '2px' }}>
        <FormControlLabel
          componentsProps={{ typography: { fontSize: '14px' } }}
          sx={{ justifyContent: 'space-between', m: 0 }}
          control={
            <Checkbox
              defaultChecked={status.includes('MR')}
              sx={{ pr: 0 }}
              name="rejected"
              id="rejected"
              size="small"
              onChange={handleCheckboxChange}
            />
          }
          label="تایید نشده‌ها"
          labelPlacement="start"
        />
      </FormGroup>
      <FormGroup style={{ marginTop: '2px' }}>
        <FormControlLabel
          componentsProps={{ typography: { fontSize: '14px' } }}
          sx={{ justifyContent: 'space-between', m: 0 }}
          control={
            <Checkbox
              defaultChecked={status.includes('MC')}
              sx={{ pr: 0 }}
              name="archive"
              id="archive"
              size="small"
              onChange={handleCheckboxChange}
            />
          }
          label="آرشیو"
          labelPlacement="start"
        />
      </FormGroup>

      <DateRangeFilter
        dateFrom={dateRange.publish_date_from__gt}
        dateTo={dateRange.publish_date_to__lt}
        onDateChange={handleDateChange}
      />

      <Button variant="contained" onClick={handleSubmit}>
        اعمال
      </Button>
    </Stack>
  );
}
