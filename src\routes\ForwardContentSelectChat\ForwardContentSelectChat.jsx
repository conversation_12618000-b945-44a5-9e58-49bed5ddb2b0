import { Stack } from '@mui/material';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectChatIds } from 'store/messenger';
import ChatItem from 'components/ChatItem/ChatItem';
import { useLoaderData } from 'react-router-dom';
import useChats from 'hooks/useChats';
import MyAppBar from './components/MyAppBar/MyAppBar';
import ConfirmBottomSheet from './components/ConfirmBottomSheet/ConfirmBottomSheet';

export async function loader({ params }) {
  return params.contentId;
}

export default function ForwardContentSelectChat() {
  const { getChats } = useChats();
  useEffect(() => getChats(), []);

  const chatIds = useSelector(selectChatIds);
  const contentId = useLoaderData();
  const [selectedChatId, setSelectedChatId] = useState(null);

  return (
    <>
      <MyAppBar />
      <Stack
        sx={{
          pb: 2,
          overflowY: 'scroll',
          height: '100%',
        }}
        spacing={1}
      >
        {chatIds.map((chatId) => (
          <ChatItem
            key={chatId}
            chatId={chatId}
            onClick={() => setSelectedChatId(chatId)}
          />
        ))}
      </Stack>

      <ConfirmBottomSheet
        show={!!selectedChatId}
        hideBottomSheet={() => setSelectedChatId(null)}
        chatId={selectedChatId}
        contentId={contentId}
      />
    </>
  );
}
