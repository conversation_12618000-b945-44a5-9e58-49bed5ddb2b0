import { useEffect, useState } from 'react';
import {
  Avatar,
  Box,
  Button,
  FormControl,
  Grid,
  IconButton,
  Modal,
  Paper,
  Stack,
  styled,
  SvgIcon,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import {
  ControlPointDuplicate,
  Delete,
  Edit,
  PersonAdd,
  PersonOff,
  PersonRemove,
  RemoveModerator,
  RemoveRedEye,
  VerifiedUser,
} from '@mui/icons-material';
import { ReactComponent as ChatTextIcon } from 'static/icons/ChatText.svg';
import { ReactComponent as BellSimpleIcon } from 'static/icons/BellSimple.svg';
import { useIsDesktop } from '../../../../../utils';
import SearchInput from './components/SearchInput';
import { updateOrganization } from '../../../../../apis/organization';
import BottomSheetMessage from '../../../../../components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from '../../../../../components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from '../../../../../components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import BottomSheet from '../../../../../components/BottomSheet/BottomSheet';
import MultiSelectNonAllocatedUsers from './components/MultiSelectNonAllocatedUsers';

const CTableHead = styled(TableHead)(() => ({
  '& th': {
    fontSize: '14px',
    color: '#737373',
    border: 'none',
  },
}));
const CTableRow = styled(TableRow)(() => ({
  '& td': {
    border: 0,
    color: '#222222',
    fontSize: '16px',
  },
}));
function CActionCell({
  sx,
  actions = [],
  basePath,
  onDelete,
  isDesktop,
  id,
  tooltip,
}) {
  const btnStyle = {
    color: '#222',
    background: '#ECEDF7',
    width: isDesktop ? '35px' : 'calc(33% - 10px)',
    minWidth: '35px',
    height: '35px',
    margin: '0 5px',
    boxShadow: 'none',
    '&:hover': {
      background: '#E7EAF4',
    },
  };

  const actionButtons = [
    {
      action: 'view',
      icon: <RemoveRedEye sx={{ fontSize: '14px' }} />,
      link: !!basePath && basePath !== '#' ? `/${basePath}/view/${id}` : '#',
    },
    // { action: 'view', icon: <RemoveRedEye sx={{ fontSize: '14px' }} />, link: '#' },
    {
      action: 'edit',
      icon: <Edit sx={{ fontSize: '14px' }} />,
      link: basePath ? `/${basePath}/${id}/update/` : '#',
    },
    // { action: 'edit', icon: <Edit sx={{ fontSize: '14px' }} />, link: '#' },
    {
      action: 'delete',
      icon: <Delete sx={{ fontSize: '14px' }} />,
      onClick: () => onDelete(id),
    },
    {
      action: 'removeUser',
      icon: <PersonOff sx={{ fontSize: '20px' }} />,
      onClick: () => onDelete(id),
    },
  ];

  return (
    <TableCell sx={sx} colSpan={isDesktop ? 1 : 2}>
      {actionButtons.map(
        ({ action, icon, link, onClick }) =>
          actions.includes(action) && (
            <Link key={action} to={link} style={{ textDecoration: 'none' }}>
              <Tooltip title={tooltip} placement="top">
                <Button
                  variant="contained"
                  size="small"
                  sx={btnStyle}
                  onClick={onClick}
                >
                  {icon}
                </Button>
              </Tooltip>
            </Link>
          ),
      )}
    </TableCell>
  );
}

function MembersSection({ organization = {} }) {
  const isDesktop = useIsDesktop();
  const navigate = useNavigate();

  const [open, setOpen] = useState(false);

  const [members, setMembers] = useState(organization.members || []);
  const [admins, setAdmins] = useState(organization.admins || []);

  const [searchText, setSearchText] = useState('');
  const [addMemberError, setAddMemberError] = useState('');
  const [filteredData, setFilteredData] = useState(organization.members || []);
  const [showDeleteBottomSheet, setShowDeleteBottomSheet] = useState(false);
  const [showAdminBottomSheet, setShowAdminBottomSheet] = useState(false);
  const [showDeleteAdminBottomSheet, setShowDeleteAdminBottomSheet] =
    useState(false);
  const [showMemberAddForm, setShowMemberAddForm] = useState(false);
  const [idToDelete, setIdToDelete] = useState(null);
  const [idToAdmin, setIdToAdmin] = useState(null);

  const [selectedMembers, setSelectedMembers] = useState([]);

  const [addUserForm, setAddUserForm] = useState({ step: 1 });

  useEffect(() => {
    const adminIds = new Set(admins.map(admin => admin.id));

    const filtered = members
      .filter(
        item =>
          item?.display_username
            ?.toLowerCase()
            .includes(searchText?.toLowerCase()) ||
          item?.last_name?.toLowerCase().includes(searchText?.toLowerCase()) ||
          item?.first_name?.toLowerCase().includes(searchText?.toLowerCase()),
      )
      .map(member => ({
        ...member,
        isAdmin: adminIds.has(member.id),
      }));

    setFilteredData(filtered);
  }, [searchText, members, admins]);
  const handleInputChange = e => {
    setSearchText(e.target.value);
  };
  const handleDelete = id => {
    setShowDeleteBottomSheet(true);
    setIdToDelete(id);
  };
  const handleAdmin = id => {
    setShowAdminBottomSheet(true);
    setIdToAdmin(id);
  };
  const handleDeleteAdmin = id => {
    setShowDeleteAdminBottomSheet(true);
    setIdToAdmin(id);
  };
  const addAdmin = async id => {
    try {
      if (!organization.id) {
        return setAddMemberError('سازمان انتخاب نشده است');
      }
      if (!id) {
        return setAddMemberError('حداقل یک کاربر را انتخاب کنید.');
      }
      const updatedAdminIds = admins.map(admin => admin.id);
      const combinedMemberIds = [...updatedAdminIds, id];

      await updateOrganization(organization.id, {
        name: organization.name,
        admins: Array.from(new Set(combinedMemberIds)),
      });
      window.location.reload();
    } catch (e) {
      console.error(e);
      setAddMemberError('خطا در افزودن مدیر!');
    }
  };
  const handleAddMember = async () => {
    try {
      const updatedMemberIds = members.map(member => member.id);
      const combinedMemberIds = [
        ...updatedMemberIds,
        ...selectedMembers.map(member => member.id),
      ];
      const updatedAdminIds = admins.map(admin => admin.id);
      const combinedAdimIds = [
        ...updatedAdminIds,
        ...selectedMembers.map(member => member.id),
      ];
      if (!organization.id) {
        return setAddMemberError('سازمان انتخاب نشده است');
      }
      if (Array.from(new Set(combinedMemberIds)).length === 0) {
        return setAddMemberError('حداقل یک کاربر را انتخاب کنید.');
      }

      console.log(combinedAdimIds);

      await updateOrganization(organization.id, {
        name: organization.name,
        members: Array.from(new Set(combinedMemberIds)),
        admins:
          addUserForm.type === 'admin'
            ? Array.from(new Set(combinedAdimIds))
            : admins.map(item => item.id),
      });
      setSelectedMembers([]);
      setShowMemberAddForm(false);
      window.location.reload();
    } catch (e) {
      console.error(e);
      setAddMemberError('خطا در افزودن کابر!');
    }
  };

  const handleOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setAddUserForm({ step: 1 });
    setSelectedMembers([]);
    setOpen(false);
  };

  const handleUserTypeClick = async (type = 'user') => {
    setAddUserForm({ ...addUserForm, type, step: 2 });
  };

  useEffect(() => {
    console.log(addUserForm);
  }, [addUserForm]);

  const colTitles = ['عکس', 'نام عضو', 'اقدامات'];

  return (
    <Grid
      sx={{
        py: 2,
        px: 3,
        mb: 3,
        borderRadius: '8px',
        background: 'white',
        boxShadow: '0px 2px 20px 0px #00000012',
        width: '100%',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
          gap: '10px',
          paddingBottom: '10px',
          borderBottom: '1px solid #ccc',
        }}
      >
        <SearchInput
          label="جستجو"
          onChange={handleInputChange}
          sx={{ maxWidth: '400px', zIndex: '10' }}
        />
        <Button
          variant="contained"
          size="large"
          type="submit"
          sx={{ width: '180px' }}
          onClick={() => {
            handleOpen();
            setShowMemberAddForm(!showMemberAddForm);
          }}
        >
          <PersonAdd sx={{ marginRight: '6px' }} />
          افزودن عضو
        </Button>
      </Box>
      {showMemberAddForm && (
        <Box sx={{ paddingBottom: '30px', borderBottom: '1px solid #ccc' }} />
      )}
      <TableContainer>
        <Table>
          <CTableHead>
            <TableRow>
              {colTitles.map(title => (
                <TableCell>{title}</TableCell>
              ))}
            </TableRow>
          </CTableHead>
          <TableBody>
            {filteredData.map(row => (
              <CTableRow key={row.id}>
                <TableCell sx={{ maxWidth: '40px' }}>
                  <img
                    width="40px"
                    height="40px"
                    alt="avatar"
                    src={row.avatar || '/logo.png'}
                    style={{ borderRadius: '100px' }}
                  />
                </TableCell>
                <TableCell width="100%">
                  <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                    {row.first_name
                      ? `${row.first_name} ${row.last_name}`
                      : row.username}
                    {row.isAdmin && ' (مدیر) '}
                  </span>
                  <br />
                  <span style={{ fontSize: '14px' }}>
                    {row.display_username ? `@${row.display_username}` : ''}
                  </span>
                </TableCell>

                <TableCell>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                    }}
                  >
                    <Tooltip title="ارسال پیام" placement="top">
                      <IconButton
                        sx={{ display: 'flex' }}
                        onClick={() =>
                          navigate('/admin-panel/group-msg/create', {
                            state: {
                              selectedMembers: [
                                {
                                  id: row.id,
                                  label: row.first_name
                                    ? `${row.first_name} ${row.last_name}`
                                    : row.username,
                                  avatar: row.avatar,
                                },
                              ],
                            },
                          })
                        }
                      >
                        <SvgIcon component={ChatTextIcon} />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="ارسال اعلان" placement="top">
                      <IconButton
                        sx={{ display: 'flex' }}
                        onClick={() =>
                          navigate('/admin-panel/announcement/create', {
                            state: {
                              selectedMembers: [
                                {
                                  id: row.id,
                                  label: row.first_name
                                    ? `${row.first_name} ${row.last_name}`
                                    : row.username,
                                  avatar: row.avatar,
                                },
                              ],
                            },
                          })
                        }
                      >
                        <SvgIcon component={BellSimpleIcon} />
                      </IconButton>
                    </Tooltip>
                    {row.isAdmin ? (
                      <Tooltip title="حذف از مدیر سازمان" placement="top">
                        <IconButton
                          sx={{ display: 'flex' }}
                          onClick={() => handleDeleteAdmin(row.id)}
                        >
                          <RemoveModerator />
                        </IconButton>
                      </Tooltip>
                    ) : (
                      <Tooltip title="ارتقا به مدیر سازمان" placement="top">
                        <IconButton
                          sx={{ display: 'flex' }}
                          onClick={() => handleAdmin(row.id)}
                        >
                          <VerifiedUser />
                        </IconButton>
                      </Tooltip>
                    )}

                    <Tooltip title="حذف از سازمان" placement="top">
                      <IconButton
                        sx={{ display: 'flex' }}
                        onClick={() => handleDelete(row.id)}
                      >
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </CTableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="child-modal-title"
        aria-describedby="child-modal-description"
      >
        <Grid
          container
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            pt: 2,
            px: 4,
            pb: 3,
            borderRadius: '8px',
            background: 'white',
            boxShadow: '0px 2px 20px 0px #00000012',
            width: '50%',
          }}
        >
          <Stack width="100%">
            <Typography
              align="center"
              fontWeight="bold"
              fontSize={18}
              sx={{ pb: 2, borderBottom: '1px solid #ccc' }}
            >
              افزودن عضو/ادمین
            </Typography>
            {addUserForm.step === 1 && (
              <>
                <Typography fontSize={16} sx={{ mt: 4 }}>
                  دسترسی کاربر را انتخاب نمایید:
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    alignItems: 'center',
                    mt: 3,
                    pb: 4,
                    justifyContent: 'center',
                    '& > :not(style)': {
                      display: 'flex',
                      m: 1,
                      width: 128,
                      height: 128,
                      cursor: 'pointer',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: '#11a6a12e',
                      '&:hover': {
                        backgroundColor: '#11a6a175',
                      },
                    },
                  }}
                >
                  <Paper
                    elevation={2}
                    onClick={() => handleUserTypeClick('admin')}
                  >
                    <VerifiedUser sx={{ mr: '3px' }} />
                    ادمین
                  </Paper>
                  <Paper
                    elevation={2}
                    onClick={() => handleUserTypeClick('user')}
                  >
                    <PersonAdd sx={{ mr: '3px' }} />
                    عضو عادی
                  </Paper>
                </Box>
              </>
            )}
            {addUserForm.step === 2 && (
              <>
                <Typography fontSize={16} sx={{ mt: 4 }}>
                  برای انتخاب عضو/اعضا جدید، نام یا شماره شخص مورد نظر را در
                  باکس زیر وارد نمایید:
                </Typography>
                <FormControl fullWidth sx={{ mt: 2, pb: 4 }}>
                  <MultiSelectNonAllocatedUsers
                    selectedMembers={selectedMembers}
                    setSelectedMembers={setSelectedMembers}
                  />
                </FormControl>
                <Typography
                  align="center"
                  fontSize={14}
                  sx={{ mb: '45px', mt: 5, color: 'red' }}
                >
                  {addMemberError}
                </Typography>
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: '0',
                    left: '0',
                    width: '100%',
                    px: 3,
                    py: 2,
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '10px',
                  }}
                >
                  <Button
                    variant="contained"
                    size="large"
                    type="submit"
                    sx={{ mt: 2, width: '100%' }}
                    onClick={handleAddMember}
                  >
                    ذخیره
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    type="button"
                    sx={{ mt: 2, width: '50%' }}
                    onClick={handleClose}
                  >
                    انصراف
                  </Button>
                </Box>
              </>
            )}
          </Stack>
        </Grid>
      </Modal>

      <BottomSheet
        title="حذف عضو"
        hideBottomSheet={() => setShowDeleteBottomSheet(false)}
        show={showDeleteBottomSheet}
      >
        <BottomSheetMessage>
          آیا می‌خواهید این عضو را از سازمان حذف کنید؟
        </BottomSheetMessage>

        <Stack direction="row">
          <BottomSheetPrimaryButton
            onClick={async () => {
              if (idToDelete) {
                await updateOrganization(organization.id, {
                  name: organization.name,
                  admins: admins
                    .map(member => member.id)
                    .filter(id => id !== idToDelete),
                  members: members
                    .map(member => member.id)
                    .filter(id => id !== idToDelete),
                });
                setAdmins(admins.filter(admin => admin.id !== idToDelete));
                setMembers(members.filter(member => member.id !== idToDelete));
                // window.location.reload();
              }
              setShowDeleteBottomSheet(false);
            }}
          >
            بله
          </BottomSheetPrimaryButton>
          <BottomSheetSecondaryButton
            onClick={() => setShowDeleteBottomSheet(false)}
          >
            خیر
          </BottomSheetSecondaryButton>
        </Stack>
      </BottomSheet>

      <BottomSheet
        title="انتخاب به عنوان مدیر"
        hideBottomSheet={() => setShowAdminBottomSheet(false)}
        show={showAdminBottomSheet}
      >
        <BottomSheetMessage>
          آیا می‌خواهید این عضو را به عنوان مدیر سازمان انتخاب کنید؟
        </BottomSheetMessage>

        <Stack direction="row">
          <BottomSheetPrimaryButton
            onClick={async () => {
              if (idToAdmin) {
                await addAdmin(idToAdmin);
              }
              setShowAdminBottomSheet(false);
            }}
          >
            بله
          </BottomSheetPrimaryButton>
          <BottomSheetSecondaryButton
            onClick={() => setShowAdminBottomSheet(false)}
          >
            خیر
          </BottomSheetSecondaryButton>
        </Stack>
      </BottomSheet>

      <BottomSheet
        title="حذف از لیست مدیر های سازمان"
        hideBottomSheet={() => setShowDeleteAdminBottomSheet(false)}
        show={showDeleteAdminBottomSheet}
      >
        <BottomSheetMessage>
          آیا می‌خواهید این عضو را از لیست مدیر های سازمان حذف کنید؟
        </BottomSheetMessage>

        <Stack direction="row">
          <BottomSheetPrimaryButton
            onClick={async () => {
              if (idToAdmin) {
                await updateOrganization(organization.id, {
                  name: organization.name,
                  admins: admins
                    .map(member => member.id)
                    .filter(id => id !== idToAdmin),
                });
                setAdmins(admins.filter(admin => admin.id !== idToAdmin));
              }
              setShowDeleteAdminBottomSheet(false);
            }}
          >
            بله
          </BottomSheetPrimaryButton>
          <BottomSheetSecondaryButton
            onClick={() => setShowDeleteAdminBottomSheet(false)}
          >
            خیر
          </BottomSheetSecondaryButton>
        </Stack>
      </BottomSheet>
    </Grid>
  );
}

export default MembersSection;
