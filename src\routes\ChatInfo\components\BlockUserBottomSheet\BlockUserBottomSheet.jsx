import { Stack } from '@mui/material';
import { blockUser } from 'apis/messenger';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { setSnackbar } from 'store/layout';

export default function BlockUserBottomSheet({
  show,
  hideBottomSheet,
  userId,
  chatId,
}) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  function onBlockUser() {
    blockUser({ chatId, blocked: userId }).then(() => {
      dispatch(setSnackbar({
        message: 'کاربر با موفقیت محدود شد.',
        severity: 'success',
      }));

      navigate(-1);
    });
  }

  return (
    <BottomSheet
      title="محدود کردن کاربر"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        آیا تمایل به محدود کردن کاربر دارید؟
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            onBlockUser();
            hideBottomSheet();
          }}
        >
          بله
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={hideBottomSheet}>
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
