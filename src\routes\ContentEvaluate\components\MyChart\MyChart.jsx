import {
  CartesianGrid,
  Line,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON>r,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Axis,
} from 'recharts';
import { Box } from '@mui/material';
import style from './MyChart.module.css';

function parseDataDate(data) {
  return data.map((item) => ({
    day: new Date(item.day).toLocaleDateString('fa', { day: 'numeric', month: 'numeric' }),
    count: item.count,
  }));
}

export default function MyChart({ data, sx }) {
  const parsedData = parseDataDate(data);
  return (
    <Box sx={{ ...sx }}>
      <ResponsiveContainer width="100%" aspect={4 / 3} className={style.chartContainer}>
        <LineChart
          data={parsedData}
          margin={{
            top: 24, right: 24, left: 0, bottom: 0,
          }}
        >
          <Line
            type="basis"
            dataKey="count"
            stroke="#11A6A1"
            strokeWidth={3}
            dot={false}
          />
          <XAxis
            dataKey="day"
            tick={{ stroke: '#999999', strokeWidth: 0.2 }}
          />
          <YAxis
            allowDecimals={false}
            tickMargin={20}
            tick={{ stroke: '#999999', strokeWidth: 0.2 }}
          />
          <CartesianGrid stroke="#E4E4E433" vertical={false} />
        </LineChart>
      </ResponsiveContainer>
    </Box>
  );
}
