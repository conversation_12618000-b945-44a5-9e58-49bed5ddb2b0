import { Box, Button } from '@mui/material';
import React, { useCallback, useState } from 'react';
import { Add, PlusOne, ControlPointDuplicate } from '@mui/icons-material';
import { Link } from 'react-router-dom';
import CTable from '../../../components/CTable/CTable';
import {
  deleteOrganization,
  getOrganizations,
} from '../../../apis/organization';
import SearchInput from '../AnnouncementView/components/ViewersSection/components/SearchInput';

export default function OrganizationList() {
  const colTitles = ['عکس', 'سازمان مرجع', 'نام سازمان', 'توضیحات', 'اقدامات'];
  const [searchValue, setSearchValue] = useState();

  const rootPath = organization => {
    if (!organization) return null;
    return (
      <>
        {rootPath(organization.parent)}

        <Link
          style={{
            textDecoration: 'none',
            color: '#0FA6A1',
            marginRight: '3px',
          }}
          to={`/admin-panel/organization/view/${organization?.id}`}
        >
          {organization.parent
            ? ` / ${organization?.name}`
            : `${organization?.name}`}
        </Link>
      </>
    );
  };

  const normalizer = useCallback(data => {
    const modifiedData = [];
    for (const i in data) {
      if (!data[i].deleted) {
        modifiedData.push({
          id: data[i].id,
          cols: [
            { type: 'img', value: data[i].avatar },
            {
              type: 'text',
              value: rootPath(data[i]?.parent) || '---',
            },
            { type: 'text', value: data[i].name },
            { type: 'text', value: data[i].description || '---' },
            { type: 'action', actions: ['view', 'edit', 'delete'] },
          ],
        });
      }
    }
    return modifiedData;
  });

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        overflowY: 'scroll',
        mt: 2,
        pt: 2,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
          gap: '10px',
          paddingBottom: '10px',
          borderBottom: '1px solid #ccc',
        }}
      >
        <SearchInput
          label="جستجو"
          onChange={e => setSearchValue(e.target.value)}
          sx={{ maxWidth: '400px', zIndex: '10' }}
        />
        {/*<Link to="/admin-panel/organization/create">*/}
        {/*  <Button*/}
        {/*    variant="contained"*/}
        {/*    size="large"*/}
        {/*    type="submit"*/}
        {/*    sx={{ width: '180px' }}*/}
        {/*  >*/}
        {/*    <ControlPointDuplicate sx={{ marginRight: '6px' }} />*/}
        {/*    افزودن سازمان*/}
        {/*  </Button>*/}
        {/*</Link>*/}
      </Box>
      <CTable
        normalizer={normalizer}
        fetchData={getOrganizations}
        deleteData={deleteOrganization}
        colTitles={colTitles}
        searchValue={searchValue}
        showPagination={false}
        basePath="admin-panel/organization"
      />
    </Box>
  );
}
