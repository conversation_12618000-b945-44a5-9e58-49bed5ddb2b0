import { Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import { requestOTP } from 'apis/auth';

const TIMER = 2 * 60; // two min
export default function ResendOTP({ mobile }) {
  const [time, setTime] = useState(TIMER);

  useEffect(() => {
    const timer = setInterval(() => {
      setTime((prevTime) => prevTime - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  function resendOTP() {
    setTime(TIMER);
    requestOTP(mobile);
  }

  return (
    <Typography
      variant="caption"
      align="center"
      sx={(theme) => ({ color: theme.palette.text.secondary })}
    >
      {time > 0 ? (
        `${time} ثانیه تا ارسال مجدد کد`
      ) : (
        <span onClick={resendOTP}>ارسال مجدد</span>
      )}
    </Typography>
  );
}
