import { Stack, TextField } from '@mui/material';
import { reportItem } from 'apis/report';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import { REPORT_ITEM_TYPE } from 'constants';
import { useRef } from 'react';
import { useDispatch } from 'react-redux';
import { setSnackbar } from 'store/layout';

export default function ReportBottomSheet({
  show,
  hideBottomSheet,
  userId,
}) {
  const dispatch = useDispatch();
  const inputRef = useRef(null);
  function reportUser() {
    reportItem({
      itemType: REPORT_ITEM_TYPE.USER,
      itemId: userId,
      description: inputRef.current.value,
    }).then(() => {
      dispatch(setSnackbar({ message: 'کاربر با موفقیت گزارش شد.', severity: 'success' }));
      inputRef.current.value = '';
    });
  }

  return (
    <BottomSheet
      title="گزارش کاربر"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        آیا می‌خواهید کاربر را گزارش دهید؟
      </BottomSheetMessage>
      <TextField sx={{ mt: 1 }} inputProps={{ maxLength: 100 }} inputRef={inputRef} />

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            reportUser();
            hideBottomSheet();
          }}
        >
          بله
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={hideBottomSheet}>
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
