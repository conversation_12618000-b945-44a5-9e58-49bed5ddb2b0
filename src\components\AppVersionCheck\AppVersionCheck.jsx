import { getLatestAppVersion } from 'apis/clientVersion';
import { useQuery } from 'react-query';
import { useIsDesktop } from 'utils';
import { useState } from 'react';
import UpdateAvailableBottomSheet from './components/UpdateBottomSheet/UpdateBottomSheet';

export default function AppVersionCheck() {
  const { data, isSuccess } = useQuery('app-version', getLatestAppVersion);
  const { version, download_link: downloadLink } = data?.data[0] ?? {};
  const [shouldShowBS, setShouldShowBS] = useState(true);

  const url = new URL(window.location.href);
  const appVersion = new URLSearchParams(url.search).get('app_version');
  const appVersionNumber = appVersion ? Number(appVersion) : null;

  const latestAppVersion = isSuccess ? Number(version) : null;
  const shouldUpdate = latestAppVersion && appVersionNumber && latestAppVersion > appVersionNumber;

  const isDesktop = useIsDesktop();

  return (
    !isDesktop && (
      <UpdateAvailableBottomSheet
        show={shouldShowBS && shouldUpdate}
        downloadLink={downloadLink}
        hideBottomSheet={() => setShouldShowBS(false)}
      />
    )
  );
}
