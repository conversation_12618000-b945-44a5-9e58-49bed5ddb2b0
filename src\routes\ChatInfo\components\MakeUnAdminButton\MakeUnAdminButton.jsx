import { IconButton } from '@mui/material';
import VpnKeyOffOutlinedIcon from '@mui/icons-material/VpnKeyOffOutlined';
import { useState } from 'react';
import MakeUnAdminBottomSheet from '../MakeUnAdminBottomSheet/MakeUnAdminBottomSheet';

export default function MakeUnAdminButton({ chatId, userId }) {
  const [showBS, setShowBS] = useState(false);

  return (
    <>
      <IconButton onClick={() => setShowBS(true)}><VpnKeyOffOutlinedIcon /></IconButton>
      <MakeUnAdminBottomSheet
        show={showBS}
        hideBottomSheet={() => setShowBS(false)}
        chatId={chatId}
        userId={userId}
      />
    </>
  );
}
