import { Stack } from '@mui/material';
import { grey } from '@mui/material/colors';

export default function Container({ sx, children, spacing }) {
  return (
    <Stack
      sx={(theme) => ({
        backgroundColor: grey[100],
        ...sx,
        p: 2,
        borderRadius: theme.shape.borderRadius,
        width: '100%',
      })}
      spacing={spacing}
    >
      {children}
    </Stack>
  );
}
