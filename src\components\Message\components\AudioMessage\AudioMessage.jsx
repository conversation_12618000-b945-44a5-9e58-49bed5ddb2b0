import { Icon<PERSON><PERSON>on, Stack } from '@mui/material';
import MyReactPlayer from 'components/MyReactPlayer/MyReactPlayer';
import IconSax from 'components/IconSax/IconSax';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { playAudio, stopAudio, selectCurrentlyPlaying } from 'store/chatPlayer';
import { selectMessageUploadProgress } from 'store/messenger';
import CircularProgressWithValue from 'components/CircularProgressWithValue/CircularProgressWithValue';
import MessageContainer from '../MessageContainer/MessageContainer';
import MessageText from '../MessageText/MessageText';

function PlayControlIcon({ playing, setPlaying }) {
  return playing
    ? <IconButton onClick={() => setPlaying(false)}><IconSax name="pause" /></IconButton>
    : <IconButton onClick={() => setPlaying(true)}><IconSax name="play" /></IconButton>;
}

export default function AudioMessage({
  message, myId, withAvatar, isAdmin,
}) {
  const dispatch = useDispatch();
  const currentlyPlaying = useSelector(selectCurrentlyPlaying);
  const {
    file,
    text,
    deliveryToken,
  } = message;
  const [playing, setPlaying] = useState(false);

  const handlePlay = () => {
    if (!playing) {
      dispatch(playAudio(deliveryToken));
    } else {
      dispatch(stopAudio());
    }
    setPlaying(!playing);
  };

  useEffect(() => {
    if (deliveryToken !== currentlyPlaying) {
      setPlaying(false);
    }
  }, [currentlyPlaying]);

  const uploadProgress = useSelector(selectMessageUploadProgress(deliveryToken));

  return (
    <MessageContainer message={message} myId={myId} withAvatar={withAvatar} isAdmin={isAdmin}>
      <Stack direction="row-reverse" spacing={1} alignItems="center">

        {uploadProgress && uploadProgress < 100
          ? <CircularProgressWithValue value={uploadProgress} size={24} />
          : <PlayControlIcon playing={playing} setPlaying={handlePlay} />}

        <MyReactPlayer url={file} isAudio playing={playing} sx={{ display: 'none' }} />

        <MessageText sx={{ whiteSpace: 'nowrap', textOverflow: 'ellipsis', overflow: 'hidden' }}>{text}</MessageText>
      </Stack>
    </MessageContainer>
  );
}
