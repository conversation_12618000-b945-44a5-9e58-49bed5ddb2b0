import {
  IconButton,
} from '@mui/material';
import FileInput from 'components/FileInput/FileInput';
import { useRef, useState } from 'react';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import SelectFileMenu from '../SelectFileMenu/SelectFileMenu';

export default function SelectFileButton({ onSelectFiles }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const fileRef = useRef(null);
  const imageRef = useRef(null);

  const showFileSelector = (e) => {
    // fileRef.current.click();
    setAnchorEl(e.currentTarget);
  };
  const open = Boolean(anchorEl);

  const onFileClick = () => {
    fileRef.current.click();
    setAnchorEl(null);
  };

  const onImageClick = () => {
    imageRef.current.click();
    setAnchorEl(null);
  };

  return (
    <>
      <FileInput ref={fileRef} onSelectFiles={onSelectFiles} accept="file/*" />
      <FileInput ref={imageRef} onSelectFiles={onSelectFiles} accept="image/*" />

      <IconButton
        onClick={showFileSelector}
        aria-controls={open ? 'basic-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
      >
        <AttachFileIcon sx={{ color: '#64676A', transform: 'rotate(-45deg)' }} />
      </IconButton>

      <SelectFileMenu
        anchorEl={anchorEl}
        setAnchorEl={setAnchorEl}
        open={open}
        onFileClick={onFileClick}
        onImageClick={onImageClick}
      />
    </>
  );
}
