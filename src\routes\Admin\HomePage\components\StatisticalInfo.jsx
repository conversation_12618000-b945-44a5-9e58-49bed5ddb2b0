import { Box } from '@mui/system';
import {
  CorporateFare,
  Download,
  Favorite,
  Person,
  RssFeed,
  Share,
  Stars,
  Visibility,
} from '@mui/icons-material';
import Badge from './Badge';

export default function StatisticalInfo() {
  return (
    <>
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        textAlign="center"
        flexDirection="row"
        gap={1}
        sx={{
          flexGrow: 1,
        }}
      >
        <Badge
          color="#f59b4e"
          icon={<RssFeed />}
          data={{
            title: 'تعداد محتوای منتشر شده',
            value: '24',
          }}
        />
        <Badge
          color="#3EA9F8"
          icon={<Visibility />}
          data={{
            title: 'مجموع بازدید محتوا',
            value: '57k',
          }}
        />
        <Badge
          color="#38ca79"
          icon={<Stars />}
          data={{
            title: 'آمار حمایت از پست',
            value: '24',
          }}
        />
        <Badge
          color="#DB9100"
          icon={<CorporateFare />}
          data={{
            title: 'تعداد سازمان‌های زیرمجموعه',
            value: '15',
          }}
        />
      </Box>
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        textAlign="center"
        flexDirection="row"
        gap={1}
        sx={{
          flexGrow: 1,
        }}
      >
        <Badge
          color="#E0526A"
          icon={<Favorite />}
          data={{
            title: 'مجموع لایک‌ها',
            value: '1200',
          }}
        />
        <Badge
          color="#38cab3"
          icon={<Download />}
          data={{
            title: 'مجموع دانلود‌ها',
            value: '561',
          }}
        />
        <Badge
          color="#3887ca"
          icon={<Share />}
          data={{
            title: 'مجموع اشتراک گذاری',
            value: '57k',
          }}
        />
        <Badge
          color="#AE2EEC"
          icon={<Person />}
          data={{
            title: 'تعداد اعضا',
            value: '561',
          }}
        />
      </Box>
    </>
  );
}
