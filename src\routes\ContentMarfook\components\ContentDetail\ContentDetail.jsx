import { Divider, Stack, Typography } from '@mui/material';
import { CONTENT_CATEGORY, CONTENT_TYPES } from 'constants';
import dayjs from 'dayjs';
import { TimeRemaining } from 'routes/MarfookFolder';

function ContentDetailTitle({ title }) {
  return (
    <Typography component="span" sx={{ fontSize: '14px' }}>
      {title}
    </Typography>
  );
}

function ContentDetailValue({ value }) {
  return (
    <Typography component="span" sx={{ fontSize: '14px', fontWeight: '500' }}>
      {value}
    </Typography>
  );
}

function resolveType(typeValue) {
  return CONTENT_TYPES.filter(t => t.value === typeValue)[0].name;
}

function resolveCategory(categoryValue) {
  return CONTENT_CATEGORY.filter(c => c.value === categoryValue)[0].name;
}

function formatDate(date) {
  return dayjs(date).format('HH:mm  YYYY-MM-DD');
}

export default function ContentDetail({
  type,
  category,
  subject,
  publishDateFrom,
  publishDateTo,
  createdAt,
}) {
  const contentType = `${resolveType(type)}/${resolveCategory(category)}`;

  return (
    <>
      <Stack
        direction="row"
        justifyContent="space-between"
        sx={{ mt: '15px!important' }}
      >
        <div>
          <ContentDetailTitle title="نوع محتوا: " />
          <ContentDetailValue value={contentType} />
        </div>

        {/* <div>
          <ContentDetailValue value={formatDate(createdAt)} />
        </div> */}
      </Stack>
      <Stack
        direction="row"
        justifyContent="space-between"
        sx={{ mt: '5px!important' }}
      >
        <div>
          <ContentDetailTitle title="محور: " />
          <ContentDetailValue value={subject} />
        </div>
      </Stack>
      <Divider />
      <Stack
        direction="row"
        justifyContent="space-between"
        sx={{ mt: '15px!important' }}
      >
        <div>
          <ContentDetailTitle title="بازه زمانی انتشار: " />
        </div>
        <div>
          <TimeRemaining publish_date_to={publishDateTo} />
        </div>
      </Stack>
      <Stack
        direction="row"
        justifyContent="space-between"
        sx={{ mt: '15px!important' }}
      >
        <div>
          <ContentDetailTitle title="از تاریخ: " />
        </div>
        <div>
          <ContentDetailValue value={`${formatDate(publishDateFrom)}`} />
        </div>
      </Stack>
      <Stack
        direction="row"
        justifyContent="space-between"
        sx={{ mt: '15px!important' }}
      >
        <div>
          <ContentDetailTitle title="تا تاریخ: " />
        </div>
        <div>
          <ContentDetailValue value={`${formatDate(publishDateTo)}`} />
        </div>
      </Stack>
      <Divider />
    </>
  );
}
