import { IconButton } from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { useNavigate, useLocation } from 'react-router-dom';
import { PATHS } from 'constants';

export default function MyBackButton() {
  const navigate = useNavigate();
  const location = useLocation();

  const back = () => {
    if (location.key === 'default') {
      navigate(PATHS.chats);
    } else {
      navigate(-1);
    }
  };

  return (
    <IconButton edge="start" onClick={back}>
      <NavigateNextIcon
        sx={{ color: 'black' }}
      />
    </IconButton>
  );
}
