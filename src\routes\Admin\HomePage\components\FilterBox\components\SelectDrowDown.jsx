import { useState } from 'react';
import {
  Autocomplete,
  Avatar,
  TextField,
  ListItem,
  ListItemAvatar,
  ListItemText,
  CircularProgress,
} from '@mui/material';

export default function SelectDropdown({
  options,
  defaultOption = null,
  handleSelect,
  placeholder = 'انتخاب کنید',
  loading = false,
  setQuery = value => {},
}) {
  const [selectedOption, setSelectedOption] = useState(defaultOption);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const handleChange = (event, newValue) => {
    setSelectedOption(newValue);
    handleSelect(newValue);
  };

  return (
    <Autocomplete
      options={options}
      value={selectedOption}
      onChange={handleChange}
      size="small"
      loading={loading}
      loadingText="در حال بارگذاری..."
      open={!loading && dropdownOpen} // Prevent dropdown when loading
      onOpen={() => {
        if (!loading) setDropdownOpen(true); // Open only if not loading
      }}
      onInputChange={(event, value) => {
        if (value.length >= 2) {
          setQuery(value); // Only set the query if input has at least 2 characters
        } else {
          setQuery(''); // Clear the query for less than 2 characters
        }
      }}
      onClose={() => setDropdownOpen(false)}
      noOptionsText="موردی یافت نشد"
      isOptionEqualToValue={(option, value) => {
        if (!option || !value) return false;
        return option.id === value.id;
      }}
      getOptionLabel={option => (option.label ? option.label : 'بدون نام')}
      renderOption={(props, option) => {
        props.key = option?.id;
        return (
          <li {...props}>
            {option.label && (
              <ListItem sx={{ height: '35px' }}>
                {option.avatar && (
                  <ListItemAvatar>
                    <Avatar
                      src={option.avatar}
                      sx={{ height: '30px', width: '30px' }}
                    />
                  </ListItemAvatar>
                )}
                <ListItemText primary={option.label} />
              </ListItem>
            )}
          </li>
        );
      }}
      renderInput={params => (
        <TextField
          {...params}
          variant="outlined"
          placeholder={placeholder}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {loading ? (
                  <>
                    در حال بارگذاری...
                    <CircularProgress
                      color="inherit"
                      size={20}
                      sx={{ color: '#11a6a1' }}
                    />
                  </>
                ) : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
        />
      )}
    />
  );
}
