import { Stack } from '@mui/material';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import { createContentMessage } from 'dtos/messenger';
import useSendMessage from 'hooks/useSendMessage';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { selectMe } from 'store/auth';

export default function ConfirmBottomSheet({
  show,
  hideBottomSheet,
  chatId,
  contentId,
}) {
  const navigate = useNavigate();
  const me = useSelector(selectMe);
  const sendMessage = useSendMessage();
  function sendContentMessage() {
    sendMessage(createContentMessage(me, chatId, contentId), null);
    navigate(-1);
  }

  return (
    <BottomSheet
      title="ارسال محتوا به گفت‌وگو"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        آیا تمایل به ارسال محتوا رو گفت‌وگوی انتخاب شده را دارید؟
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            sendContentMessage();
            hideBottomSheet();
          }}
        >
          بله
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={hideBottomSheet}>
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
