import { Stack, Typography } from '@mui/material';
import dayjs from 'dayjs';
import MyLink from 'components/MyLink/MyLink';

export default function CallMessage({ message }) {
  const callLink = message.extraData?.call_link;
  const createdAt = dayjs(message.createdAt).format('YYYY-MM-DD HH:mm');

  return (
    <Stack alignItems="center">
      <Typography
        sx={(theme) => ({
          pr: 2,
          pl: 2,
          pt: 0.5,
          pb: 0.5,

          background: theme.palette.secondary.main,
          borderRadius: theme.shape.borderRadius,
          color: theme.palette.secondary.dark,
          fontSize: '12px',
        })}
        align="center"
      >
        کاربر
        {` @${message.author.displayUsername} `}
        در
        <Typography sx={{ fontSize: 'inherit' }} component="span" dir="ltr">
          {` ${createdAt} `}
        </Typography>
        اتاق گفت‌وگو ایجاد کرده است.

        <MyLink to={callLink} sx={{ color: '##1565c0', paddingRight: '4px' }}>
          برای ورود کلیک کنید.
        </MyLink>
      </Typography>
    </Stack>
  );
}
