import { Fab } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import AddIcon from '@mui/icons-material/Add';
import { PATHS } from 'constants';
import { useIsDesktop } from 'utils';

export default function NewFab() {
  const navigate = useNavigate();

  const isDesktop = useIsDesktop();

  return (
    <Fab
      color="primary"
      sx={{ position: 'fixed', bottom: '64px', left: isDesktop ? '64px' : '32px' }}
      size="medium"
      onClick={() => navigate(PATHS.createChat)}
    >
      <AddIcon />
    </Fab>
  );
}
