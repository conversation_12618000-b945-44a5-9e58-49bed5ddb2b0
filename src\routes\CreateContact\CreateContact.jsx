import {
  CircularProgress,
  IconButton,
  InputAdornment,
  Stack,
  TextField,
} from '@mui/material';
import LoadingButton from 'components/LoadingButton/LoadingButton';
import IconSax from 'components/IconSax/IconSax';
import { useMutation, useQuery } from 'react-query';
import { resolveUser } from 'apis/auth';
import { useEffect, useState } from 'react';
import { removeLeadingZeroFromMobile } from 'utils';
import createContact from 'apis/contacts';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { setSnackbar } from 'store/layout';
import { createContact as createContactDTO } from 'dtos/messenger';
import { addContact } from 'store/messenger';
import MyAppBar from './components/MyAppBar/MyAppBar';

const APPBAR_HEIGHT = '56px';

export default function CreateContact() {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');

  const { refetch, data, isError, isSuccess, isLoading } = useQuery(
    phoneNumber,
    async () => resolveUser(removeLeadingZeroFromMobile(phoneNumber)),
    { enabled: false, retry: 0 },
  );
  const foundUser = data?.data;

  useEffect(() => {
    if (isSuccess) {
      setFirstName(foundUser.first_name);
      setLastName(foundUser.last_name);
    }
  }, [isSuccess]);

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const createContactMutation = useMutation(
    () => createContact(firstName, lastName, foundUser.id),
    {
      onError: () => {
        dispatch(
          setSnackbar({ message: 'خطا در ساخت مخاطب', severity: 'error' }),
        );
      },
      onSuccess: data => {
        const contact = createContactDTO(
          data.data.id,
          data.data.user,
          data.data.user_first_name,
          data.data.user_last_name,
        );
        dispatch(addContact(contact));

        navigate(-1);
      },
    },
  );

  return (
    <>
      <MyAppBar />
      <Stack
        sx={{ paddingTop: APPBAR_HEIGHT, pb: 2, height: '100%' }}
        justifyContent="space-between"
      >
        <Stack spacing={2} sx={{ mt: 2 }}>
          <TextField
            label="شماره موبایل (الزامی)"
            value={phoneNumber}
            onChange={e => setPhoneNumber(e.target.value)}
            error={isError}
            helperText={isError && 'کاربر یافت نشد'}
            InputProps={{
              endAdornment: (
                <InputAdornment position="start">
                  {isLoading ? (
                    <CircularProgress size={24} color="secondary" />
                  ) : (
                    <IconButton type="submit" edge="end" onClick={refetch}>
                      <IconSax name="search-normal" />
                    </IconButton>
                  )}
                </InputAdornment>
              ),
              type: 'tel',
            }}
          />

          <TextField
            label="نام (الزامی)"
            value={firstName}
            onChange={e => setFirstName(e.target.value)}
            disabled={!foundUser}
          />

          <TextField
            label="نام خانوادگی (اختیاری)"
            value={lastName}
            onChange={e => setLastName(e.target.value)}
            disabled={!foundUser}
          />
        </Stack>
        <br />
        <LoadingButton
          variant="contained"
          size="large"
          loading={createContactMutation.isLoading}
          onClick={createContactMutation.mutate}
        >
          افزودن
        </LoadingButton>
      </Stack>
    </>
  );
}
