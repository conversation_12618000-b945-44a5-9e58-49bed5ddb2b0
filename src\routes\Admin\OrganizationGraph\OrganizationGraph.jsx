import { Box, Tab, Tabs, Typography } from '@mui/material';
import { useEffect, useLayoutEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { AccountTree, FolderCopy } from '@mui/icons-material';
import { useDispatch } from 'react-redux';
import {
  deleteOrganization,
  getOrganizationGraph,
} from '../../../apis/organization';
import FolderView from './components/FolderView';
import TreeView from './components/TreeView';
import DeleteBottomSheet from './components/DeleteBottomSheet/DeleteBottomSheet';
import { setSidebarCollapsed, setSnackbar } from '../../../store/layout';
import LoadingPage from '../../../components/LoadingPage/LoadingPage';

export default function OrganizationGraph() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const dispatch = useDispatch();

  const [pureData, setPureData] = useState([]);

  const [showDeleteBottomSheet, setShowDeleteBottomSheet] = useState(false);
  const [idToDelete, setIdToDelete] = useState(null);
  const [selectedTab, setSelectedTab] = useState('folder-view');

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await getOrganizationGraph(); // Replace with your API endpoint.

      const foo = JSON.parse(JSON.stringify(response));
      if (foo.data && foo.data.length > 0) foo.data[0].parent = null;
      setPureData(foo.data);
    } catch (err) {
      setError('Failed to load data'); // Handle error case.
    } finally {
      setLoading(false); // Stop the loading state.
    }
  };

  useLayoutEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    const notification = localStorage.getItem('notification');
    if (notification) {
      const { message, severity, timestamp } = JSON.parse(notification);

      // Optional: Check if the message is recent (e.g., within 5 seconds)
      const isRecent = Date.now() - timestamp < 2500 * 60;
      if (isRecent) {
        dispatch(setSnackbar({ message, severity }));
      } else {
        // Clear the notification from localStorage after displaying
        localStorage.removeItem('notification');
      }
    }
  }, [dispatch]);

  const navigate = useNavigate();

  const handleMenuItemClick = (key, node) => {
    switch (key) {
      case 'showUsers':
        navigate(`/admin-panel/organization/view/${node.id}`);
        break;
      case 'addOrganizationChart':
        navigate('/admin-panel/organization/create', {
          state: {
            selectedOrganization: {
              id: node.id,
              value: node.caption || node.name,
            },
          },
        });
        break;
      case 'sendAnnounce':
        navigate('/admin-panel/announcement/create', {
          state: {
            selectedOrganizations: [
              {
                id: node.id,
                label: node.caption || node.name,
                avatar: node.avatar,
              },
            ],
          },
        });
        break;
      case 'sendMessage':
        navigate('/admin-panel/group-msg/create', {
          state: {
            selectedOrganizations: [
              {
                id: node.id,
                label: node.caption || node.name,
                avatar: node.avatar,
              },
            ],
          },
        });
        break;
      case 'edit':
        navigate(`/admin-panel/organization/${node.id}/update`);
        break;
      case 'delete':
        setIdToDelete(node?.id);
        setShowDeleteBottomSheet(true);
        break;
      default:
    }
  };

  const handleTabChange = (event, newValue) => {
    if (newValue === 'chart-view') {
      dispatch(setSidebarCollapsed(true));
    }
    setSelectedTab(newValue);
  };

  if (loading) {
    return <LoadingPage />;
  }

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        overflowY: 'hidden',
        mt: 2,
      }}
    >
      {/* <Box style={{ display: 'flex', flexDirection: 'row', flex: 1 }}> */}
      {/*  <Box */}
      {/*    style={{ */}
      {/*      display: 'flex', */}
      {/*      flexDirection: 'row', */}
      {/*      alignItems: 'center', */}
      {/*      gap: '10px', */}
      {/*    }} */}
      {/*  > */}
      {/*    <Box style={{ display: 'flex', flexDirection: 'column', flex: 1 }}> */}
      {/*      <Typography */}
      {/*        variant="h5" */}
      {/*        sx={{ fontWeight: 700, fontSize: '18px', mb: 3 }} */}
      {/*      > */}
      {/*        نمایش درختی سازمان ها */}
      {/*      </Typography> */}
      {/*    </Box> */}
      {/*  </Box> */}
      {/* </Box> */}
      <Box
        style={{
          width: '100%',
          display: 'flex',
          flexDirection: 'row',
          flex: 1,
          borderBottom: 1,
          borderColor: 'divider',
        }}
      >
        <Tabs
          scrollButtons="auto"
          value={selectedTab}
          onChange={handleTabChange}
        >
          {/*<Tab*/}
          {/*  label={*/}
          {/*    <Typography sx={{ fontSize: '12px' }} noWrap>*/}
          {/*      نمایش لیستی‌*/}
          {/*    </Typography>*/}
          {/*  }*/}
          {/*  value="list-view"*/}
          {/*  icon={<Segment fontSize="small" />}*/}
          {/*  iconPosition="start"*/}
          {/*  sx={{ minHeight: '50px' }}*/}
          {/*/>*/}
          <Tab
            label={
              <Typography sx={{ fontSize: '12px' }} noWrap>
                نمایش پوشه‌ای
              </Typography>
            }
            value="folder-view"
            icon={<FolderCopy />}
            iconPosition="start"
            sx={{ minHeight: '50px' }}
          />
          {/* <Tab
            label={
              <Typography sx={{ fontSize: '12px' }} noWrap>
                نمایش درختی
              </Typography>
            }
            value="chart-view"
            icon={<AccountTree />}
            iconPosition="start"
            sx={{ minHeight: '50px' }}
          /> */}
        </Tabs>
      </Box>

      {selectedTab === 'folder-view' && (
        <FolderView data={pureData} handleMenuItemClick={handleMenuItemClick} />
      )}
      {/*{selectedTab === 'list-view' && (*/}
      {/*  <TableView data={pureData} handleAction={handleMenuItemClick} />*/}
      {/*)}*/}
      {selectedTab === 'chart-view' && (
        <TreeView data={pureData} handleMenuItemClick={handleMenuItemClick} />
      )}

      <DeleteBottomSheet
        show={showDeleteBottomSheet}
        hideBottomSheet={() => setShowDeleteBottomSheet(false)}
        onPrimary={async () => {
          if (idToDelete) await deleteOrganization(idToDelete);
          // todo: refresh table
          dispatch(
            setSnackbar({
              message:
                'درخواست شما ثبت شد و سیستم درحال پردازش درخواست شماست. این تغییر دقایقی زمان خواهد برد. بعد از چند دقیقه صفحه را مجدد بارگذاری نمایید.',
              severity: 'success',
            }),
          );

          localStorage.setItem(
            'notification',
            JSON.stringify({
              message:
                'درخواست شما ثبت شد و سیستم درحال پردازش درخواست شماست. این تغییر دقایقی زمان خواهد برد. بعد از چند دقیقه صفحه را مجدد بارگذاری نمایید.',
              severity: 'success',
              timestamp: Date.now(), // Optional: to manage message expiration
            }),
          );
          fetchData();
        }}
      />
    </Box>
  );
}
