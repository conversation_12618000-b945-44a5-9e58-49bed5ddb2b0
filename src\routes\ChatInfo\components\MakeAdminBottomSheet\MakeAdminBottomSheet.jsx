import { Stack } from '@mui/material';
import { addAdmin } from 'apis/messenger';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { setSnackbar } from 'store/layout';

export default function MakeAdminBottomSheet({
  show,
  hideBottomSheet,
  userId,
  chatId,
}) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  function onAddAdmin() {
    addAdmin({ chatId, admin: userId }).then(() => {
      dispatch(setSnackbar({
        message: 'کاربر با موفقیت مدیر شد.',
        severity: 'success',
      }));

      navigate(-1);
    });
  }

  return (
    <BottomSheet
      title="مدیر کردن کاربر"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        آیا تمایل به دادن نقش مدیر به کاربر هستید؟
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            onAddAdmin();
            hideBottomSheet();
          }}
        >
          بله
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={hideBottomSheet}>
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
