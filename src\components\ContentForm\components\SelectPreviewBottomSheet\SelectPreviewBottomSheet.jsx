import { Stack } from '@mui/material';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';

export default function SelectPreviewBottomSheet({
  hideBottomSheet,
  show,
  onPrimary,
  onSecondary,
  onOutSideClick,
}) {
  return (
    <BottomSheet
      title="انتخاب پیش‌نمایش"
      hideBottomSheet={hideBottomSheet}
      show={show}
      onOutSideClick={onOutSideClick}
    >
      <BottomSheetMessage>
        لطفا برای نمایش بهتر، برای محتوای خود پیش‌نمایش انتخاب کنید
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton onClick={onPrimary}>
          انتخاب پیش‌نمایش
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={onSecondary}>
          لغو
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
