import {
  IconButton, InputAdornment, SvgIcon, TextField,
} from '@mui/material';
import { ReactComponent as SearchIcon } from 'static/icons/search-normal.svg';

export default function SearchInput({ onChange, onSubmit }) {
  return (
    <TextField
      fullWidth
      label="جستجوی کاربر"
      onChange={onChange}
      InputProps={{
        endAdornment: (
          <InputAdornment position="start">
            <IconButton type="submit" edge="end" onClick={onSubmit}>
              <SvgIcon
                component={SearchIcon}
                sx={{ fill: 'none' }}
              />
            </IconButton>
          </InputAdornment>
        ),
      }}
      sx={{
        '& input': {
          height: '17px',
        },
        '& label': {
          top: '-3px',
          fontSize: '15px',
        },
        background: '#F3F3F3',
        height: '50px',
      }}
    />
  );
}
