import { Grid, TextField, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useDispatch } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useState } from 'react';
import LoginLayout from 'components/LoginLayout/LoginLayout';
import { setMe as setMeAction } from 'store/auth';
import { getMe, submitOTP } from 'apis/auth';
import LoadingButton from 'components/LoadingButton/LoadingButton';
import { a2En, fa2En, removeLeadingZeroFromMobile, setToken } from 'utils';
import { PATHS } from 'constants';
import { useMutation } from 'react-query';
import ResendOTP from './components/ResendOTP/ResendOTP';

export default function ValidateOTP() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [otp, setOtp] = useState('');
  const [otpError, setOTPError] = useState('');

  const [searchParams] = useSearchParams();
  const mobile = searchParams.get('mobile');

  const getMeMutation = useMutation(getMe, {
    onSuccess: data => {
      dispatch(setMeAction(data.data));
      if (data.data.display_username) navigate(PATHS.home, { replace: true });
      else navigate(PATHS.registration_form, { replace: true });
    },
  });

  const sendOTPMutation = useMutation(
    () => {
      const cleanedOTP = a2En(fa2En(otp));
      return submitOTP(mobile, cleanedOTP);
    },
    {
      onSuccess: data => {
        setToken(data.data.token);
        getMeMutation.mutate();
      },
      onError: e => {
        if (e?.response?.status === 400) {
          const response = e.response.data;
          const errList = response.non_field_errors || response.otp;
          setOTPError(errList[0]);
        }

        if (e?.response?.status === 429) {
          setOTPError(e.response.data.detail);
        }
      },
    },
  );

  return (
    <LoginLayout>
      <Grid item>
        <Stack direction="row" justifyContent="space-between">
          <Typography sx={{ fontWeight: 'bold' }}>
            کد تایید را وارد کنید
          </Typography>

          <Stack direction="row" onClick={() => navigate('/')}>
            <Typography color="primary">ویرایش موبایل</Typography>

            <ArrowBackIcon
              color="primary"
              sx={theme => ({ marginLeft: theme.spacing(1) })}
            />
          </Stack>
        </Stack>
      </Grid>

      <Grid item>
        <Typography>کد تایید برای شماره {mobile} ارسال گردید</Typography>
      </Grid>

      <Grid item>
        <TextField
          variant="outlined"
          label="کد تایید"
          value={otp}
          onChange={e => setOtp(e.target.value)}
          inputProps={{ maxLength: 6 }}
          helperText={otpError}
          error={otpError}
          fullWidth
          type="tel"
        />
      </Grid>

      <Grid item>
        <LoadingButton
          variant="contained"
          size="large"
          onClick={sendOTPMutation.mutate}
          loading={sendOTPMutation.isLoading || getMeMutation.isLoading}
          fullWidth
        >
          تایید و ورود
        </LoadingButton>
      </Grid>

      <Grid item xs sx={{ textAlign: 'center' }}>
        <ResendOTP mobile={removeLeadingZeroFromMobile(mobile)} />
      </Grid>
    </LoginLayout>
  );
}
