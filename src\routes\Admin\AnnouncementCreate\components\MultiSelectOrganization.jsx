import { useState } from 'react';
import { useQuery } from 'react-query';
import { getOrganizations } from '../../../../apis/organization';
import MultiSelectDropdown from '../../../../components/UI/MultiSelectDrowDown';

function MultiSelectOrganization({
  selectedOrganizations = [],
  setSelectedOrganizations,
}) {
  const [options, setOptions] = useState([]);

  const transformData = data =>
    data.map(item => ({
      id: item.id,
      label: item.name,
      avatar: item.avatar || '/logo.png', // Fallback if avatar is null
    }));

  const { isLoading } = useQuery({
    queryKey: ['OrganizationKey'],
    queryFn: () => getOrganizations(),
    onSuccess: data => {
      if (data.status === 200) {
        setOptions(transformData(data.data));
      }
    },
  });

  return (
    <MultiSelectDropdown
      defaultOptions={selectedOrganizations}
      handleSelect={setSelectedOrganizations}
      options={options}
      placeholder="انتخاب سازمان/سازمان ها"
      loading={isLoading}
    />
  );
}

export default MultiSelectOrganization;
