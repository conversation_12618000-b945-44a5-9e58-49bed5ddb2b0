import { Stack, TextField } from '@mui/material';
import { reportBug } from 'apis/report';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import { useRef } from 'react';
import { useDispatch } from 'react-redux';
import { setSnackbar } from 'store/layout';

export default function BugReportBottomSheet({
  show,
  hideBottomSheet,
}) {
  const dispatch = useDispatch();
  function onReportBug(description) {
    reportBug({ description }).then(() => {
      dispatch(setSnackbar({ message: 'گفت‌وگو با موفقیت گزارش شد.', severity: 'success' }));
    });
  }

  const inputRef = useRef(null);

  return (
    <BottomSheet
      title="گزارش مشکل"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <TextField variant="standard" sx={{ p: 2 }} inputProps={{ ref: inputRef }} />

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            onReportBug(inputRef.current.value);
            hideBottomSheet();
            inputRef.current.value = '';
          }}
        >
          ثبت
        </BottomSheetPrimaryButton>
      </Stack>
    </BottomSheet>
  );
}
