import { <PERSON>lapse, Grid, Stack } from '@mui/material';
import { useState } from 'react';
import { Form } from 'react-router-dom';
import { SEARCH_PARAMS } from './searchConfig';
import SearchInput from './components/SearchInput/SearchInput';
import DropBottom from './components/DropBottom/DropBottom';
import AdvancedSearch from './components/AdvancedSearch/AdvancedSearch';
import Order from './components/Order/Order';
import ViewMode from './components/ViewMode/ViewMode';

export default function SearchBoxMarfookFolder({
  sx,
  searchConfig,
  setFilter,
}) {
  const handleSubmit = e => {
    e.preventDefault();
    const formData = new FormData(e.target);
    Array.from(formData.entries()).forEach(([key, value]) => {
      if (key === 'search') setFilter(value);
    });
  };

  return (
    <Form onSubmit={handleSubmit}>
      <Grid
        container
        justifyContent="center"
        alignItems="center"
        columnSpacing={3}
        sx={sx}
      >
        <Grid item xs={12} lg={8}>
          <SearchInput defaultValue={searchConfig.search} />
        </Grid>
      </Grid>
    </Form>
  );
}
