import { Grid, TextField, Typography } from '@mui/material';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { requestOTP } from 'apis/auth';
import LoadingButton from 'components/LoadingButton/LoadingButton';
import LoginLayout from 'components/LoginLayout/LoginLayout';
import { a2En, fa2En, removeLeadingZeroFromMobile } from 'utils';
import { useMutation } from 'react-query';
import { APP_VERSION } from 'constants';

export default function EnterMobile() {
  const [mobile, setMobile] = useState('');
  const [mobileError, setMobileError] = useState('');
  const navigate = useNavigate();

  const cleanedMobile = removeLeadingZeroFromMobile(fa2En(a2En(mobile)));
  const { mutate, isLoading } = useMutation(() => requestOTP(cleanedMobile), {
    onSuccess: () => {
      navigate(`/auth/validate-otp?mobile=${cleanedMobile}`, { replace: true });
    },
    onError: e => {
      if (e?.response?.status === 400) {
        setMobileError(e.response.data.mobile[0]);
      }
      if (e?.response?.status === 429) {
        setMobileError(e.response.data.detail);
      }
    },
  });

  return (
    <LoginLayout>
      <Grid item>
        <Typography sx={{ fontWeight: 'bold' }}>ورود | ثبت نام</Typography>
      </Grid>

      <Grid item>
        <Typography sx={theme => ({ color: theme.palette.text.secondary })}>
          برای ورود یا ثبت‌نام شماره موبایل خود را وارد کنید
        </Typography>
      </Grid>

      <Grid item>
        <TextField
          variant="outlined"
          label="شماره همراه"
          type="tel"
          value={mobile}
          onChange={e => setMobile(e.target.value)}
          error={mobileError}
          helperText={mobileError}
          fullWidth
          inputProps={{ maxLength: 11 }}
        />
      </Grid>
      <Grid item>
        <LoadingButton
          variant="contained"
          size="large"
          onClick={mutate}
          loading={isLoading}
          fullWidth
        >
          ورود
        </LoadingButton>
      </Grid>

      <Grid item>
        <Typography
          variant="caption"
          component="p"
          textAlign="center"
          sx={theme => ({ color: theme.palette.text.secondary })}
        >
          ورود و ثبت‌نام شما به معنای پذیرش قوانین و مقررات می‌باشد
        </Typography>
        <Typography
          variant="caption"
          component="p"
          textAlign="center"
          sx={theme => ({ color: theme.palette.text.secondary })}
        >
          نسخه برنامه: {`${APP_VERSION}`}
        </Typography>
      </Grid>
    </LoginLayout>
  );
}
