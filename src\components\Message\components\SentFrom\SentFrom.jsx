import { Typography } from '@mui/material';
import { useSelector } from 'react-redux';
import { selectContactWithUser } from 'store/messenger';
import { buildFullName } from 'utils';

export default function ForwardedFrom({ sentFrom, onClick }) {
  const contact = useSelector(selectContactWithUser(sentFrom.id));
  // eslint-disable-next-line no-nested-ternary
  const sentFromName = contact
    ? buildFullName(contact.userFirstName, contact.userLastName)
    : sentFrom.firstName || sentFrom.lastName
      ? buildFullName(sentFrom.firstName, sentFrom.lastName)
      : sentFrom.displayUsername;

  return (
    <Typography
      sx={{
        cursor: 'pointer', fontSize: '12px', p: 1, fontWeight: 'bold', fontStyle: 'normal', color: '#3390ec',
      }}
      onClick={onClick}
      component="span"
    >
      {sentFromName}
    </Typography>
  );
}
