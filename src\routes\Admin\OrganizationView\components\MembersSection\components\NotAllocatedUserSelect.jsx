import { useState, useRef, useEffect } from 'react';
import {
  Checkbox,
  ListItemText,
  Popper,
  Box,
  ClickAwayListener,
  IconButton,
  InputBase,
  MenuItem,
} from '@mui/material';
import { ArrowDropDown } from '@mui/icons-material';
import { styled } from '@mui/system';
import { useQuery } from 'react-query';
import { getNotAllocatedUsers } from 'apis/organization';
import SearchInput from './SearchInput';

const CustomInputBase = styled(InputBase)(() => ({
  border: '1px solid rgba(0,0,0,0.25)',
  borderRadius: 6,
  padding: '6px 8px',
  width: '100%',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
}));

function NotAllocatedUserSelect({
  selectedMembers = [], setSelectedMembers,
}) {
  const [selectedTexts, setSelectedTexts] = useState([]);
  const [open, setOpen] = useState(false);
  const anchorRef = useRef(null);

  const [members, setMembers] = useState([]);

  useEffect(() => {
    if (selectedMembers.length === 0) {
      setSelectedTexts([]);
    }
  }, [selectedMembers]);

  useQuery({
    queryKey: ['OrganizationUserKey'],
    queryFn: () => getNotAllocatedUsers(),
    onSuccess: (data) => {
      if (data.status === 200) {
        setMembers(data.data);
      }
    },
  });

  const handleChange = (event, value) => {
    setSelectedMembers((prevValues) =>
      (prevValues.includes(value.id)
        ? prevValues.filter((v) => v !== value.id)
        : [...prevValues, value.id]),
    );
    setSelectedTexts((prevValues) =>
      (prevValues.includes(value.value)
        ? prevValues.filter((v) => v !== value.value)
        : [...prevValues, value.value]),
    );
  };

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  const [searchText, setSearchText] = useState('');
  const [filteredData, setFilteredData] = useState([]);

  useEffect(() => {
    const filtered = members.filter(item => (`${item.first_name?.toLowerCase()}${item.last_name?.toLowerCase()}0${item.username?.toLowerCase()}`).includes(searchText.toLowerCase()));
    setFilteredData(filtered);
  }, [searchText, members]);

  const handleSearchChange = (e) => {
    setSearchText(e.target.value);
  };

  const selectAll = () => {
    const allMemberIds = filteredData.map(member => member.id);
    const allMemberNames = filteredData.map(member => `${member.first_name} ${member.last_name}`);
    if (selectedMembers.length === filteredData.length) {
      setSelectedMembers([]);
      setSelectedTexts([]);
    } else {
      setSelectedMembers(allMemberIds);
      setSelectedTexts(allMemberNames);
    }
  };

  return (
    <>
      <CustomInputBase
        ref={anchorRef}
        value={selectedTexts.join(', ')}
        onClick={handleToggle}
        endAdornment={(
          <IconButton onClick={handleToggle}>
            <ArrowDropDown />
          </IconButton>
        )}
        placeholder="انتخاب مخاطبین"
        readOnly
      />
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        placement="bottom-start"
        style={{ width: anchorRef.current ? anchorRef.current.clientWidth : undefined }}
      >
        <ClickAwayListener onClickAway={handleClose}>
          <Box sx={{
            border: 'none',
            bgcolor: 'background.paper',
            width: '100%',
            borderRadius: '8px',
            boxShadow: '0px 4px 20px 0px #0000001A',
            // p: 1,
          }}
          >
            <Box style={{
              minHeight: '150px', maxHeight: '250px', overflowY: 'scroll', padding: '10px',
            }}
            >
              <SearchInput label="جستجو" onChange={handleSearchChange} sx={{ position: 'sticky', top: '0px', zIndex: '10' }} />
              {searchText !== '' ? (filteredData.length > 0 ? (
                <>
                  <MenuItem onClick={selectAll}>
                    <Checkbox checked={selectedMembers.length === filteredData.length} />
                    <ListItemText primary="انتخاب همه" />
                  </MenuItem>
                  {filteredData.slice(-10).map((option) => (
                    <MenuItem value={option.id} onClick={(event) => handleChange(event, { id: option.id, value: option.first_name !== '' ? (`${option.first_name} ${option.last_name}`) : option.username })}>
                      <Checkbox checked={selectedMembers.includes(option.id)} />
                      {option.avatar
                          && (
                          <Box sx={{ maxWidth: '40px', mr: 2 }}>
                            <img alt="avatar" width="40px" height="40px" src={option.avatar} style={{ borderRadius: '100px' }} />
                          </Box>
                          )}
                      <ListItemText primary={option.first_name !== '' ? (`${option.first_name} ${option.last_name} (0${option.username})`) : option.username} />
                    </MenuItem>
                  ))}
                </>
              ) : <Box sx={{ width: '100%', textAlign: 'center', py: 6 }}>موردی یافت نشد</Box>) : <Box sx={{ width: '100%', textAlign: 'center', py: 6 }}>نام یا شماره شخص مورد نظر را جستجو کنید</Box>}

            </Box>
          </Box>
        </ClickAwayListener>
      </Popper>
    </>
  );
}

export default NotAllocatedUserSelect;
