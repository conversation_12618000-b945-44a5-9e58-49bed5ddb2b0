import { Button, Divider } from '@mui/material';
import IconSax from 'components/IconSax/IconSax';
import { useNavigate } from 'react-router-dom';
import { PATHS } from 'constants';
import { useState } from 'react';
import ConfirmBottomSheet from 'components/ConfirmBottomSheet/ConfirmBottomSheet';
import { deleteUser as deleteUserAPI } from 'apis/auth';
import { selectMe } from 'store/auth';
import { useDispatch, useSelector } from 'react-redux';
import { removeToken } from 'utils';
import { setSnackbar } from 'store/layout';
import Container from '../Container/Container';

export default function AccountActions({ sx }) {
  const navigate = useNavigate();
  const [showLogoutBS, setShowLogoutBS] = useState(false);
  const [showDeleteAccountBS, setShowDeleteAccountBS] = useState(false);

  const logout = () => {
    removeToken();
    navigate(PATHS.enter_mobile);
  };

  const me = useSelector(selectMe);
  const dispatch = useDispatch();
  const deleteUser = async () => {
    try {
      await deleteUserAPI(me.id);
      logout();
    } catch (e) {
      dispatch(setSnackbar({
        message: 'خطا در حذف حساب کاربری',
        severity: 'error',
      }));
    }
  };

  return (
    <Container sx={sx}>
      <Button
        startIcon={<IconSax name="logout" />}
        onClick={() => setShowLogoutBS(true)}
      >
        خروج از حساب کاربری
      </Button>

      <Divider />

      <Button
        startIcon={<IconSax name="trash" />}
        color="error"
        onClick={() => setShowDeleteAccountBS(true)}
      >
        حذف حساب کاربری
      </Button>

      <ConfirmBottomSheet
        title="خروج از حساب کاربری"
        message="تمایل به خروج از حساب کاربری دارید؟"
        show={showLogoutBS}
        hideBottomSheet={() => setShowLogoutBS(false)}
        onPrimary={logout}
      />
      <ConfirmBottomSheet
        title="حذف حساب کاربری"
        message="با حذف حساب کاربری، اطلاعات حساب شما پاک خواهد شد. تمایل به حذف حساب کاربری دارید؟"
        show={showDeleteAccountBS}
        hideBottomSheet={() => setShowDeleteAccountBS(false)}
        onPrimary={deleteUser}
      />

    </Container>
  );
}
