import { useTheme } from '@emotion/react';
import { Button } from '@mui/material';
import MyLink from 'components/MyLink/MyLink';

export default function MyButton({
  text, icon, active, link,
}) {
  const theme = useTheme();
  const color = active ? theme.palette.primary.main : '#737373';
  return (
    <MyLink to={link}>
      <Button startIcon={icon} sx={{ color }}>
        {text}
      </Button>
    </MyLink>
  );
}
