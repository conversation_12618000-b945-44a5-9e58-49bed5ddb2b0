import { IconButton, InputAdornment, SvgIcon, TextField } from '@mui/material';
import { SEARCH_PARAMS } from 'components/SearchBox/searchConfig';
import { ReactComponent as SearchIcon } from 'static/icons/search-normal.svg';

export default function SearchInput({ defaultValue }) {
  return (
    <TextField
      fullWidth
      label="محور / هشتگ را جستجو کنید"
      defaultValue={defaultValue}
      InputProps={{
        endAdornment: (
          <InputAdornment position="start">
            <IconButton type="submit" edge="end">
              <SvgIcon component={SearchIcon} sx={{ fill: 'none' }} />
            </IconButton>
          </InputAdornment>
        ),
        name: SEARCH_PARAMS.SEARCH,
      }}
      sx={{
        '& input': {
          height: '17px',
        },
        '& label': {
          top: '-3px',
          fontSize: '15px',
        },
        background: '#F3F3F3',
        height: '50px',
      }}
    />
  );
}
