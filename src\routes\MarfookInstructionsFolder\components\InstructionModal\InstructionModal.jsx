import {
  Mo<PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON>,
  Divider,
  <PERSON>ack,
  IconButton,
  CircularProgress,
} from '@mui/material';
import { Close, GetApp, Subject } from '@mui/icons-material';
import dayjs from 'dayjs';
import { generateInstructionPDF } from 'utils/pdfGenerator';
import { useDispatch } from 'react-redux';
import { setSnackbar } from 'store/layout';
import { useState } from 'react';

// Helper function to format date to Jalali
const formatToJalali = date => {
  if (!date) return '';
  try {
    return dayjs(date).format('YYYY/MM/DD');
  } catch (error) {
    return date;
  }
};

// Helper function to check if instruction is expired
const isExpired = expirationDate => {
  if (!expirationDate) return false;
  return dayjs(expirationDate).isBefore(dayjs(), 'day');
};

const modalStyle = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: { xs: '90%', sm: '80%', md: '70%', lg: '60%' },
  maxHeight: '90vh',
  bgcolor: 'background.paper',
  boxShadow: 24,
  borderRadius: 2,
  overflow: 'hidden',
  display: 'flex',
  flexDirection: 'column',
};

export default function InstructionModal({ open, instruction, onClose }) {
  const [downloading, setDownloading] = useState(false);
  const dispatch = useDispatch();

  if (!instruction) return null;

  const expired = isExpired(instruction.expiration_date);

  const handleDownloadPDF = async () => {
    setDownloading(true);

    try {
      await generateInstructionPDF(instruction);

      dispatch(
        setSnackbar({
          message: 'فایل PDF با موفقیت دانلود شد',
          severity: 'success',
        }),
      );
    } catch (error) {
      dispatch(
        setSnackbar({
          message: 'خطا در دانلود فایل PDF',
          severity: 'error',
        }),
      );
    } finally {
      setDownloading(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Box sx={modalStyle}>
        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 3,
            borderBottom: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
            جزئیات دستورالعمل
          </Typography>
          <IconButton onClick={onClose}>
            <Close />
          </IconButton>
        </Box>

        {/* Content */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
          {/* Basic Info */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 2,
                }}
              >
                <Chip
                  label={`شماره: ${instruction.code}`}
                  color="primary"
                  variant="outlined"
                />
                {expired && (
                  <Chip label="منقضی شده" color="error" variant="filled" />
                )}
              </Box>

              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                {instruction.title}
              </Typography>

              {instruction.description && (
                <>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="body1" sx={{ lineHeight: 1.8 }}>
                    {instruction.description}
                  </Typography>
                </>
              )}

              <Divider sx={{ my: 2 }} />

              {/* Dates and Info */}
              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    تاریخ ثبت:
                  </Typography>
                  <Typography variant="body1">
                    {formatToJalali(instruction.created_at)}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    تاریخ انقضا:
                  </Typography>
                  <Typography variant="body1">
                    {formatToJalali(instruction.expiration_date)}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    تعداد محورها:
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Subject sx={{ fontSize: 16, mr: 0.5 }} />
                    <Typography variant="body1">
                      {instruction.subject?.length || 0} محور
                    </Typography>
                  </Box>
                </Box>
              </Stack>
            </CardContent>
          </Card>

          {/* Hashtags */}
          {instruction.hashtags && instruction.hashtags.length > 0 && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                  هشتگ‌ها
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {instruction.hashtags.map((hashtag, index) => (
                    <Chip
                      key={index}
                      label={hashtag}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          )}

          {/* Subjects/Axes */}
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                محورها
              </Typography>

              {instruction.subject && instruction.subject.length > 0 ? (
                instruction.subject.map((subj, index) => (
                  <Card
                    key={subj.id || index}
                    sx={{ mb: 2, backgroundColor: '#f8f9fa' }}
                  >
                    <CardContent>
                      <Typography
                        variant="subtitle1"
                        sx={{ fontWeight: 'bold', mb: 1 }}
                      >
                        {index + 1}. {subj.title}
                      </Typography>

                      {subj.description && (
                        <Typography
                          variant="body2"
                          sx={{ mb: 2, color: 'text.secondary' }}
                        >
                          {subj.description}
                        </Typography>
                      )}

                      {subj.hashtags && subj.hashtags.length > 0 && (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {subj.hashtags.map((hashtag, hashtagIndex) => (
                            <Chip
                              key={hashtagIndex}
                              label={hashtag}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Typography variant="body2" color="text.secondary">
                  هیچ محوری تعریف نشده است
                </Typography>
              )}
            </CardContent>
          </Card>
        </Box>

        {/* Footer */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 3,
            borderTop: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Button variant="outlined" onClick={onClose}>
            بستن
          </Button>
          <Button
            variant="contained"
            startIcon={
              downloading ? <CircularProgress size={16} /> : <GetApp />
            }
            onClick={handleDownloadPDF}
            disabled={downloading}
          >
            {downloading ? 'در حال دانلود...' : 'دانلود PDF'}
          </Button>
        </Box>
      </Box>
    </Modal>
  );
}
