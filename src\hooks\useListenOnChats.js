import { ChatBrief } from 'dtos/messenger';
import { setChat, setChatIds } from 'store/messenger';

const { useDispatch } = require('react-redux');
const { useEffect } = require('react');
const { WS_MESSAGE_ACTIONS } = require('constants');
const { default: useAuthenticatedWebsocket } = require('./useAuthenticatedWebSocket');

function parseChatBrief(chatBrief) {
  return chatBrief.map((chat) => new ChatBrief(
    chat.id,
    chat.name,
    chat.avatar,
    chat.creator,
    chat.type,
    chat.display_name,
    chat.display_avatar,
    chat.last_message_created_at,
    chat.last_message_text,
    chat.last_message_type,
    chat.has_unread_message,
  ));
}

function handleChatBriefAction(dispatch, chatBriefData) {
  const chatBrief = parseChatBrief(chatBriefData);

  const chatIds = chatBrief.map((chat) => chat.id);
  dispatch(setChatIds(chatIds));

  chatBrief.forEach((chat) => {
    dispatch(setChat(chat));
  });
}

export default function useListenOnChats(getChats) {
  const { lastJsonMessage } = useAuthenticatedWebsocket();
  const dispatch = useDispatch();

  useEffect(() => {
    if (lastJsonMessage) {
      const { action, body } = lastJsonMessage;

      if (action === WS_MESSAGE_ACTIONS.CHAT_BRIEF) {
        handleChatBriefAction(dispatch, body);
      }

      if (action === WS_MESSAGE_ACTIONS.MESSAGE) {
        getChats();
      }
    }
  }, [lastJsonMessage]);
}
