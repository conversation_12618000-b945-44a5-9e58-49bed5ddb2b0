body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overscroll-behavior-y: none;
  touch-action: none;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/**
 * Hide scroll bars
 * */

@media (max-width: 600px) {
  * {
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scrollbar-width: none; /* Firefox */

    -webkit-user-select: none; /* Safari */
    -ms-user-select: none; /* IE 10 and IE 11 */
    user-select: none; /* Standard syntax */
  }
  *::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }
}
.scrollbar-thin {
  scrollbar-width: thin !important; /* For Firefox */
  scrollbar-color: #a9a9a9 #d3d3d3 !important; /* Thumb color and track color */
}

.scrollbar-thin::-webkit-scrollbar {
  display: block !important;
  width: 5px !important;
  border-radius: 50px !important;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #d3d3d3 !important; /* lightgray */
  border-radius: 50px !important;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #a9a9a9 !important; /* darkgray */
  border-radius: 50px !important;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #8c8c8c !important; /* slightly darker gray on hover */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
