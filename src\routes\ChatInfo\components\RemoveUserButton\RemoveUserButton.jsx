import { IconButton } from '@mui/material';
import { CloseOutlined } from '@mui/icons-material';
import { useState } from 'react';
import RemoveParticipantBottomSheet from '../RemoveParticipantBottomSheet/RemoveParticipantBottomSheet';

export default function RemoveUserButton({ chatId, userId }) {
  const [showBS, setShowBS] = useState(false);

  return (
    <>
      <IconButton onClick={() => setShowBS(true)}><CloseOutlined /></IconButton>
      <RemoveParticipantBottomSheet
        show={showBS}
        hideBottomSheet={() => setShowBS(false)}
        chatId={chatId}
        userId={userId}
      />
    </>
  );
}
