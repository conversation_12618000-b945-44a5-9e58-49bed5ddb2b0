import { Box, Stack } from '@mui/material';
import styled from '@emotion/styled';
import { useNavigate } from 'react-router-dom';
import { LongPressEventType, useLongPress } from 'use-long-press';
import { useRef, useState } from 'react';
import MessagePreview from 'components/MessagePreview/MessagePreview';
import { genAuthorFullName } from 'dtos/messenger';
import { PATHS, MESSAGE_TYPES } from 'constants';
import { setPathParam } from 'utils';
import UserAvatar from '../../../../routes/Chat/components/UserAvatar/UserAvatar';
import MessageMeta from '../MessageMeta/MessageMeta';
import MessagePaper from '../MessagePaper/MessagePaper';
import MessageMenu from '../MessageMenu/MessageMenu';
import ForwardedFrom from '../ForwardedFrom/ForwardedFrom';
import SentFrom from '../SentFrom/SentFrom';
import ForwardedContent from '../ForwardedContent/ForwardedContent';

const MessageBody = styled(Box)({
  padding: 16,
  // paddingTop: 8,
  paddingTop: 0,
  paddingBottom: 8,
});

export default function MessageContainer(props) {
  const {
    myId, withAvatar, message, children, isAdmin,
  } = props;

  const sent = message.author.id === myId;

  const containerRef = useRef(null);
  const [anchorEl, setAnchorEl] = useState(false);
  const bind = useLongPress(() => setAnchorEl(containerRef.current), {
    filterEvents: () => true,
    captureEvent: true,
    detect: LongPressEventType.Pointer,

  });

  const { forwardedFrom } = message;
  const repliedMessage = message.replyTo;

  const buildProfilePath = (userId) => setPathParam(PATHS.profile, 'userId', userId);

  const navigate = useNavigate();
  const onUserClick = () => navigate(buildProfilePath(message.author.id));

  const isContentMessage = message.type === MESSAGE_TYPES.CONTENT;

  return (
    <>
      <Stack ref={containerRef} direction={sent ? 'row' : 'row-reverse'} spacing={1} {...bind()}>
        {withAvatar && !sent
        && <UserAvatar onClick={onUserClick} avatar={message.author.avatar} />}

        <MessagePaper sent={sent}>
          <Stack>
            {!!forwardedFrom
             && <ForwardedFrom forwardedFrom={forwardedFrom} onClick={onUserClick} />}

            {!forwardedFrom
             && !sent && withAvatar && <SentFrom sentFrom={message.author} onClick={onUserClick} />}

            {repliedMessage && (
              <MessagePreview
                title={genAuthorFullName(repliedMessage.author)}
                message={repliedMessage}
              />
            )}

            {isContentMessage && <ForwardedContent />}

            <MessageBody sx={{
              mt: (!sent && withAvatar) || forwardedFrom || repliedMessage ? 0 : 2,
            }}
            >
              {children}
            </MessageBody>

            <MessageMeta
              sent={sent}
              deliveryStatus={message.deliveryStatus}
              createdAt={message.createdAt}
            />
          </Stack>
        </MessagePaper>

      </Stack>

      <MessageMenu
        open={!!anchorEl}
        anchorEl={anchorEl}
        handleClose={() => setAnchorEl(null)}
        sentByMe={sent}
        isAdmin={isAdmin}
        message={message}
      />
    </>
  );
}
