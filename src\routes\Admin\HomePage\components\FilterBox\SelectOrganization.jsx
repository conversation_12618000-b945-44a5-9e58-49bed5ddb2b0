import { useState } from 'react';
import { useQuery } from 'react-query';
import { getOrganizations } from '../../../../../apis/organization';
import SelectDropdown from './components/SelectDrowDown';

function SelectOrganization({
  selectedOrganizations = [],
  setSelectedOrganizations,
  index = 0,
}) {
  const [options, setOptions] = useState([]);

  const transformData = data =>
    data.map(item => ({
      id: item.id,
      label: item.name,
      avatar: item.avatar || '/logo.png', // Fallback if avatar is null
    }));

  const { isLoading } = useQuery({
    queryKey: ['OrganizationKey'],
    queryFn: () => getOrganizations(),
    onSuccess: data => {
      if (data.status === 200) {
        setOptions(transformData(data.data));
      }
    },
  });

  const handleSelect = value => {
    const copy = [...selectedOrganizations];
    copy[index] = value;
    setSelectedOrganizations(copy);
  };

  return (
    <SelectDropdown
      defaultOption={selectedOrganizations[index]}
      handleSelect={handleSelect}
      options={options}
      placeholder="انتخاب سازمان"
      loading={isLoading}
    />
  );
}

export default SelectOrganization;
