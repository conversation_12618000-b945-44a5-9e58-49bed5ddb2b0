import { createTheme } from '@mui/material';
import { grey } from '@mui/material/colors';
import IRANSansX from './static/fonts/IRANSansX-Regular.woff';

export default createTheme({
  direction: 'rtl',
  palette: {
    primary: {
      light: '#40B7B3',
      main: '#11a6a1',
      dark: '#0B7470',
    },
    secondary: grey,
  },
  typography: {
    fontFamily: 'IRANSansX, Arial',
    caption: {
      fontSize: '0.85rem',
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: `
                @font-face {
                font-family: 'IRANSansX';
                font-style: normal;
                font-display: swap;
                font-weight: 400;
                font-size: 16px;
                src: local('IRANSansX'), url(${IRANSansX}) format('woff');
                unicodeRange: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF;
                }
            `,
    },
    MuiFormLabel: {
      styleOverrides: {
        root: {
          color: '#64676A',
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        root: {
          borderRadius: '8px !important',
        },
      },
    },
    MuiBottomNavigationAction: {
      styleOverrides: {
        root: {
          minWidth: 'auto',
        },
      },
    },
  },
});
