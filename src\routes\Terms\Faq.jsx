/* eslint-disable max-len */
import {
  Stack,
  Typography,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import IconSax from 'components/IconSax/IconSax';
import MyLink from 'components/MyLink/MyLink';
import icon from 'static/imgs/conversation.png';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { useIsDesktop } from 'utils';
import DesktopAppBar from 'components/DesktopAppBar/DesktopAppBar';

function Header() {
  return (
    <Stack justifyContent="center" flexGrow={1} alignItems="center">
      <Box sx={{ textAlign: 'center', height: '80px' }}>
        <img src={icon} alt="conversation icon" />
      </Box>
      <Typography
        sx={{
          color: '#222222',
          fontWeight: 700,
          fontSize: '18px',
          mt: 2,
        }}
        align="center"
      >
        سوالات متداول و راهنمایی
      </Typography>
    </Stack>
  );
}

function ExternalLinkButton({ to, children, mt = '50px' }) {
  return (
    <MyLink to={to} target="_blank" rel="noopener noreferrer">
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          bgcolor: '#C4F8F3',
          borderRadius: '8px',
          pr: 2,
          pl: 2,
          mr: 2,
          ml: 2,
          mt: mt,
          height: '54px',
        }}
      >
        {children}
      </Stack>
    </MyLink>
  );
}

function FAQAccordion1() {
  const title = 'ثبت نام و انتشار محتوا';
  const faqItems = [
    { id: 1, summary: 'مراحل ثبت نام در تام چگونه است؟', details: '' },
    {
      id: 2,
      summary: 'مراحل ثبت و انتشار محتوا در تام چگونه است؟',
      details: '',
    },
    { id: 3, summary: 'قوانین انتشار محتوا چیست؟', details: '' },
  ];

  return (
    <Box sx={{ mt: 5 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
        <FiberManualRecordIcon
          sx={{ fontSize: 'small', mr: 1, color: '#A0A0A0' }}
        />
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
          {title}
        </Typography>
      </Box>
      {faqItems.map(faq => (
        <Accordion
          sx={{ background: 'transparent', boxShadow: 'none' }}
          key={faq.id}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>{faq.summary}</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography>{faq.details}</Typography>
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );
}

function FAQAccordion2() {
  const title = 'پیام رسان';
  const faqItems = [
    { id: 1, summary: 'ویژگی های پیام رسان تام چیست؟', details: '' },
    { id: 2, summary: 'نحوه ساخت گروه و کانال چگونه است؟', details: '' },
  ];

  return (
    <Box sx={{ mt: 5 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
        <FiberManualRecordIcon
          sx={{ fontSize: 'small', mr: 1, color: '#A0A0A0' }}
        />
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
          {title}
        </Typography>
      </Box>
      {faqItems.map(faq => (
        <Accordion
          sx={{ background: 'transparent', boxShadow: 'none' }}
          key={faq.id}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>{faq.summary}</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography>{faq.details}</Typography>
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );
}

export default function Faq() {
  const isDesktop = useIsDesktop();

  return (
    <>
      {isDesktop && <DesktopAppBar />}

      <Stack
        sx={{
          width: '100%',
          height: '100%',
          overflowY: 'scroll',
          marginTop: '16px',
        }}
      >
        <Header />

        {/*<ExternalLinkButton to="/tutorial/about-taam.mp4">*/}
        {/*  <Typography*/}
        {/*    color="black"*/}
        {/*    sx={{*/}
        {/*      height: '54px',*/}
        {/*      lineHeight: '54px',*/}
        {/*      paddingLeft: '8px',*/}
        {/*      fontSize: '14px',*/}
        {/*      display: 'flex',*/}
        {/*      alignItems: 'center',*/}
        {/*    }}*/}
        {/*  >*/}
        {/*    <IconSax*/}
        {/*      name="video-circle"*/}
        {/*      sx={{ color: '#000', mr: 1, height: '100%' }}*/}
        {/*    />*/}
        {/*    <Typography sx={{ fontSize: '16px' }}>آشنایی با تام</Typography>*/}
        {/*  </Typography>*/}

        {/*  <Typography*/}
        {/*    color="black"*/}
        {/*    sx={{*/}
        {/*      height: '54px',*/}
        {/*      lineHeight: '54px',*/}
        {/*      fontSize: '12px !important',*/}
        {/*    }}*/}
        {/*  >*/}
        {/*    تماشای فیلم*/}
        {/*    <ArrowBackIosNewIcon*/}
        {/*      sx={{*/}
        {/*        fontSize: '18px !important',*/}
        {/*        verticalAlign: 'middle',*/}
        {/*        marginLeft: '4px',*/}
        {/*      }}*/}
        {/*    />*/}
        {/*  </Typography>*/}
        {/*</ExternalLinkButton>*/}

        <FAQAccordion1 />
        <FAQAccordion2 />
      </Stack>
    </>
  );
}
