import { Stack, SvgIcon, Typography } from '@mui/material';
import { ReactComponent as EyeIcon } from 'static/icons/eye.svg';
import { ReactComponent as HeartIcon } from 'static/icons/heart.svg';
import { ReactComponent as ShareIcon } from 'static/icons/hierarchy-3.svg';
import { ReactComponent as MessageTextIcon } from 'static/icons/message-text.svg';
import { ReactComponent as DirectInboxIcon } from 'static/icons/direct-inbox.svg';
import { ReactComponent as FavoriteIcon } from 'static/icons/archive-book.svg';
import { formatNumber } from 'utils';
import { ReactComponent as DocumentDownloadIcon } from '../../static/icons/document-download.svg';

function ContentAnalytic({ icon, value }) {
  return (
    <Stack direction="row" alignItems="center">
      <SvgIcon
        component={icon}
        sx={{
          fill: 'none',
          fontSize: '14px',
          verticalAlign: 'middle',
          marginRight: '2px',
        }}
      />
      <Typography sx={{ fontSize: '14px', lineHeight: '12px' }}>
        {value}
      </Typography>
    </Stack>
  );
}

export default function ContentAnalytics({
  small,
  download,
  view,
  like,
  share,
  favorite,
  isOpenLayer,
  shareInMessenger,
  size,
  sx,
}) {
  return (
    <>
      <Stack
        direction="row"
        justifyContent={small ? 'space-evenly' : 'space-between'}
        alignItems="center"
        sx={sx}
      >
        {size !== undefined && (
          <ContentAnalytic
            icon={DocumentDownloadIcon}
            value={`حجم فایل: ${formatNumber(size)}`}
          />
        )}
        {isOpenLayer && (
          <Typography
            component="span"
            sx={{ fontSize: '14px', fontWeight: '500' }}
          >
            ✅ لایه باز
          </Typography>
        )}

      </Stack>
      <Stack
        direction="row"
        justifyContent={small ? 'space-evenly' : 'space-between'}
        alignItems="center"
        sx={sx}
      >

        {like !== undefined && (
          <ContentAnalytic icon={HeartIcon} value={formatNumber(like)} />
        )}

        {favorite !== undefined && (
          <ContentAnalytic icon={FavoriteIcon} value={formatNumber(favorite)} />
        )}

        {share !== undefined && (
          <ContentAnalytic icon={ShareIcon} value="10K" />
        )}

        {shareInMessenger && (
          <ContentAnalytic icon={MessageTextIcon} value="10K" />
        )}

        {download !== undefined && (
          <ContentAnalytic
            icon={DirectInboxIcon}
            value={formatNumber(download)}
          />
        )}

        {view !== undefined && (
          <ContentAnalytic icon={EyeIcon} value={formatNumber(view)} />
        )}
      </Stack>
    </>
  );
}
