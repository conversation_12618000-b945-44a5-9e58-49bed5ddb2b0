import myAxios from './myAxios';

export async function requestOTP(mobile) {
  return myAxios.post('/auth/request_otp/', { mobile });
}

export async function submitOTP(mobile, otp) {
  return myAxios.post('/auth/validate_otp/', { mobile, otp });
}

export async function getStates() {
  return myAxios.get('/auth/state/', {});
}

export async function getCities(stateId) {
  return myAxios.get('/auth/city/', { params: { state: stateId } });
}

export const GET_ME_URL = '/auth/user/me/';
export async function getMe() {
  return myAxios.get(GET_ME_URL);
}

export async function updateUserProfile(userId, profile, partial = false) {
  return partial
    ? myAxios.patch(`/auth/user/${userId}/`, profile)
    : myAxios.put(`/auth/user/${userId}/`, profile);
}

export async function getUser(userId) {
  return myAxios.get(`/auth/user/${userId}/`);
}

export async function deleteUser(userId) {
  return myAxios.delete(`/auth/user/${userId}/`);
}

export async function resolveUser(phoneNumber) {
  return myAxios.get(`/auth/user/${phoneNumber}/by-username/`);
}

export async function listUsers(searchValue) {
  return myAxios.get(`/auth/user/?search=${searchValue}`);
}

export async function inviteUser(data) {
  return myAxios.post('/auth/user/invite/', data);
}
