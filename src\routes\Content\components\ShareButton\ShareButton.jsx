import {
  Button,
  IconButton,
  Tooltip,
  tooltipClasses,
  Modal,
  Typography,
  Box,
} from '@mui/material';
import ShareOutlinedIcon from '@mui/icons-material/ShareOutlined';
import { createIntentUrl, useIsDesktop } from 'utils';
import { styled } from '@mui/system';
import { useState } from 'react';
import TelegramLogo from '../../../../assets/icons/TelegramLogo.png';
import WhatsappLogo from '../../../../assets/icons/WhatsappLogo.png';
import XTwitter from '../../../../assets/icons/XTwitter.png';
import Bale from '../../../../assets/icons/Bale.png';
import eeta from '../../../../assets/icons/eeta.png';

const ShareTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
  backgroundColor: 'red',
  color: 'black',
  boxShadow: 3,
  padding: 1,
  borderRadius: '6px',
  '& .MuiTooltip-arrow': {
    color: 'white',
  },
  '& .MuiTooltip-tooltip': {
    backgroundColor: 'white',
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: theme.palette.common.black,
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.common.red,
  },
}));

// Modal styling
const ModalContent = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 300,
  backgroundColor: theme.palette.background.paper,
  borderRadius: '8px',
  boxShadow: 24,
  padding: theme.spacing(4),
  textAlign: 'center',
}));

export default function ShareButton({ url, type, description, labels, sx }) {
  const isDesktop = useIsDesktop();
  const [openModal, setOpenModal] = useState(false);

  const intentURL = createIntentUrl('share', 'share', {
    mime_type: type,
    url,
    description: encodeURIComponent(description),
    labels: encodeURIComponent(labels.map(label => `#${label}`).join()),
  });

  const [showTooltip, setShowTooltip] = useState(false);

  const handleToggleTooltip = () => {
    setShowTooltip(!showTooltip);
  };

  const handleTwitterShare = () => {
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(
      description,
    )}&url=${encodeURIComponent(url)}`;
    window.open(twitterUrl, '_blank');
  };

  const handleTelegramShare = () => {
    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(
      url,
    )}&text=${encodeURIComponent(description)}`;
    window.open(telegramUrl, '_blank');
  };

  const handleWhatsAppShare = () => {
    const whatsappUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(
      `${description} ${url}`,
    )}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleEetaShare = () => {
    const eitaaUrl = `https://eitaa.com/share/url?url=${encodeURIComponent(
      url,
    )}&text=${encodeURIComponent(description)}`;
    window.open(eitaaUrl, '_blank');
  };

  const handleBaleShare = () => {
    const baleUrl = `https://ble.ir/share/url?url=${encodeURIComponent(url)}`;
    window.open(baleUrl, '_blank');
  };

  const handleButtonClick = () => {
    if (isDesktop) {
      setOpenModal(true); // Open modal on desktop
    } else {
      handleToggleTooltip(); // Toggle tooltip on mobile
    }
  };

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  return (
    <>
      <Button
        sx={{
          height: '40px',
          borderRadius: '6px',
          ...sx,
        }}
        variant="contained"
        startIcon={<ShareOutlinedIcon />}
        disableElevation
        onClick={handleButtonClick}
        href={isDesktop ? null : intentURL}
      >
        اشتراک گذاری
      </Button>

      <Modal
        open={openModal}
        onClose={handleCloseModal}
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <ModalContent>
          <Typography id="modal-title" variant="h6" component="h3">
            برای اشتراک گذاری از نسخه موبایل سامانه تام استفاده نمایید.
          </Typography>

          <Button onClick={handleCloseModal} variant="contained" sx={{ mt: 3 }}>
            بستن
          </Button>
        </ModalContent>
      </Modal>
    </>
  );
}
