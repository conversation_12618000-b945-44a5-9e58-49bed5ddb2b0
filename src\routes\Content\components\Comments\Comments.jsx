import { useSelector } from 'react-redux';
import { selectMe } from 'store/auth';
import { useQuery } from 'react-query';
import { getComments } from '../../../../apis/content';
import Comment from '../Comment/Comment';
import SendComment from '../SendComment/SendComment';
import SendCommentLogin from '../SendCommentLogin/SendCommentLogin';

export default function Comments({ contentId }) {
  const me = useSelector(selectMe);
  const { data } = useQuery(['comments', contentId], () =>
    getComments({ contentId }),
  );

  return (
    <>
      {data?.data?.results?.map(comment => (
        <Comment
          key={comment.id}
          avatar={comment.author.avatar}
          fullName={`${comment.author.first_name} ${comment.author.last_name}`}
          username={comment.author.display_username}
          comment={comment.comment}
          commentId={comment.id}
          authorId={comment.author.id}
          contentId={contentId}
        />
      ))}

      {me ? <SendComment contentId={contentId} /> : <SendCommentLogin />}
    </>
  );
}
