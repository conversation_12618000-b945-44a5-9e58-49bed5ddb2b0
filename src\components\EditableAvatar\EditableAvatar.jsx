import {
  alpha,
  Avatar,
  Box,
  CircularProgress,
  Stack,
  styled,
} from '@mui/material';
import {
  useCallback, useEffect, useRef, useState,
} from 'react';
import ImgCropper from 'components/ImgCropper/ImgCropper';

const HiddenInput = styled('input')(() => ({
  display: 'none',
}));

function Loading({ size }) {
  return (
    <Stack
      sx={(theme) => ({
        width: size,
        height: size,
        background: alpha(theme.palette.grey[400], 0.4),
        borderRadius: '50%',
        position: 'absolute',
        top: 0,
      })}
      justifyContent="center"
      alignItems="center"
    >
      <CircularProgress color="secondary" />
    </Stack>
  );
}

export default function EditableAvatar({
  avatar, setAvatar, loading, size, placeholderIcon,
}) {
  const inputRef = useRef(null);
  const [selectedImg, setSelectedImg] = useState(null);
  const [croppedImg, setCroppedImg] = useState(null);
  const [showCropper, setShowCropper] = useState(false);

  useEffect(() => {
    inputRef.current.addEventListener('change', (event) => {
      setSelectedImg(event.target.files[0]);
    });
  }, []);

  useEffect(() => {
    if (selectedImg) {
      setShowCropper(true);
    }
  }, [selectedImg]);

  useEffect(() => {
    if (croppedImg) {
      setAvatar(croppedImg);
    }
  }, [croppedImg]);

  const onAvatarClick = () => {
    if (!loading) { inputRef.current.click(); }
  };

  const hideCropper = useCallback(() => setShowCropper(false), []);

  const croppedImgURL = croppedImg ? URL.createObjectURL(croppedImg) : null;

  return (
    <Box sx={{ position: 'relative' }}>
      <HiddenInput ref={inputRef} type="file" accept="image/*" />

      <Avatar
        sx={(theme) => ({
          width: size,
          height: size,
          fontSize: 48,
          bgcolor: theme.palette.primary.dark,
          cursor: loading ? '' : 'pointer',
        })}
        src={croppedImgURL || avatar}
        onClick={onAvatarClick}
      >
        {placeholderIcon}
      </Avatar>

      {showCropper && (
        <ImgCropper
          src={URL.createObjectURL(selectedImg)}
          hideCropper={hideCropper}
          setCroppedImg={setCroppedImg}
          cropShape="round"
        />
      )}

      {loading && <Loading size={size} />}
    </Box>
  );
}
