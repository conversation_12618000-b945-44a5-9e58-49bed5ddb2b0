import { Button, Typography, Modal, Box, Grid, Chip } from '@mui/material';
import { Quiz } from '@mui/icons-material';
import { useState } from 'react';
import Divider from '@mui/material/Divider';
import TextField from '@mui/material/TextField';
import IconButton from '@mui/material/IconButton';
import ContentPasteOutlinedIcon from '@mui/icons-material/ContentPasteOutlined';
import { useQuery } from 'react-query';

// Assuming getContent is defined elsewhere (e.g., an API service)
import Type from 'components/ContentForm/components/Type/Type';
import Category from 'components/ContentForm/components/Category/Category';
import Labels from 'components/ContentForm/components/Labels/Labels';
import { getContent } from 'apis/content';

import dayjs from 'dayjs';
import JalaliDateTimePicker from 'components/JalaliDateTimePicker/JalaliDateTimePicker';
import { createContentMarfook } from 'apis/contentMarfook';
import MyReactPlayer from 'components/MyReactPlayer/MyReactPlayer';
import { setSnackbar } from 'store/layout';
import { useDispatch } from 'react-redux';
import { isAudio, isImage, isVideo } from '../../../../utils';
import { CONTENT_ASPECT_RATIO, PATHS } from '../../../../constants';

export default function MarfookButton({ id, active = false, sx }) {
  const [open, setOpen] = useState(false); // State to control modal open/close
  const dispatch = useDispatch();

  // Fetch content data
  const query = useQuery([PATHS.content, id], () => getContent(id), {
    enabled: !!id, // Only fetch if id is provided
  });

  // Modal open/close handlers
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  // Form submission handler (implement as needed)
  const submitForm = async event => {
    event.preventDefault();

    const formData = new FormData(event.target);
    Object.keys(query.data?.data).map(item => {
      formData.append(item, query.data?.data?.[item]);
    });
    formData.set('author', query?.data?.data?.author?.id);
    formData.append('file_size', query?.data?.data?.size);
    formData.append('content_id', query?.data?.data?.id);
    formData.append('organization', '---');

    try {
      await createContentMarfook(formData);

      dispatch(
        setSnackbar({
          message: 'محتوا با موفقیت جهت بررسی ارسال شد!',
          severity: 'success',
        }),
      );
      handleClose(); // Close modal after submission (optional)
    } catch (e) {
      console.error(e);

      dispatch(
        setSnackbar({ message: 'خطا در ثبت مرفوک!', severity: 'error' }),
      );
    }
  };

  // Placeholder for custom component props (will be replaced by fetched data)
  const content = query.data?.data || {
    file: null,
    file_type: '',
    title: '',
    description: '',
    type: '',
    category: '',
    is_open_layer: false,
    id: '',
    elected: false,
    elected_hours: 0,
    elected_date: '',
    labels: [],
    interaction_requests: [],
  };

  // Mocked or placeholder values (replace with actual implementations)
  const errors = {}; // Update with actual error handling
  const mutation = { isLoading: false }; // Update with actual mutation state
  const descriptionRef = { current: null }; // Mock ref
  const isDesktop = true; // Mock (adjust based on your needs)
  const DIVIDER_MY = 2; // Mock constant

  // Mock functions (replace with actual implementations)
  const pasteDescription = () => {};

  // Loading and error states
  if (query.isLoading) {
    return <Typography>در حال بارگذاری...</Typography>;
  }

  if (query.isError) {
    return <Typography>خطا در بارگذاری داده‌ها</Typography>;
  }

  return (
    <>
      <Button
        onClick={handleOpen} // Open modal on click
        variant={active ? 'contained' : ''}
        startIcon={active ? <Quiz /> : null}
        sx={{
          height: '40px',
          backgroundColor: active ? '' : '#F1F2F9',
          borderRadius: '6px',
          ...sx,
        }}
        disabled={!active}
      >
        <Typography
          sx={{ fontSize: '14px', fontWeight: 'bold', fontStyle: 'bold' }}
        >
          <span>باز انتشار برای مرفوک</span>
        </Typography>
      </Button>

      {/* Modal */}
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: { xs: '90%', sm: '80%', md: '60%' },
            maxHeight: '80vh',
            overflowY: 'auto',
            bgcolor: 'background.paper',
            boxShadow: 24,
            p: 4,
            borderRadius: 2,
          }}
        >
          <Typography
            id="modal-title"
            variant="h6"
            component="h2"
            gutterBottom
            sx={{
              textAlign: 'center',
              fontWeight: 'bold',
              mb: 4,
              position: 'sticky',
            }}
          >
            ویرایش محتوا
          </Typography>
          <form onSubmit={submitForm}>
            <Grid container columnSpacing={2}>
              <Grid item xs={12} lg={5}>
                <Box
                  sx={{
                    position: 'relative',

                    width: '100%',
                    aspectRatio: `${CONTENT_ASPECT_RATIO}`,

                    borderRadius: '8px',
                    border: '1px solid #D1D1D6',

                    display: 'flex',
                    justifyContent: 'center',
                  }}
                  alignItems="center"
                >
                  {isImage(content.file_type) && (
                    <img
                      alt="preview"
                      src={content.file}
                      style={{
                        width: '100%',
                        borderRadius: '8px',
                      }}
                    />
                  )}
                  {isVideo(content.file_type) && (
                    <MyReactPlayer
                      url={content.file}
                      isAudio={isAudio(content.file_type)}
                    />
                  )}
                  <Box
                    sx={{
                      position: 'absolute',
                      height: '60px',
                      bottom: '1px',
                      left: '1px',
                      right: '1px',
                      background:
                        content.status === 'MR'
                          ? 'linear-gradient(360deg, rgba(255, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%)'
                          : content.status === 'MA'
                          ? 'linear-gradient(360deg, rgba(72, 255, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%)'
                          : content.status === 'MW'
                          ? 'linear-gradient(360deg, rgba(255, 208, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%)'
                          : 'linear-gradient(360deg, rgba(63, 63, 63, 0.6) 0%, rgba(0, 0, 0, 0) 100%)',
                      color: '#fff',
                      textAlign: 'center',
                      pt: 2,
                    }}
                  >
                    {content.status === 'MR' && (
                      <Chip label="رد شده" color="error" size="small" />
                    )}
                    {content.status === 'MA' && (
                      <Chip label="منتشر شده" color="success" size="small" />
                    )}
                    {content.status === 'MW' && (
                      <Chip label="در انتظار" color="warning" size="small" />
                    )}
                    {content.status === 'MC' && (
                      <Chip
                        label="آرشیو شده"
                        size="small"
                        sx={{ background: '#fff' }}
                      />
                    )}
                  </Box>
                </Box>
              </Grid>
              <Grid
                item
                xs={12}
                lg={7}
                sx={{
                  mt: isDesktop ? 0 : 2,
                  maxHeight: 600,
                  overflowY: 'scroll',
                  py: 2,
                  px: 2,
                }}
                className="scrollbar-thin"
              >
                <Type
                  type={content.type}
                  errors={errors.type}
                  disabled={mutation.isLoading}
                  selectedFileType={content.file_type}
                />

                <Category
                  value={content.category}
                  errors={errors.category}
                  disabled={mutation.isLoading}
                />

                <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

                <TextField
                  variant="outlined"
                  required
                  label="عنوان"
                  name="title"
                  defaultValue={content.title}
                  helperText={errors.title}
                  error={!!errors.title}
                  disabled={mutation.isLoading}
                  inputProps={{ maxLength: 100 }}
                  fullWidth
                />

                <TextField
                  sx={{ mt: 2 }}
                  label="توضیحات"
                  name="description"
                  defaultValue={content.description}
                  variant="outlined"
                  multiline
                  rows={4}
                  helperText={errors.description}
                  disabled={mutation.isLoading}
                  fullWidth
                  inputRef={descriptionRef}
                  InputProps={{
                    endAdornment: (
                      <IconButton edge="end" onClick={pasteDescription}>
                        <ContentPasteOutlinedIcon />
                      </IconButton>
                    ),
                  }}
                />

                <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

                <TextField
                  variant="outlined"
                  required
                  label="محور"
                  name="subject"
                  defaultValue={content.subject}
                  helperText={errors.subject}
                  error={!!errors.subject}
                  disabled={mutation.isLoading}
                  inputProps={{ maxLength: 100 }}
                  fullWidth
                />

                <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

                <Typography
                  variant="body2"
                  style={{ fontSize: '13px' }}
                  color="textSecondary"
                  mb={1}
                >
                  ** مجوز انتشار این محتوا فقط در روز و ساعات تعیین شده زیر
                  خواهد بود
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <JalaliDateTimePicker
                      inputName="publish_date_from"
                      label="از تاریخ"
                      size="medium"
                      setMinDateToday
                      defaultValue={dayjs()}
                      time
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <JalaliDateTimePicker
                      inputName="publish_date_to"
                      label="تا تاریخ"
                      size="medium"
                      setMinDateToday
                      defaultValue={dayjs()}
                    />
                  </Grid>
                </Grid>

                <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

                <Labels defaultValue={content.labels} />

                <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

                <TextField
                  sx={{ mt: 2 }}
                  label="توضیحات کارشناس"
                  name="expert_description"
                  defaultValue={content.expert_description}
                  variant="outlined"
                  multiline
                  rows={4}
                  helperText={errors.expert_description}
                  disabled={mutation.isLoading}
                  fullWidth
                />

                <Button
                  variant="contained"
                  size="large"
                  type="submit"
                  fullWidth
                  sx={{ mt: 2 }}
                >
                  ثبت برای مرفوک
                </Button>
              </Grid>
            </Grid>
          </form>
        </Box>
      </Modal>
    </>
  );
}
