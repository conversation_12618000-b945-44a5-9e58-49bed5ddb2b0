import myAxios from './myAxios';

// export async function createOrganization(data, onProgress, abortController) {
//   return myAxios.post('/auth/organizations/', data, {
//     signal: abortController.signal,
//     headers: { 'Content-Type': 'application/json' },
//     onUploadProgress: e => {
//       onProgress(Math.floor((e.loaded / e.total) * 100));
//     },
//   });
// }

export async function getMarfookUserList() {
  return myAxios.get('/marfook/user-management/');
}

export async function updateMarfookUser(id, data) {
  return myAxios.put(`/marfook/user-management/${id}/`, data); // data: {gropus: ['persmisino1', 'permision2']}
}

export async function updateMarfookUserBulk(data) {
  return myAxios.put('/marfook/user-management/bulk-update/', data); // data: {gropus: ['persmisino1', 'permision2']}
}

export async function getMarfookInstructions() {
  return myAxios.get('/marfook/instruction/');
}

export async function getMarfookInstructionsPublic() {
  return myAxios.get('/marfook/instructions/');
}

export async function getMarfookInstructionsList() {
  return myAxios.get('/marfook/instructions/list/');
}

// export async function getOrganization(id) {
//   return myAxios.get(`/auth/organizations/${id}/`);
// }

// export async function getOrganizationGraph() {
//   return myAxios.get('/auth/organizations/traverse/');
// }

// export async function updateOrganization(id, data) {
//   return myAxios.put(`/auth/organizations/${id}/`, data);
// }
export async function createMarfookInstruction(
  data,
  onProgress,
  abortController,
) {
  return myAxios.post('/marfook/instruction/', data, {
    signal: abortController.signal,
    headers: { 'Content-Type': 'application/json' },
    onUploadProgress: e => {
      onProgress(Math.floor((e.loaded / e.total) * 100));
    },
  });
}

export async function getMarfookInstruction(id) {
  return myAxios.get(`/marfook/instruction/${id}/`);
}

export async function updateMarfookInstruction(
  id,
  data,
  onProgress,
  abortController,
) {
  return myAxios.put(`/marfook/instruction/${id}/`, data, {
    signal: abortController.signal,
    headers: { 'Content-Type': 'application/json' },
    onUploadProgress: e => {
      onProgress(Math.floor((e.loaded / e.total) * 100));
    },
  });
}

export async function deleteMarfookInstruction(id) {
  return myAxios.delete(`/marfook/instruction/${id}/`);
}

export async function downloadMarfookInstructionPDF(id) {
  return myAxios.get(`/marfook/instruction/${id}/pdf/`, {
    responseType: 'blob',
  });
}

// export function getOrganizationUsers() {
//   return myAxios.get('/auth/organizations/users/');
// }

// export function getNotAllocatedUsers() {
//   return myAxios.get('/auth/organizations/not-allocated-users/');
// }
