import { Skeleton, styled } from '@mui/material';
import { useState } from 'react';

const StyledImage = styled('img')({
  maxWidth: '100%',
  borderRadius: '8px',
});

export default function Image({ src, onClick }) {
  const [loading, setLoading] = useState(true);

  const loaded = () => {
    setLoading(false);
  };

  return (
    <>
      <Skeleton sx={{ display: loading ? '' : 'none', width: '128px', height: '128px' }} animation="wave" />
      <StyledImage src={src} onClick={onClick} onLoad={loaded} sx={{ display: loading ? 'none' : '' }} />
    </>
  );
}
