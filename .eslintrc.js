module.exports = {
  env: {
    browser: true,
    es2021: true,
  },
  extends: ['plugin:react/recommended', 'airbnb'],
  overrides: [],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  plugins: ['react'],
  rules: {
    'react/jsx-uses-react': 'off',
    'react/react-in-jsx-scope': 'off',
    'react/prop-types': 'off',
    'import/no-extraneous-dependencies': 'off',
    'react/jsx-no-bind': 'off',
    'import/no-unresolved': 'off',
    'no-plusplus': 'off',
    'jsx-a11y/no-static-element-interactions': 'off',
    'jsx-a11y/click-events-have-key-events': 'off',
    'react/jsx-props-no-spreading': 'off',
    'react/jsx-wrap-multilines': 'off',
    'react/jsx-curly-newline': 'off',
    'react/no-unstable-nested-components': 'off',
    'import/prefer-default-export': 'off',
    'no-shadow': 'off',
    'new-cap': 'off',
    'linebreak-style': 'off',
    'implicit-arrow-linebreak': 'off',
    'arrow-parens': 'off',
    'function-paren-newline': 'off',
  },
};
