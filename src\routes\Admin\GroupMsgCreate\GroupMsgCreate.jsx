import {
  Box,
  Button,
  Checkbox,
  CircularProgress,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  IconButton,
  Switch,
  TextField,
  Typography,
} from '@mui/material';
import { useMutation } from 'react-query';
import { useIsDesktop } from 'utils';
import { useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { ContentPasteGoOutlined } from '@mui/icons-material';
import { createGroupMsg } from '../../../apis/groupMsg';
import { setSnackbar } from '../../../store/layout';
import MultiSelectAdmins from '../AnnouncementCreate/components/MultiSelectAdmins';
import MultiSelectOrganization from '../AnnouncementCreate/components/MultiSelectOrganization';
import MultiSelectMembers from '../AnnouncementCreate/components/MultiSelectMember';

export default function GroupMsgCreate() {
  const isDesktop = useIsDesktop();
  const location = useLocation();

  const [sendToOrgan, setSendToOrgan] = useState(
    !!location.state?.selectedOrganizations || false,
  );
  const [sendToAdmins, setSendToAdmins] = useState(
    !!location.state?.selectedAdmins || false,
  );
  const [sendToMembers, setSendToMembers] = useState(
    !!location.state?.selectedMembers || false,
  );
  const descriptionRef = useRef(null);
  const formRef = useRef(null);

  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);

  const [selectedMembers, setSelectedMembers] = useState(
    location?.state?.selectedMembers || [],
  );
  const [selectedAdmins, setSelectedAdmins] = useState(
    location?.state?.selectedAdmins || [],
  );
  const [selectedOrganizations, setSelectedOrganizations] = useState(
    location?.state?.selectedOrganizations || [],
  );

  const pasteDescription = async () => {
    descriptionRef.current.value = await navigator.clipboard.readText();
  };

  const dispatch = useDispatch();
  const abortController = useRef(null);
  const mutation = useMutation(
    ({ event }) => {
      setLoading(true);

      const { body, use_chat, use_sms } = event.target;
      if (body.value === '') {
        throw { message: 'متن پیام اجباری است', severity: 'error' };
      } else if (
        selectedAdmins.length === 0 &&
        selectedMembers.length === 0 &&
        selectedOrganizations.length === 0
      ) {
        throw { message: 'حداقل یک مخاطب انتخاب کنید', severity: 'error' };
      }

      return createGroupMsg(
        {
          body: body.value,
          receiving_organizations: selectedOrganizations.map(x => x.id) || [],
          receiving_users: sendToAdmins
            ? selectedAdmins.map(x => x.id) || []
            : selectedMembers.map(x => x.id) || [],
          use_sms: use_sms?.checked,
          use_chat: use_chat?.checked,
        },
        setProgress,
        abortController.current,
      );
    },
    {
      onSuccess: async data => {
        dispatch(
          setSnackbar({
            message: 'پیام گروهی با موفقیت ارسال شد',
            severity: 'success',
          }),
        );
        setProgress(0);
        setLoading(false);
        formRef.current.reset();
        setSelectedAdmins([]);
        setSelectedMembers([]);
        setSelectedOrganizations([]);
      },
      onError: error => {
        if (error?.response?.status === 400) {
          dispatch(
            setSnackbar({
              message: 'خطا در ارسال پیام گروهی',
              severity: 'error',
            }),
          );
        } else dispatch(setSnackbar(error));

        setProgress(0);
        setLoading(false);
      },
    },
  );

  const errors = mutation.error?.response?.data || {};

  const submitForm = event => {
    abortController.current = new AbortController();
    event.preventDefault();
    mutation.mutate({ event });
  };

  const resetCheckBoxes = async () => {
    setSendToOrgan(false);
    setSendToAdmins(false);
    setSendToMembers(false);
    setSelectedOrganizations([]);
    setSelectedMembers([]);
    setSelectedAdmins([]);
  };

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        overflowY: 'scroll',
        mt: 2,
      }}
    >
      <form onSubmit={submitForm} ref={formRef}>
        <Grid container columnSpacing={2}>
          <Grid item xs={12} lg={8} sx={{ mt: isDesktop ? 0 : 2 }}>
            <TextField
              sx={{ mt: 2 }}
              label="متن پیام"
              name="body"
              variant="outlined"
              helperText={errors.body}
              multiline
              rows={4}
              fullWidth
              inputRef={descriptionRef}
              InputProps={{
                endAdornment: (
                  <IconButton edge="end" onClick={pasteDescription}>
                    <ContentPasteGoOutlined />
                  </IconButton>
                ),
              }}
            />

            <Divider sx={{ my: 3 }} />

            <Typography sx={{ fontSize: 16, fontWeight: 'bold' }}>
              ارسال از طریق
            </Typography>

            <FormGroup>
              <FormControlLabel
                componentsProps={{ typography: { fontSize: '14px' } }}
                sx={{
                  justifyContent: 'end',
                  m: 0,
                  mt: 1,
                  direction: 'rtl',
                }}
                control={
                  <Checkbox
                    defaultChecked
                    sx={{ pr: 1 }}
                    name="use_chat"
                    id="use_chat"
                    size="small"
                  />
                }
                label="پیام رسان تام"
                labelPlacement="start"
              />
            </FormGroup>
            <FormGroup>
              <FormControlLabel
                componentsProps={{ typography: { fontSize: '14px' } }}
                sx={{ justifyContent: 'end', m: 0, direction: 'rtl' }}
                control={
                  <Checkbox
                    defaultChecked={false}
                    sx={{ pr: 1 }}
                    name="use_sms"
                    id="use_sms"
                    size="small"
                  />
                }
                label="ارسال پیامک"
                labelPlacement="start"
              />
            </FormGroup>

            <Divider sx={{ mt: 3, mb: 3 }} />

            <Typography sx={{ fontSize: 16, fontWeight: 'bold' }}>
              ارسال به
            </Typography>

            <FormGroup row sx={{ justifyContent: 'space-between' }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={sendToAdmins}
                    onChange={e =>
                      resetCheckBoxes() && setSendToAdmins(e.target.checked)
                    }
                  />
                }
                label="مدیر سازمان ها"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={sendToMembers}
                    onChange={e =>
                      resetCheckBoxes() && setSendToMembers(e.target.checked)
                    }
                  />
                }
                label="اعضا/عضو انتخابی"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={sendToOrgan}
                    onChange={e =>
                      resetCheckBoxes() && setSendToOrgan(e.target.checked)
                    }
                  />
                }
                label="سازمان‌ها/سازمان‌ انتخابی"
              />
            </FormGroup>

            <FormControl
              fullWidth
              sx={
                sendToAdmins
                  ? { mt: 2 }
                  : { mt: 0, height: '0', visibility: 'hidden' }
              }
            >
              <MultiSelectAdmins
                selectedAdmins={selectedAdmins}
                setSelectedAdmins={setSelectedAdmins}
              />
            </FormControl>

            <FormControl
              fullWidth
              sx={
                sendToOrgan
                  ? { mt: 0 }
                  : { mt: 0, height: '0', visibility: 'hidden' }
              }
            >
              <MultiSelectOrganization
                selectedOrganizations={selectedOrganizations}
                setSelectedOrganizations={setSelectedOrganizations}
              />
            </FormControl>

            <FormControl
              fullWidth
              sx={
                sendToMembers
                  ? { mt: 0 }
                  : { mt: 0, height: '0', visibility: 'hidden' }
              }
            >
              <MultiSelectMembers
                selectedMembers={selectedMembers}
                setSelectedMembers={setSelectedMembers}
              />
            </FormControl>

            <Button
              variant="contained"
              size="large"
              type="submit"
              sx={{ mt: 4, width: '100%' }}
            >
              {!loading ? (
                <>ارسال پیام</>
              ) : (
                <CircularProgress sx={{ color: 'white' }} size={24} />
              )}
            </Button>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
}
