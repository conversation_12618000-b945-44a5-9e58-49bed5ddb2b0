import { Box, Stack, Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import { DEFAULT_APPBAR_HEIGHT } from 'components/CustomAppBar/constants';
import { useLoaderData, useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { getChat, getChatParticipants } from 'apis/messenger';
import { setPathParam } from 'utils';
import { PATHS } from 'constants';
import TextProgress from 'components/TextProgress/TextProgress';
import { selectMyId } from 'store/auth';
import { useSelector } from 'react-redux';
import MyAppBar from './components/MyAppBar/MyAppBar';
import EditableGroupAvatar from './components/EditableGroupAvatar/EditableGroupAvatar';
import Participants from './components/Participants/Participants';
import Participant from './components/Participant/Participant';
import AddParticipant from './components/AddParticipant/AddParticipant';

export default function ChatInfo() {
  const [chatName, setChatName] = useState('');
  const chatId = useLoaderData();

  const { data: chatData } = useQuery(
    ['get-chat', chatId],
    async () => getChat({ chatId }),
    {
      initialData: { data: {} },
    },
  );

  const { isLoading: participantsLoading, data: participantsData } = useQuery(
    ['participants', chatId],
    () => getChatParticipants({ chatId }),
    { initialData: { data: [] } },
  );

  const chat = chatData.data;
  const chatAvatar = chat.display_avatar;

  useEffect(() => {
    setChatName(chat.display_name);
  }, []);

  const navigate = useNavigate();
  const buildChatPath = userId => setPathParam(PATHS.profile, 'userId', userId);

  const myId = useSelector(selectMyId);
  const isAdmin = chat?.admins?.includes(myId);

  return (
    <Box sx={{ height: '100vh' }}>
      <MyAppBar />

      <Stack
        sx={{ pt: DEFAULT_APPBAR_HEIGHT, height: '100%' }}
        alignItems="center"
      >
        <EditableGroupAvatar
          sx={{
            width: 128,
            height: 128,
            // mt: 3,
          }}
          avatar={!chatAvatar ? chat.display_avatar : chatAvatar}
          chatId={chatId}
          chatName={chatName}
          setChatName={setChatName}
          creator={chat.creator}
        />

        <Box
          sx={{
            mt: 3,
            background: 'white',
            width: '100%',
            borderTopLeftRadius: '8px',
            borderTopRightRadius: '8px',
            p: 2,
            flexGrow: 1,
            overflowY: 'scroll',
          }}
        >
          <Stack direction="row" justifyContent="space-between">
            <Typography
              sx={{
                fontSize: '16px',
                fontWeight: 'bold',
                marginTop: 'auto',
                marginBottom: 'auto',
              }}
            >
              اعضا
            </Typography>
            {isAdmin && <AddParticipant chatId={chatId} />}
          </Stack>

          <Participants chatId={chatId} sx={{ mt: 2 }}>
            {participantsLoading && <TextProgress />}
            {participantsData.data.map(participant => (
              <Participant
                key={participant.user_id}
                userId={participant.user_id}
                displayUsername={participant.display_username}
                firstName={participant.display_first_name}
                lastName={participant.display_last_name}
                avatar={participant.avatar}
                isBlocked={participant.is_blocked}
                onClick={() => {
                  navigate(buildChatPath(participant.user_id));
                }}
                chatId={chatId}
                admins={chat.admins}
                creator={chat.creator}
              />
            ))}
          </Participants>
        </Box>
      </Stack>
    </Box>
  );
}
