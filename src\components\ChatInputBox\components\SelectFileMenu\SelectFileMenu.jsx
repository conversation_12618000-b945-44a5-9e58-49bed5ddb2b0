import {
  ListItemIcon, ListItemText, Menu, MenuItem, MenuList,
} from '@mui/material';
import ImageOutlinedIcon from '@mui/icons-material/ImageOutlined';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';

export default function SelectFileMenu({
  anchorEl, setAnchorEl, open, onFileClick, onImageClick,
}) {
  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={() => setAnchorEl(null)}
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
    >
      <MenuList>
        <MenuItem>
          <ListItemIcon onClick={onImageClick}>
            <ImageOutlinedIcon />
          </ListItemIcon>
          <ListItemText>تصویر</ListItemText>
        </MenuItem>

        <MenuItem onClick={onFileClick}>
          <ListItemIcon><InsertDriveFileOutlinedIcon /></ListItemIcon>
          <ListItemText>فایل</ListItemText>
        </MenuItem>
      </MenuList>
    </Menu>
  );
}
