import {
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
} from '@mui/material';
import { CONTENT_CATEGORY } from 'constants';

export default function Category({ value, errors, disabled }) {
  return (
    <FormControl fullWidth sx={{ mt: 2 }}>
      <InputLabel>دسته بندی</InputLabel>
      <Select
        label="دسته بندی"
        name="category"
        defaultValue={value || 'O'}
        disabled={disabled}
        required
      >
        {CONTENT_CATEGORY.map((category) => (
          <MenuItem value={category.value} key={category.value}>
            {category.name}
          </MenuItem>
        ))}
      </Select>
      <FormHelperText>{errors}</FormHelperText>
    </FormControl>
  );
}
