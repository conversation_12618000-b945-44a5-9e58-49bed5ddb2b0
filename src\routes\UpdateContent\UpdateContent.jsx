import { useQuery } from 'react-query';
import { useLoaderData } from 'react-router-dom';
import { getContent } from '../../apis/content';
import ContentForm from '../../components/ContentForm/ContentForm';
import LoadingPage from '../../components/LoadingPage/LoadingPage';
import { PATHS } from '../../constants';

export async function loader({ params }) {
  return params.contentId;
}

export default function UpdateContent() {
  const contentId = useLoaderData();
  const query = useQuery([PATHS.content, contentId], () => getContent(contentId));

  return query.isLoading ? (
    <LoadingPage />
  ) : (
    <ContentForm content={query.data.data} />
  );
}
