import {
  Grid,
  Typography,
  styled,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Button,
  Pagination,
} from '@mui/material';
import dayjs from 'dayjs';
import { Circle, Delete, Edit, RemoveRedEye } from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Box } from '@mui/system';
import DeleteBottomSheet from '../../routes/Admin/AnnouncementList/components/DeleteBottomSheet/DeleteBottomSheet';
import { useIsDesktop } from '../../utils';
import LoadingPage from '../LoadingPage/LoadingPage';

function formatDate(date) {
  return dayjs(date).format('YYYY-MM-DD');
}

const CTableHead = styled(TableHead)(() => ({
  '& th': {
    fontSize: '14px',
    color: '#737373',
    border: 'none',
  },
}));
const CTableRow = styled(TableRow)(() => ({
  '& td': {
    border: 0,
    color: '#222222',
    fontSize: '16px',
  },
}));
// const CTextCell = styled(TableCell)(() => ({
//
// }));

function CTextCell({ sx, children }) {
  const truncateText = text => {
    if (typeof text === 'string' && text.length > 50) {
      return `${text.slice(0, 50)}...`;
    }
    return text;
  };

  return <TableCell sx={sx}>{truncateText(children)}</TableCell>;
}

function CImageCell({ sx, address }) {
  return (
    <TableCell sx={sx}>
      <img
        style={{ width: '60px', height: '60px', borderRadius: '100px' }}
        src={address || '/logo.png'}
        alt="image"
      />
    </TableCell>
  );
}

function CDateCell({ sx, date }) {
  // let convertedDate =

  return <TableCell sx={sx}>{formatDate(date)}</TableCell>;
}
function CStatusCell({ sx, data, color }) {
  return (
    <TableCell sx={sx}>
      <Circle style={{ marginLeft: '3px', fontSize: '10px', color }} />
      {data}
    </TableCell>
  );
}
function CActionCell({ sx, actions = [], basePath, onDelete, isDesktop, id }) {
  const btnStyle = {
    color: '#222',
    background: '#ECEDF7',
    width: isDesktop ? '35px' : 'calc(33% - 10px)',
    minWidth: '35px',
    height: '35px',
    margin: '0 5px',
    boxShadow: 'none',
    '&:hover': {
      background: '#E7EAF4',
    },
  };

  const actionButtons = [
    {
      action: 'view',
      icon: <RemoveRedEye sx={{ fontSize: '14px' }} />,
      link: !!basePath && basePath !== '#' ? `/${basePath}/view/${id}` : '#',
    },
    // { action: 'view', icon: <RemoveRedEye sx={{ fontSize: '14px' }} />, link: '#' },
    {
      action: 'edit',
      icon: <Edit sx={{ fontSize: '14px' }} />,
      link: basePath ? `/${basePath}/${id}/update/` : '#',
    },
    // { action: 'edit', icon: <Edit sx={{ fontSize: '14px' }} />, link: '#' },
    {
      action: 'delete',
      icon: <Delete sx={{ fontSize: '14px' }} />,
      onClick: () => onDelete(id),
    },
  ];

  return (
    <TableCell
      sx={sx}
      colSpan={isDesktop ? 1 : 2}
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      {actionButtons.map(
        ({ action, icon, link, onClick }, index) =>
          actions.includes(action) && (
            <Link key={action} to={link} style={{ textDecoration: 'none' }}>
              <Button
                variant="contained"
                size="small"
                sx={btnStyle}
                onClick={onClick}
              >
                {icon}
              </Button>
            </Link>
          ),
      )}
    </TableCell>
  );
}

const CustomPagination = styled(Pagination)({
  '& .MuiPaginationItem-root': {
    color: '#333',
    border: 'none',
  },
  '& .MuiPaginationItem-page.Mui-selected': {
    backgroundColor: '#0FA6A1',
    color: 'white',
  },
  '& .MuiPaginationItem-ellipsis': {
    color: '#333',
  },
});
export default function CTable({
  normalizer,
  fetchData,
  deleteData,
  colTitles,
  searchValue,
  showPagination = true,
  basePath,
  sx,
}) {
  const isDesktop = useIsDesktop();

  const [showDeleteBottomSheet, setShowDeleteBottomSheet] = useState(false);
  const [tableIsLoading, setTableIsLoading] = useState(true);
  const [idToDelete, setIdToDelete] = useState(null);
  const [page, setPage] = useState(1);
  const [count, setCount] = useState(0);
  const [rows, setRows] = useState([]);
  const tableRef = useRef(null);
  const rowsPerPage = 10;
  const [filteredRows, setFilteredRows] = useState([]);
  const debounceTimeout = useRef(null);

  useEffect(() => {
    fetchData({ page: 1, page_size: rowsPerPage }).then(newData => {
      setCount(newData?.data?.count || newData?.data?.length || 0);

      setRows(normalizer(newData.data));
      if (newData?.data?.length > 0 || newData?.data?.count > 0) {
        setRows(normalizer(newData.data));
      }
      setTableIsLoading(false);
    });
  }, []);

  const filterRows = useCallback(() => {
    if (!searchValue || searchValue === '') return setFilteredRows(rows);
    setFilteredRows(
      rows.filter(item =>
        item.cols?.some(
          col =>
            typeof col.value === 'string' &&
            col.value.toLowerCase().includes(searchValue.toLowerCase()),
        ),
      ),
    );
  }, [rows, searchValue]);

  useEffect(() => {
    clearTimeout(debounceTimeout.current);
    debounceTimeout.current = setTimeout(() => {
      filterRows();
    }, 300); // Adjust debounce time as needed
    return () => clearTimeout(debounceTimeout.current);
  }, [searchValue, filterRows]);

  const displayedRows = useMemo(() => filteredRows, [filteredRows]);

  const handleDelete = id => {
    setShowDeleteBottomSheet(true);
    setIdToDelete(id);
  };

  const handlePageChange = (event, value) => {
    setTableIsLoading(true);
    setPage(value);
    fetchData({ page: value, page_size: rowsPerPage }).then(newData => {
      setRows(normalizer(newData?.data || []));
      setTableIsLoading(false);
    });
  };

  const handleScroll = useCallback(() => {
    setTableIsLoading(true);
    const tableElement = tableRef.current;
    if (tableElement) {
      const { scrollTop, offsetHeight, scrollHeight } = tableElement;
      if (scrollTop + offsetHeight >= scrollHeight - 2) {
        fetchData({ page: page + 1, page_size: rowsPerPage }).then(newData => {
          newData = normalizer(newData?.data?.results || []);
          setRows(prevRows => [...prevRows, ...newData]);
          setPage(prevPage => prevPage + 1);
          setTableIsLoading(false);
        });
      }
    }
  }, [page, rowsPerPage, fetchData]);

  useEffect(() => {
    if (!isDesktop) {
      const tableElement = tableRef.current;
      if (tableElement) {
        tableElement.addEventListener('scroll', handleScroll);
        return () => {
          tableElement.removeEventListener('scroll', handleScroll);
        };
      }
    }
  }, [isDesktop, handleScroll]);

  if (tableIsLoading) return <LoadingPage />;

  return (
    <>
      {isDesktop ? (
        <Grid xs={12} sx={sx}>
          <TableContainer>
            <Table>
              <CTableHead>
                <TableRow>
                  {colTitles.map((title, index) => (
                    <TableCell key={index}>{title}</TableCell>
                  ))}
                </TableRow>
              </CTableHead>
              <TableBody>
                {displayedRows.map(row => (
                  <CTableRow key={row.id}>
                    {row.cols.map(col => {
                      if (col.type === 'text') {
                        return <CTextCell>{col.value}</CTextCell>;
                      }
                      if (col.type === 'img') {
                        return <CImageCell address={col.value} />;
                      }
                      if (col.type === 'date') {
                        return <CDateCell date={col.value} />;
                      }
                      if (col.type === 'status') {
                        return (
                          <CStatusCell data={col.value} color={col.color} />
                        );
                      }
                      if (col.type === 'action') {
                        return (
                          <CActionCell
                            actions={col.actions}
                            basePath={basePath}
                            onDelete={handleDelete}
                            id={row.id}
                          />
                        );
                      }
                    })}
                  </CTableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          {showPagination && (
            <Box display="flex" justifyContent="center" mt={2}>
              <CustomPagination
                count={Math.ceil((count || 0) / rowsPerPage)}
                page={page}
                onChange={handlePageChange}
                variant="outlined"
                shape="rounded"
                siblingCount={1}
                boundaryCount={1}
              />
            </Box>
          )}
        </Grid>
      ) : (
        <Grid xs={12} sx={sx} px={0}>
          {rows.map(row => (
            <Box
              sx={{
                display: 'flex',
                padding: '10px 0',
                borderBottom: '1px solid #676B7E',
              }}
            >
              <Table ref={tableRef}>
                <TableBody>
                  {row.cols.map((col, index) => {
                    if (col.type === 'action') {
                      return (
                        <CActionCell
                          actions={col.actions}
                          basePath={basePath}
                          onDelete={handleDelete}
                          id={row.id}
                          isDesktop={isDesktop}
                          sx={{
                            padding: '10px 0',
                            textAlign: 'center',
                            borderBottom: 'none',
                          }}
                        />
                      );
                    }
                    return (
                      <CTableRow id={row.id}>
                        <CTextCell sx={{ padding: '10px 0' }}>
                          <Typography
                            sx={{ color: '#737373', fontSize: '14px' }}
                          >
                            {colTitles[index]}
                          </Typography>
                        </CTextCell>
                        {col.type === 'text' && (
                          <CTextCell
                            sx={{ padding: '10px 0', textAlign: 'right' }}
                          >
                            {col.value}
                          </CTextCell>
                        )}
                        {col.type === 'img' && (
                          <CImageCell
                            address={col.value}
                            sx={{ padding: '10px 0', textAlign: 'right' }}
                          />
                        )}
                        {col.type === 'date' && (
                          <CDateCell
                            sx={{ padding: '10px 0', textAlign: 'right' }}
                            date={col.value}
                          />
                        )}
                        {col.type === 'status' && (
                          <CStatusCell
                            sx={{ padding: '10px 0', textAlign: 'right' }}
                            data={col.value}
                            color={col.color}
                          />
                        )}
                      </CTableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </Box>
          ))}
        </Grid>
      )}
      <DeleteBottomSheet
        show={showDeleteBottomSheet}
        hideBottomSheet={() => setShowDeleteBottomSheet(false)}
        onPrimary={async () => {
          if (idToDelete) await deleteData(idToDelete);
          // todo: refresh table
          setPage(page);
          return window.location.reload();
        }}
      />
    </>
  );
}
