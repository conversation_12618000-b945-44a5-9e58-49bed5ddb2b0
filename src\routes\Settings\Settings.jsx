import { Stack, Typography } from '@mui/material';
import { useQuery } from 'react-query';
import { getMe, GET_ME_URL } from 'apis/auth';
import LoadingPage from 'components/LoadingPage/LoadingPage';
import { APP_VERSION } from 'constants';
import { useIsDesktop } from 'utils';
import DesktopAppBar from 'components/DesktopAppBar/DesktopAppBar';
import EditableAvatar from './components/EditableAvatar/EditableAvatar';
import UserInfo from './components/UserInfo/UserInfo';
import AccountActions from './components/AccountActions/AccountAction';
import BugReport from './components/BugReport/BugReport';

export default function Settings() {
  const { isLoading, data } = useQuery(GET_ME_URL, () => getMe());
  const isDesktop = useIsDesktop();

  if (isLoading) {
    return <LoadingPage />;
  }
  const me = data.data;

  return (
    <>
      {isDesktop && <DesktopAppBar />}

      <Stack
        sx={{
          height: '100%',
          overflowY: 'scroll',
          pt: 3,
          pb: 3,
        }}
        alignItems="center"
        className="no-scrollbar"
      >
        <EditableAvatar avatar={me.avatar} userId={me.id} />

        <UserInfo
          sx={{ mt: 3 }}
          userId={me.id}
          firstName={me.first_name}
          lastName={me.last_name}
          displayUsername={me.display_username}
          username={me.username}
          bio={me.bio}
        />

        <AccountActions sx={{ mt: 3 }} />

        <BugReport sx={{ mt: 3 }} />

        <Typography sx={{ color: 'secondary.dark' }}>
          ویرایش {APP_VERSION}
        </Typography>
      </Stack>
    </>
  );
}
