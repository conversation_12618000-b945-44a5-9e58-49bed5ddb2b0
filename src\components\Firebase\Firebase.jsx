import { useEffect } from 'react';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';
import { initializeApp } from 'firebase/app';
import { addPushToken } from 'apis/notifier';
import { isLoggedIn } from 'utils';
import { useDispatch } from 'react-redux';
import { setHasUnreadMessage } from 'store/messenger';

function requestPermission(messaging) {
  if ('Notification' in window) {
    Notification.requestPermission().then((permission) => {
      if (permission === 'granted') {
        getToken(
          messaging,
          { vapidKey: 'BBZ5NTatoJ1ebNbId9vTRj_ahz1HhDjjHjD2iVPOkp0qmdpFdh8cvyeclhjejTpPab9coL_1CKGrDFvX9ch7yfU' },
        ).then((token) => {
          if (token && isLoggedIn()) {
            addPushToken({ token });
          }
        });
      }
    });
  }
}

export default function Firebase() {
  const dispatch = useDispatch();

  useEffect(() => {
    const firebaseConfig = {
      apiKey: 'AIzaSyBPRRhvxvH--tIt0ZsDlEJuyIaCh6q-WT8',
      authDomain: 'haghighatgram-8901e.firebaseapp.com',
      projectId: 'haghighatgram-8901e',
      storageBucket: 'haghighatgram-8901e.appspot.com',
      messagingSenderId: '514478643516',
      appId: '1:514478643516:web:26289494ff45b3b1372639',
      measurementId: 'G-25RB2EV635',
    };

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const messaging = getMessaging(app);
    requestPermission(messaging);

    onMessage(messaging, () => {
      dispatch(setHasUnreadMessage({ hasUnreadMessage: true }));
    });
  }, []);
}
