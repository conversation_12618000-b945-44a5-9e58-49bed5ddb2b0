import { IconButton } from '@mui/material';
import KeyOutlinedIcon from '@mui/icons-material/KeyOutlined';
import { useState } from 'react';
import MakeAdminBottomSheet from '../MakeAdminBottomSheet/MakeAdminBottomSheet';

export default function MakeAdminButton({ chatId, userId }) {
  const [showBS, setShowBS] = useState(false);

  return (
    <>
      <IconButton onClick={() => setShowBS(true)}><KeyOutlinedIcon /></IconButton>
      <MakeAdminBottomSheet
        show={showBS}
        hideBottomSheet={() => setShowBS(false)}
        chatId={chatId}
        userId={userId}
      />
    </>
  );
}
