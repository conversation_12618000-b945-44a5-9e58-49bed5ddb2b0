import {
  Box,
  Button,
  Divider,
  FormControl,
  Grid,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { useMutation, useQueryClient } from 'react-query';
import { useIsDesktop } from 'utils';
import ContentPasteOutlinedIcon from '@mui/icons-material/ContentPasteOutlined';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import File from '../../../components/ContentForm/components/File/File';
import MyBackdrop from '../../../components/ContentForm/components/MyBackdrop/MyBackdrop';
import CircularProgressWithValue from '../../../components/CircularProgressWithValue/CircularProgressWithValue';
import { setSnackbar } from '../../../store/layout';
import { selectMe } from '../../../store/auth';
import uploadFile from '../../../apis/storage';
import OrganizationSelect from './components/OrganizationSelect';
import { createOrganization } from '../../../apis/organization';

export default function OrganizationCreate() {
  const isDesktop = useIsDesktop();
  const location = useLocation();

  const [content, setContent] = useState({
    selectedOrganization: location?.state?.selectedOrganization || [],
    name: location?.state?.name,
    description: location?.state?.description,
  });

  const descriptionRef = useRef(null);
  const formRef = useRef(null);

  const [filePreview, setFilePreview] = useState(null);
  const [fileType, setFileType] = useState(undefined);

  const [selectedMembers, setSelectedMembers] = useState([]);
  const [selectedOrganization, setSelectedOrganization] = useState(
    content.selectedOrganization || {},
  );

  const navigate = useNavigate();

  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();
  const abortController = useRef(null);
  const mutation = useMutation(
    async ({ event }) => {
      setLoading(true);

      const { name, description, file } = event.target;

      if (name.value === '') {
        throw { message: 'نام سازمان اجباری است', severity: 'error' };
      } else if (!selectedOrganization.id) {
        throw { message: 'سازمان مرجع را انتخاب کنید', severity: 'error' };
      }

      let uploadedFile;

      if (file?.files[0])
        uploadedFile = await uploadFile(file.files[0], setProgress);

      const data = {
        name: name.value,
        description: description.value || '',
        parent: selectedOrganization?.id,
      };

      if (uploadedFile?.data?.id) data.avatar = uploadedFile.data.id;
      if (selectedMembers.length > 0) data.admins = setSelectedMembers();

      return createOrganization(data, setProgress, abortController.current);
    },
    {
      onSuccess: async data => {
        dispatch(
          setSnackbar({
            message: 'سازمان با موفقیت ایجاد شد',
            severity: 'success',
          }),
        );
        localStorage.setItem(
          'notification',
          JSON.stringify({
            message:
              'درخواست شما ثبت شد و سیستم درحال پردازش درخواست شماست. این تغییر دقایقی زمان خواهد برد. بعد از چند دقیقه صفحه را مجدد بارگذاری نمایید.',
            severity: 'success',
            timestamp: Date.now(), // Optional: to manage message expiration
          }),
        );

        setProgress(0);
        setLoading(false);
        formRef.current.reset();
        setSelectedMembers([]);
        setSelectedOrganization({});
        navigate('/admin-panel/organization/graph');
      },
      onError: error => {
        if (error?.response?.status === 400) {
          dispatch(
            setSnackbar({ message: 'خطا در ایجاد سازمان', severity: 'error' }),
          );
        } else dispatch(setSnackbar(error));

        setProgress(0);
        setLoading(false);
      },
    },
  );

  const errors = mutation.error?.response?.data || {};

  const submitForm = event => {
    abortController.current = new AbortController();
    event.preventDefault();
    mutation.mutate({ event });
  };

  const getFilePreview = () => {
    if (filePreview) return URL.createObjectURL(filePreview);
    if (content.preview) return content.preview;
    return null;
  };

  useEffect(
    () => () => {
      abortController.current?.abort();
    },
    [],
  );
  const cancelCreate = () => {
    abortController.current.abort();
  };

  const pasteDescription = async () => {
    descriptionRef.current.value = await navigator.clipboard.readText();
  };

  const me = useSelector(selectMe);

  const DIVIDER_MY = 3;

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        overflowY: 'scroll',
        mt: 2,
      }}
    >
      <form onSubmit={submitForm} ref={formRef}>
        <Grid container columnSpacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} lg={4}>
            <File
              file={content.avatar}
              fileType={content.file_type}
              errors={errors.avatar}
              disabled={mutation.isLoading}
              setPreview={setFilePreview}
              preview={getFilePreview()}
              setFileType={setFileType}
              title="بارگذاری لوگو سازمان"
              ratio={1}
            />
          </Grid>

          <Grid item xs={12} lg={8} sx={{ mt: isDesktop ? 0 : 2 }}>
            <TextField
              variant="outlined"
              required
              label="نام سازمان"
              name="name"
              defaultValue={content.name}
              helperText={errors.name}
              error={!!errors.name}
              disabled={mutation.isLoading}
              inputProps={{ maxLength: 100 }}
              fullWidth
            />

            <TextField
              sx={{ mt: 2 }}
              label="توضیحات"
              name="description"
              defaultValue={content.description}
              variant="outlined"
              multiline
              rows={3}
              helperText={errors.description}
              disabled={mutation.isLoading}
              fullWidth
              inputRef={descriptionRef}
              InputProps={{
                endAdornment: (
                  <IconButton edge="end" onClick={pasteDescription}>
                    <ContentPasteOutlinedIcon />
                  </IconButton>
                ),
              }}
            />

            <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

            <Typography sx={{ fontSize: 16, fontWeight: 'bold' }}>
              سازمان مرجع
            </Typography>

            <FormControl fullWidth sx={{ mt: 2 }}>
              <OrganizationSelect
                selectedOrganization={selectedOrganization}
                setSelectedOrganization={setSelectedOrganization}
              />
            </FormControl>

            <Button
              variant="contained"
              size="large"
              type="submit"
              sx={{ mt: 2, width: '100%' }}
            >
              ذخیره
            </Button>
          </Grid>
        </Grid>
      </form>

      <MyBackdrop open={loading}>
        <Stack spacing={2}>
          <CircularProgressWithValue
            variant="determinate"
            value={progress}
            color="inherit"
            size={84}
          />
          <Button
            color="error"
            variant="contained"
            disableElevation
            startIcon={<HighlightOffIcon />}
            onClick={cancelCreate}
          >
            لغو
          </Button>
        </Stack>
      </MyBackdrop>
    </Box>
  );
}
