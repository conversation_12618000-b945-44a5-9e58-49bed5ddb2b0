import { MESSAGE_TYPES, MESSAGE_DELIVER_STATUS } from 'constants';
import { memo, useEffect } from 'react';
import useSendMessageRead from 'hooks/useSendMessageRead';
import StatusMessage from './components/StatusMessage/StatusMessage';
import TextMessage from './components/TextMessage/TextMessage';
import FileMessage from './components/FileMessage/FileMessage';
import MediaMessage from './components/MediaMessage/MediaMessage';
import AudioMessage from './components/AudioMessage/AudioMessage';
import ContentMessage from './components/ContentMessage/ContentMessage';
import CallMessage from './components/CallMessage/CallMessage';

function shouldSendReadStatus(message, myId) {
  const notAlreadyRead = message.deliveryStatus !== MESSAGE_DELIVER_STATUS.READ;
  const notSendByMe = message.author.id !== myId;

  return notSendByMe && notAlreadyRead;
}

function Message({
  message, myId, withAvatar, isAdmin,
}) {
  const sendMessageRead = useSendMessageRead();
  useEffect(() => {
    if (shouldSendReadStatus(message, myId)) {
      sendMessageRead(message.deliveryToken);
    }
  }, []);

  if (message.type === MESSAGE_TYPES.GROUP_CREATE) {
    return (
      <StatusMessage
        message={message}
        isAdmin={isAdmin}
      />
    );
  }

  if (message.type === MESSAGE_TYPES.EVENT) {
    return (
      <StatusMessage
        message={message}
        isAdmin={isAdmin}
      />
    );
  }

  if (message.type === MESSAGE_TYPES.TEXT) {
    return (
      <TextMessage
        message={message}
        myId={myId}
        withAvatar={withAvatar}
        isAdmin={isAdmin}
      />
    );
  }

  if (message.type === MESSAGE_TYPES.FILE) {
    return (
      <FileMessage
        myId={myId}
        message={message}
        withAvatar={withAvatar}
        isAdmin={isAdmin}
      />
    );
  }

  if (message.type === MESSAGE_TYPES.IMAGE || message.type === MESSAGE_TYPES.VIDEO) {
    return (
      <MediaMessage
        myId={myId}
        message={message}
        withAvatar={withAvatar}
        isAdmin={isAdmin}
      />
    );
  }

  if (message.type === MESSAGE_TYPES.AUDIO) {
    return (
      <AudioMessage
        myId={myId}
        message={message}
        withAvatar={withAvatar}
        isAdmin={isAdmin}
      />
    );
  }

  if (message.type === MESSAGE_TYPES.CONTENT) {
    return (
      <ContentMessage
        myId={myId}
        message={message}
        withAvatar={withAvatar}
        isAdmin={isAdmin}
      />
    );
  }

  if (message.type === MESSAGE_TYPES.CALL) {
    return (
      <CallMessage
        message={message}
      />
    );
  }

  return '';
}

export default memo(Message);
