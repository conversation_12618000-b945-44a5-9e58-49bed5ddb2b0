import { Menu } from '@mui/material';
import { useState } from 'react';
import DeleteMessageMenuItem from '../DeleteMessageMenuItem/DeleteMessageMenuItem';
import DeleteMessageBottomSheet from '../DeleteMessageBottomSheet/DeleteMessageBottomSheet';

export default function DeleteMenu(props) {
  const {
    open,
    anchorEl,
    handleClose,
    message,
    isAdmin,
  } = props;

  const [showDeleteBottomSheet, setShowDeleteBottomSheet] = useState(false);
  const onDeleteMessageClick = () => {
    handleClose();
    setShowDeleteBottomSheet(true);
  };

  return (
    <>
      <Menu
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'center',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
      >

        {(isAdmin) && (
          <DeleteMessageMenuItem
            onClick={onDeleteMessageClick}
          />
        )}
      </Menu>

      <DeleteMessageBottomSheet
        show={showDeleteBottomSheet}
        hideBottomSheet={() => setShowDeleteBottomSheet(false)}
        messageDeliveryToken={message.deliveryToken}
      />
    </>
  );
}
