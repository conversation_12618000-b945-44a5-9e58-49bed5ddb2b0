import { Stack } from '@mui/material';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import { useState } from 'react';

export default function ConfirmBottomSheet({
  title,
  message,
  show,
  hideBottomSheet,
  onPrimary,
}) {
  const [loading, setLoading] = useState(false);
  const hideWhileNotLoading = () => !loading && hideBottomSheet();

  return (
    <BottomSheet
      title={title}
      hideBottomSheet={hideWhileNotLoading}
      show={show}
    >
      <BottomSheetMessage>
        {message}
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton
          loading={loading}
          onClick={async () => {
            setLoading(true);
            await onPrimary();
            hideBottomSheet();
            setLoading(false);
          }}
        >
          بله
        </BottomSheetPrimaryButton>

        <BottomSheetSecondaryButton
          onClick={hideWhileNotLoading}
        >
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
