import { But<PERSON>, Stack, Typography } from '@mui/material';
import { useMutation, useQuery } from 'react-query';
import { getMe, GET_ME_URL } from 'apis/auth';
import LoadingPage from 'components/LoadingPage/LoadingPage';
import { APP_VERSION, INTERACTION_RESPONSE_TYPES, PATHS } from 'constants';
import { useIsDesktop } from 'utils';
import DesktopAppBar from 'components/DesktopAppBar/DesktopAppBar';
import { Link } from 'react-router-dom';
import ImportContactsIcon from '@mui/icons-material/ImportContacts';
import { useState } from 'react';
import dayjs from 'dayjs';
import EditableAvatar from './components/EditableAvatar/EditableAvatar';
import UserInfo from './components/UserInfo/UserInfo';
import AccountActions from './components/AccountActions/AccountAction';
import BugReport from './components/BugReport/BugReport';
import IconSax from '../../components/IconSax/IconSax';
import MyLink from '../../components/MyLink/MyLink';
import LoadingButton from '../../components/LoadingButton/LoadingButton';
import {
  getAnnouncements,
  getMyAnnouncements,
  getMyAnnouncementsUnreadCount,
  readAnnouncement,
  updateAnnouncement,
} from '../../apis/announcement';
import {
  createInteractionResponse,
  updateInteractionResponse,
} from '../../apis/content';
import { setSnackbar } from '../../store/layout';

export default function Announcement() {
  const { isLoading, data } = useQuery(GET_ME_URL, () => getMe());
  const isDesktop = useIsDesktop();

  const [announcements, setAnnouncement] = useState([]);
  const [announceIsLoading, setAnnounceIsLoading] = useState(true);
  const [loadingStates, setLoadingStates] = useState([]);

  const { isLoading: countLoading, data: countData } = useQuery(
    ['unreadAnnounce'],
    () => getMyAnnouncementsUnreadCount(),
  );
  const unread =
    countLoading && !!countData ? 0 : countData?.data?.unread_number || 0;

  useQuery({
    queryKey: ['GetAnnouncementsKey'],
    queryFn: () => getMyAnnouncements(),
    onSuccess: data => {
      if (data.status === 200) {
        setAnnouncement(data?.data?.results || []);
        setLoadingStates(Array(data.data?.results?.length).fill(false));
      }
      setAnnounceIsLoading(false);
    },
    onSettled: () => setAnnounceIsLoading(false),
  });

  const readAnnounce = useMutation(async id => {
    await readAnnouncement(id);
  });

  const handleReadClick = (index, id) => {
    const newLoadingStates = [...loadingStates];
    newLoadingStates[index] = true;
    setLoadingStates(newLoadingStates);

    readAnnounce.mutate(id, {
      onSuccess: () => {
        const updatedAnnouncements = [...announcements];
        const updatedLoadingStates = [...newLoadingStates];
        updatedLoadingStates[index] = false;
        updatedAnnouncements[index].has_read = true;
        setAnnouncement(updatedAnnouncements);
        setLoadingStates(updatedLoadingStates);
      },
      onError: () => {
        const updatedLoadingStates = [...newLoadingStates];
        updatedLoadingStates[index] = false;
        setLoadingStates(updatedLoadingStates);
      },
    });
  };

  if (isLoading || announceIsLoading) {
    return <LoadingPage />;
  }

  return (
    <>
      {isDesktop && <DesktopAppBar />}

      <Stack
        sx={{
          height: '100%',
          overflowY: 'scroll',
          pt: 3,
          pb: 3,
        }}
        alignItems="center"
      >
        {unread !== 0 ? (
          <Stack
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'middle',
              width: '100%',
              height: isDesktop ? '65px' : '45px',
              backgroundColor: '#FAE1E5',
              pl: '10px',
              boxShadow: '0px 2px 20px 0px #00000012',
              borderRadius: '8px',
            }}
          >
            <Typography
              sx={{
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'center',
                height: '100%',
                fontSize: '16px',
                fontWeight: '500',
              }}
            >
              <IconSax sx={{ height: '100%', mr: '5px' }} name="bell" />
              <span> {unread} پیام خوانده نشده</span>
            </Typography>
          </Stack>
        ) : (
          <Stack
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'middle',
              width: '100%',
              height: isDesktop ? '65px' : '45px',
              backgroundColor: '#CCDEFF',
              pl: '10px',
              boxShadow: '0px 2px 20px 0px #00000012',
              borderRadius: '8px',
            }}
          >
            <Typography
              sx={{
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'center',
                height: '100%',
                fontSize: '16px',
                fontWeight: '500',
              }}
            >
              <IconSax sx={{ height: '100%', mr: '5px' }} name="bell" />
              <span> همه اعلانات خوانده شده است</span>
            </Typography>
          </Stack>
        )}

        {announcements.map((item, index) => (
          <Stack
            sx={{
              display: 'flex',
              gap: '10px',
              width: '100%',
              mt: '30px',
              boxShadow: '0px 2px 20px 0px #00000012',
              background: '#fff',
              px: '20px',
              py: '20px',
              textAlign: 'left',
              borderRadius: '8px',
            }}
          >
            <Typography
              sx={{ color: '#00000080', fontSize: '14px', textAlign: 'left' }}
            >
              اعلان شماره
              {item.id}
            </Typography>
            {item.banner && (
              <img
                src={item.banner || '#'}
                alt="image"
                style={{
                  width: '100%',
                  borderRadius: '8px',
                  maxWidth: '400px',
                }}
              />
            )}
            <Typography sx={{ fontSize: '16px', fontWeight: '700' }}>
              {item.title}
            </Typography>
            <Typography
              sx={{ color: '#00000080', fontSize: '14px', textAlign: 'left' }}
            >
              {dayjs(item.created_at).format('HH:mm  YYYY-MM-DD')}
            </Typography>
            <Typography
              sx={{ fontWeight: 400, fontSize: '14px', textAlign: 'left' }}
            >
              {item.body}
            </Typography>

            <Stack
              sx={{
                display: 'flex',
                flexDirection: 'row',
                width: '100%',
                justifyContent: 'space-between',
              }}
            >
              <LoadingButton
                variant={item.has_read ? 'outline' : 'contained'}
                size="small"
                sx={{ mt: 3 }}
                // fullWidth
                onClick={
                  item.has_read ? null : () => handleReadClick(index, item.id)
                }
                loading={loadingStates[index]}
              >
                <ImportContactsIcon fontSize="12" sx={{ mr: '10px' }} />
                {item.has_read ? 'مطالعه شده' : 'مطالعه کردم'}
              </LoadingButton>
            </Stack>
          </Stack>
        ))}
      </Stack>
    </>
  );
}
