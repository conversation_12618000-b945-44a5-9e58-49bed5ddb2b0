import { Stack, SvgIcon, Typography } from '@mui/material';
import { PATHS } from 'constants';
import { useNavigate } from 'react-router-dom';
import ArrowLeftIcon from '@mui/icons-material/ArrowBackIos';
import { ReactComponent as CrownIcon } from 'static/icons/crown.svg';
import MyAvatar from '../../../../components/MyAvatar/MyAvatar';
import EditProfile from '../EditProfile/EditProfile';
import ProfileMenu from '../ProfileMenu/ProfileMenu';
import { useIsDesktop } from '../../../../utils';
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined';

const FONT_SIZE = '14px';

function Field({ title, value, sx }) {
  return (
    <Stack direction="row" sx={sx}>
      <Typography
        sx={{ fontSize: FONT_SIZE, fontWeight: 400, color: '#2C2C2E' }}
      >
        {title}:
      </Typography>
      <Typography
        sx={{
          fontSize: FONT_SIZE,
          fontWeight: 500,
          color: '#2C2C2E',
          ml: 1,
        }}
      >
        {value}
      </Typography>
    </Stack>
  );
}

export default function UserDetail({
  userId,
  avatar,
  firstName,
  lastName,
  username,
  bio,
  city,
  state,
  isMe,
  isAdmin = false,
  directChatId,
}) {
  const hasName = !!firstName || !!lastName;
  const fullName = hasName ? `${firstName} ${lastName}` : 'بدون نام';

  const isDesktop = useIsDesktop();
  const navigate = useNavigate();

  return (
    <>
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <MyAvatar
          avatar={avatar}
          fullName={fullName}
          username={username}
          withFullScreen
        />
        <Stack direction="row" justifyContent="left">
          {/* {!isDesktop && isMe && isAdmin */}
          {/*  && (<AdminButton />)} */}
          {isMe ? (
            <EditProfile />
          ) : (
            <ProfileMenu directChatId={directChatId} userId={userId} />
          )}
        </Stack>
      </Stack>

      <Field title="موقعیت" value={`${state} - ${city}`} sx={{ mt: 1 }} />
      {bio && (
        <Typography sx={{ mt: 2, fontSize: FONT_SIZE, whiteSpace: 'pre-line' }}>
          {bio}
        </Typography>
      )}

      {isDesktop && isMe && (
        <Stack sx={{ mt: 5 }}>
          {/* <Stack
            sx={{
              marginTop: '20px',
              padding: '10px 0',
              borderTop: '1px solid #E5E7F0',
              borderBottom: '1px solid #E5E7F0',
              cursor: 'pointer',
            }}
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            onClick={() => navigate(PATHS.admin.homepage)}
          >
            <Stack direction="row" justifyContent="left" alignItems="center">
              <SearchOutlinedIcon
                sx={{ width: '40px', height: '40px', padding: '8px' }}
              />
              <Typography sx={{ fontSize: '14px' }}>گزارش آماری</Typography>
            </Stack>
            <Stack direction="row" justifyContent="right" alignItems="center">
              <ArrowLeftIcon sx={{ fontSize: '18px', color: '#757575' }} />
            </Stack>
          </Stack> */}
          {isAdmin && (
            <Stack
              sx={{
                padding: '10px 0',
                borderTop: '1px solid #E5E7F0',
                borderBottom: '1px solid #E5E7F0',
                cursor: 'pointer',
              }}
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              onClick={() => navigate(PATHS.admin.homepage)}
            >
              <Stack direction="row" justifyContent="left" alignItems="center">
                <SvgIcon
                  component={CrownIcon}
                  sx={{ width: '40px', height: '40px', padding: '8px' }}
                />
                <Typography sx={{ fontSize: '14px' }}>پنل مدیریت</Typography>
              </Stack>
              <Stack direction="row" justifyContent="right" alignItems="center">
                <ArrowLeftIcon sx={{ fontSize: '18px', color: '#757575' }} />
              </Stack>
            </Stack>
          )}
        </Stack>
      )}
    </>
  );
}
