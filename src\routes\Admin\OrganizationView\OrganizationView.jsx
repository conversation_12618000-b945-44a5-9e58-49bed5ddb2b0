import {
  Box,
  IconButton,
  Stack,
  SvgIcon,
  Tooltip,
  Tabs,
  Typography,
} from '@mui/material';
import { useIsDesktop } from 'utils';
import { useState } from 'react';
import { Link, useLoaderData, useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { useDispatch } from 'react-redux';
import { ReactComponent as BellSimpleIcon } from 'static/icons/BellSimple.svg';
import { ReactComponent as ChatTextIcon } from 'static/icons/ChatText.svg';
import { PATHS } from '../../../constants';
import AdminsSection from './components/AdminsSection/AdminsSection';
import MembersSection from './components/MembersSection/MembersSection';
import LoadingPage from '../../../components/LoadingPage/LoadingPage';
import {
  deleteOrganization,
  getOrganization,
} from '../../../apis/organization';
import BottomSheetMessage from '../../../components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from '../../../components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from '../../../components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import BottomSheet from '../../../components/BottomSheet/BottomSheet';
import { setSnackbar } from '../../../store/layout';

export async function loader({ params }) {
  return params.organizationId;
}

export default function OrganizationView() {
  const isDesktop = useIsDesktop();

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const organizationId = useLoaderData();
  const { isLoading, data } = useQuery(
    [PATHS.admin.organizationView, organizationId],
    () => getOrganization(organizationId),
  );

  const rootPath = organization => {
    if (!organization) return null;
    return (
      <>
        {rootPath(organization.parent)}

        <Link
          style={{
            textDecoration: 'none',
            color: '#0FA6A1',
            marginRight: '3px',
          }}
          to={`/admin-panel/organization/view/${organization?.id}`}
        >
          {` / ${organization?.name}`}
        </Link>
      </>
    );
  };
  const organization = isLoading ? {} : data?.data;

  const tabs = [
    {
      value: 'tab1',
      label: 'لیست اعضا',
    },
  ];
  const [selectedTab, setSelectedTab] = useState(tabs[0].value);
  const [showDeleteOrganizationBox, setShowDeleteOrganizationBox] = useState(false);

  if (isLoading) {
    return <LoadingPage />;
  }

  return (
    <>
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingBottom: '15px',
          borderBottom: '1px solid #ccc',
        }}
      >
        <Box style={{ display: 'flex', flexDirection: 'row', flex: 1 }}>
          <Box
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              gap: '10px',
            }}
          >
            <img
              style={{ width: '70px', height: '70px', borderRadius: '1000px' }}
              src={organization.avatar || '/logo.png'}
              alt=""
            />
            <Box style={{ display: 'flex', flexDirection: 'column', flex: 1 }}>
              <Typography
                variant="h5"
                sx={{ fontWeight: 700, fontSize: '18px' }}
              >
                {organization.name}
              </Typography>
              <Typography
                variant="h6"
                sx={{ fontWeight: 400, fontSize: '16px' }}
              >
                {organization.description}
              </Typography>
              {organization.parent && (
                <Typography
                  variant="h6"
                  sx={{ fontWeight: 400, fontSize: '13px' }}
                >
                  {/* <Link */}
                  {/*  style={{ */}
                  {/*    textDecoration: 'none', */}
                  {/*    color: '#0FA6A1', */}
                  {/*    marginRight: '3px', */}
                  {/*  }} */}
                  {/*  to={`/admin-panel/organization/view/${organization.parent?.id}`} */}
                  {/* > */}
                  {/*  {organization.parent?.name} */}
                  {/* </Link> */}
                  {rootPath(organization.parent)}
                  {` / ${organization.name}`}
                </Typography>
              )}
            </Box>
          </Box>
        </Box>
        <Box sx={{ display: 'flex', flexDirection: 'row' }}>
          <Tooltip title="ارسال پیام به اعضای سازمان">
            <IconButton
              onClick={() =>
                navigate('/admin-panel/group-msg/create', {
                  state: {
                    selectedOrganizations: [
                      {
                        id: organization.id,
                        label: organization.name,
                        avatar: organization.avatar,
                      },
                    ],
                  },
                })
              }
            >
              <SvgIcon component={ChatTextIcon} />
            </IconButton>
          </Tooltip>
          <Tooltip title="ارسال اعلان به اعضای سازمان">
            <IconButton
              onClick={() =>
                navigate('/admin-panel/announcement/create', {
                  state: {
                    selectedOrganizations: [
                      {
                        id: organization.id,
                        label: organization.name,
                        avatar: organization.avatar,
                      },
                    ],
                  },
                })
              }
            >
              <SvgIcon component={BellSimpleIcon} />
            </IconButton>
          </Tooltip>
          <Tooltip title="ویرایش سازمان">
            <IconButton>
              <EditIcon
                onClick={() =>
                  navigate(`/admin-panel/organization/${organization.id}/update`)
                }
                style={{ color: '#888' }}
              >
                {' '}
              </EditIcon>
            </IconButton>
          </Tooltip>
          <Tooltip title="حذف سازمان">
            <IconButton>
              <DeleteIcon
                style={{ color: 'red', marginLeft: 0 }}
                onClick={() => setShowDeleteOrganizationBox(true)}
              />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
      <Box sx={{ width: '100%', marginBottom: '20px', textAlign: 'center' }}>
        <Typography
          variant="h4"
          sx={{ mt: 3, fontSize: 17, fontWeight: 'bold' }}
        >
          لیست اعضای سازمان
        </Typography>
      </Box>

      <MembersSection organization={organization} />
      {/* <Grid container> */}

      <BottomSheet
        title="حذف سازمان"
        hideBottomSheet={() => setShowDeleteOrganizationBox(false)}
        show={showDeleteOrganizationBox}
      >
        <BottomSheetMessage>
          آیا می‌خواهید این سازمان را حذف کنید؟
        </BottomSheetMessage>

        <Stack direction="row">
          <BottomSheetPrimaryButton
            onClick={async () => {
              try {
                await deleteOrganization(organization.id);
                setShowDeleteOrganizationBox(false);
                navigate('/admin-panel/organization');
              } catch (e) {
                dispatch(
                  setSnackbar({
                    message: e?.response?.data[0],
                    severity: 'error',
                  }),
                );
                setShowDeleteOrganizationBox(false);
              }
            }}
          >
            بله
          </BottomSheetPrimaryButton>
          <BottomSheetSecondaryButton
            onClick={() => setShowDeleteOrganizationBox(false)}
          >
            خیر
          </BottomSheetSecondaryButton>
        </Stack>
      </BottomSheet>
    </>
  );
}
