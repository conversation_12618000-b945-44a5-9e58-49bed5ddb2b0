import {
  Box,
  Button,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  IconButton,
  Stack,
  Switch,
  TextField,
  Typography,
} from '@mui/material';
import { useMutation, useQueryClient } from 'react-query';
import { useIsDesktop } from 'utils';
import ContentPasteOutlinedIcon from '@mui/icons-material/ContentPasteOutlined';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import File from '../../../components/ContentForm/components/File/File';
import MyBackdrop from '../../../components/ContentForm/components/MyBackdrop/MyBackdrop';
import CircularProgressWithValue from '../../../components/CircularProgressWithValue/CircularProgressWithValue';
import { setSnackbar } from '../../../store/layout';
import { selectMe } from '../../../store/auth';
import { createAnnouncement } from '../../../apis/announcement';
import uploadFile from '../../../apis/storage';
import MultiSelectOrganization from './components/MultiSelectOrganization';
import MultiSelectMembers from './components/MultiSelectMember';
import MultiSelectAdmins from './components/MultiSelectAdmins';
import { ContentPasteGoOutlined } from '@mui/icons-material';

const EMPTY_CONTENT = {
  type: '',
  category: '',
  title: '',
  description: '',
  interaction_requests: [],
};

export default function AnnouncementCreate({ content = EMPTY_CONTENT }) {
  const isDesktop = useIsDesktop();
  const location = useLocation();

  const [sendToOrgan, setSendToOrgan] = useState(
    !!location.state?.selectedOrganizations || false,
  );
  const [sendToAdmins, setSendToAdmins] = useState(
    !!location.state?.selectedAdmins || false,
  );
  const [sendToMembers, setSendToMembers] = useState(
    !!location.state?.selectedMembers || false,
  );

  const isCreate = content === EMPTY_CONTENT;

  const descriptionRef = useRef(null);
  const formRef = useRef(null);

  const [filePreview, setFilePreview] = useState(null);
  const [fileType, setFileType] = useState(undefined);

  const [selectedMembers, setSelectedMembers] = useState(
    location?.state?.selectedMembers || [],
  );
  const [selectedAdmins, setSelectedAdmins] = useState(
    location?.state?.selectedAdmins || [],
  );
  const [selectedOrganizations, setSelectedOrganizations] = useState(
    location?.state?.selectedOrganizations || [],
  );

  const navigate = useNavigate();

  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);

  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const abortController = useRef(null);
  const mutation = useMutation(
    async ({ event }) => {
      setLoading(true);

      const { body, title, file } = event.target;

      if (body.value === '' || title.value === '') {
        throw { message: 'عنوان و متن اعلان اجباری است', severity: 'error' };
      } else if (
        selectedAdmins.length === 0 &&
        selectedMembers.length === 0 &&
        selectedOrganizations.length === 0
      ) {
        throw { message: 'حداقل یک مخاطب انتخاب کنید', severity: 'error' };
      }

      let uploadedFile;

      if (file?.files[0])
        uploadedFile = await uploadFile(file.files[0], setProgress);

      return createAnnouncement(
        {
          banner: uploadedFile?.data?.id || '',
          title: title.value,
          body: body.value || '',
          receiving_organizations: selectedOrganizations.map(x => x.id) || [],
          receiving_users: sendToAdmins
            ? selectedAdmins.map(x => x.id) || []
            : selectedMembers.map(x => x.id) || [],
        },
        setProgress,
        abortController.current,
      );
    },
    {
      onSuccess: async data => {
        dispatch(
          setSnackbar({
            message: 'اعلان با موفقیت ارسال شد',
            severity: 'success',
          }),
        );

        setProgress(0);
        setLoading(false);
        formRef.current.reset();
        setSelectedMembers([]);
        setSelectedAdmins([]);
        setSelectedOrganizations([]);
      },
      onError: error => {
        if (error?.response?.status === 400) {
          dispatch(
            setSnackbar({ message: 'خطا در ایجاد اعلان', severity: 'error' }),
          );
        } else dispatch(setSnackbar(error));

        setProgress(0);
        setLoading(false);
      },
    },
  );

  const errors = mutation.error?.response?.data || {};

  const submitForm = event => {
    abortController.current = new AbortController();
    event.preventDefault();
    mutation.mutate({ event });
  };

  const getFilePreview = () => {
    if (filePreview) return URL.createObjectURL(filePreview);
    if (content.preview) return content.preview;
    return null;
  };

  useEffect(() => {
    abortController.current?.abort();
  }, []);

  const cancelCreate = () => {
    abortController.current.abort();
  };

  const pasteDescription = async () => {
    descriptionRef.current.value = await navigator.clipboard.readText();
  };

  const resetCheckBoxes = async () => {
    setSendToOrgan(false);
    setSendToAdmins(false);
    setSendToMembers(false);
    setSelectedOrganizations([]);
    setSelectedMembers([]);
    setSelectedAdmins([]);
  };

  const me = useSelector(selectMe);

  const DIVIDER_MY = 3;

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        overflowY: 'scroll',
        mt: 2,
      }}
    >
      <form onSubmit={submitForm} ref={formRef}>
        <Grid container columnSpacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} lg={4}>
            <File
              file={content.file}
              fileType={content.file_type}
              errors={errors.file}
              disabled={!isCreate || mutation.isLoading}
              setPreview={setFilePreview}
              preview={getFilePreview()}
              setFileType={setFileType}
            />
          </Grid>

          <Grid item xs={12} lg={8} sx={{ mt: isDesktop ? 0 : 2 }}>
            <TextField
              variant="outlined"
              required
              label="عنوان"
              name="title"
              defaultValue={content.title}
              helperText={errors.title}
              error={!!errors.title}
              disabled={mutation.isLoading}
              inputProps={{ maxLength: 100 }}
              fullWidth
            />

            <TextField
              sx={{ mt: 2 }}
              label="متن اعلان"
              name="body"
              defaultValue={content.description}
              variant="outlined"
              multiline
              rows={4}
              helperText={errors.description}
              disabled={mutation.isLoading}
              fullWidth
              inputRef={descriptionRef}
              InputProps={{
                endAdornment: (
                  <IconButton edge="end" onClick={pasteDescription}>
                    <ContentPasteGoOutlined />
                  </IconButton>
                ),
              }}
            />

            <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

            <Typography sx={{ fontSize: 16, fontWeight: 'bold' }}>
              ارسال به
            </Typography>

            <FormGroup row sx={{ justifyContent: 'space-between' }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={sendToAdmins}
                    onChange={e =>
                      resetCheckBoxes() && setSendToAdmins(e.target.checked)
                    }
                  />
                }
                label="مدیر سازمان ها"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={sendToMembers}
                    onChange={e =>
                      resetCheckBoxes() && setSendToMembers(e.target.checked)
                    }
                  />
                }
                label="اعضا/عضو انتخابی"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={sendToOrgan}
                    onChange={e =>
                      resetCheckBoxes() && setSendToOrgan(e.target.checked)
                    }
                  />
                }
                label="سازمان‌ها/سازمان‌ انتخابی"
              />
            </FormGroup>

            {sendToAdmins && (
              <FormControl fullWidth sx={{ mt: 2 }}>
                <MultiSelectAdmins
                  selectedAdmins={selectedAdmins}
                  setSelectedAdmins={setSelectedAdmins}
                />
              </FormControl>
            )}

            {sendToOrgan && (
              <FormControl fullWidth sx={{ mt: 2 }}>
                <MultiSelectOrganization
                  selectedOrganizations={selectedOrganizations}
                  setSelectedOrganizations={setSelectedOrganizations}
                />
              </FormControl>
            )}

            {sendToMembers && (
              <FormControl fullWidth sx={{ mt: 2 }}>
                <MultiSelectMembers
                  selectedMembers={selectedMembers}
                  setSelectedMembers={setSelectedMembers}
                />
              </FormControl>
            )}

            <Button
              variant="contained"
              size="large"
              type="submit"
              sx={{ mt: 2, width: '100%' }}
            >
              ذخیره و ارسال
            </Button>
          </Grid>
        </Grid>
      </form>

      <MyBackdrop open={loading}>
        <Stack spacing={2}>
          <CircularProgressWithValue
            variant={isCreate ? 'determinate' : 'indeterminate'}
            value={progress}
            color="inherit"
            size={84}
          />
          <Button
            color="error"
            variant="contained"
            disableElevation
            startIcon={<HighlightOffIcon />}
            onClick={cancelCreate}
          >
            لغو
          </Button>
        </Stack>
      </MyBackdrop>
    </Box>
  );
}
