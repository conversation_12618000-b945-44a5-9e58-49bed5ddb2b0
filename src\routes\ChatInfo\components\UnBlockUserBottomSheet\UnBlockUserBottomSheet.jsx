import { Stack } from '@mui/material';
import { unblockUser } from 'apis/messenger';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { setSnackbar } from 'store/layout';

export default function UnBlockUserBottomSheet({
  show,
  hideBottomSheet,
  userId,
  chatId,
}) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  function onUnBlockUser() {
    unblockUser({ chatId, blocked: userId }).then(() => {
      dispatch(setSnackbar({
        message: 'کاربر با موفقیت از محدودیت خارج شد.',
        severity: 'success',
      }));

      navigate(-1);
    });
  }

  return (
    <BottomSheet
      title="رفع محدودیت کاربر"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        آیا تمایل به رفع محدودیت کاربر دارید؟
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            onUnBlockUser();
            hideBottomSheet();
          }}
        >
          بله
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={hideBottomSheet}>
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
