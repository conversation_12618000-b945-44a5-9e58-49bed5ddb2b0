import { useEffect, useRef, useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  Grid,
  Stack,
  Typography,
} from '@mui/material';
import { CONTENT_TYPES } from 'constants';
import DesktopAppBar from 'components/DesktopAppBar/DesktopAppBar';
import { useIsDesktop, jalaliToGregorian } from 'utils';
import dayjs from 'dayjs';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import { useSelector } from 'react-redux';
import ReactPlayer from 'react-player';
import ImageSlider from '../../components/ImageSlider/ImageSlider';
import StickyNotification from '../../components/StickyNotification/StickyNotification';
import ContentTypeMenu from './components/ContentTypeMenu/ContentTypeMenu';
import TopItems from './components/TopItems/TopItems';
import Header from './components/Header/Header';
import VerticalBanners from './components/VerticalBanners/VerticalBanners';
import MobileContentTypeMenu from './components/MobileContentTypeMenu/MobileContentTypeMenu';
import IconSax from '../../components/IconSax/IconSax';
import MyLink from '../../components/MyLink/MyLink';
import { selectMe } from '../../store/auth';
import TopItemsMarfook from './components/TopItemsMarfook/TopItemsMarfook';

function ExternalLinkButton({ to, children, mt = '50px' }) {
  return (
    <MyLink to={to} target="_blank" rel="noopener noreferrer">
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          bgcolor: '#C4F8F3',
          borderRadius: '8px',
          pr: 2,
          pl: 2,
          mt,
          height: '54px',
        }}
      >
        {children}
      </Stack>
    </MyLink>
  );
}

export default function Home() {
  const scrollContainerRef = useRef(null);
  const [openPopup, setOpenPopup] = useState(false);

  const me = useSelector(selectMe);

  useEffect(() => {
    const threshold = 150;
    let lastScrollY = 0;

    const savedPosition = localStorage.getItem('scrollPosition');
    if (savedPosition && scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = parseInt(savedPosition, 10);
    }

    const handleScroll = () => {
      if (!scrollContainerRef.current) return;

      const currentScrollY = scrollContainerRef.current.scrollTop;
      if (Math.abs(currentScrollY - lastScrollY) >= threshold) {
        localStorage.setItem('scrollPosition', currentScrollY.toString());
        lastScrollY = currentScrollY;
      }
    };

    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  useEffect(() => {
    // Show popup if not already dismissed
    const isPopupDismissed = localStorage.getItem('popupDismissed');
    // const isPopupDismissed = false;
    if (!isPopupDismissed) {
      setOpenPopup(true);
    }
  }, []);

  const handleClosePopup = () => {
    setOpenPopup(false);
    localStorage.setItem('popupDismissed', 'true'); // Mark popup as dismissed
  };

  const isDesktop = useIsDesktop();
  const thirtyDaysBefore = jalaliToGregorian(
    dayjs().subtract(30, 'day').startOf('day'),
  );

  return (
    <>
      {!!me && (
        <Dialog open={openPopup} onClose={handleClosePopup}>
          <DialogTitle
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              textAlign: 'center',
              fontWeight: 'bold',
              fontSize: '16px',
              pt: 3,
              gap: '10px',
            }}
          >
            <img src="/logo.png" alt="logo" height={30} />
            به سامانه تام خوش آمدید!
          </DialogTitle>
          <DialogContent sx={{ textAlign: 'center' }}>
            <ReactPlayer
              url="/tutorial/video.mp4"
              controls
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                margin: 'auto',
                // border: '1px solid #0FA6A1',
                borderRadius: '7px',
                overflow: 'hidden',
              }}
              width="70%"
              maxWidth="100%"
              height="100%"
              playing
            />
          </DialogContent>
        </Dialog>
      )}
      <Grid
        container
        ref={scrollContainerRef}
        sx={{ overflowY: 'scroll', position: 'relative' }}
        columnSpacing={1}
        rowSpacing={2}
        className="no-scrollbar"
      >
        {!isDesktop && <Header />}
        {isDesktop && <DesktopAppBar />}

        <Grid item xs={12} lg={6} sx={{ marginTop: isDesktop ? '56px' : '0' }}>
          <ImageSlider />
        </Grid>

        {isDesktop && <VerticalBanners />}

        <StickyNotification />

        {isDesktop && <ContentTypeMenu />}
        {!isDesktop && <MobileContentTypeMenu />}

        {!isDesktop && <VerticalBanners />}

        <TopItemsMarfook sx={{ mt: 3 }} title="مرفوک" />
        <TopItems
          sx={{ mt: 3 }}
          title="برترین‌ها"
          type=""
          ordering="-thebest"
          // fromDate={thirtyDaysBefore}
          link="&ordering=-downloads_count"
        />

        <TopItems
          sx={{ mt: 3 }}
          title="جدیدترین‌ها"
          type=""
          ordering="-id"
          // fromDate={thirtyDaysBefore}
        />

        {CONTENT_TYPES.map(type => (
          <TopItems
            title={type.name}
            key={type.name}
            type={type.value}
            sx={{ mt: 3 }}
            ordering="-id"
          />
        ))}
      </Grid>
    </>
  );
}
