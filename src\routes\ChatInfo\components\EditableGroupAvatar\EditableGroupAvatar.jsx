import {
  alpha,
  Avatar,
  Box,
  CircularProgress,
  Typography,
  Stack,
  styled,
} from '@mui/material';
import IconSax from 'components/IconSax/IconSax';
import {
  useCallback, useEffect, useRef, useState,
} from 'react';
import { useMutation } from 'react-query';
import { useSelector } from 'react-redux';
import { selectMyId } from 'store/auth';
import ImgCropper from '../../../../components/ImgCropper/ImgCropper';
import Field from '../Field/Field';
import { updateChat } from '../../../../apis/messenger';
import uploadFile from '../../../../apis/storage';

const SIZE = 128;

const HiddenInput = styled('input')(() => ({
  display: 'none',
}));

function Loading() {
  return (
    <Stack
      sx={(theme) => ({
        width: SIZE,
        height: SIZE,
        background: alpha(theme.palette.grey[400], 0.4),
        borderRadius: '50%',
        position: 'absolute',
        top: 0,
      })}
      justifyContent="center"
      alignItems="center"
    >
      <CircularProgress color="secondary" />
    </Stack>
  );
}

export default function EditableGroupAvatar({
  avatar,
  chatId,
  creator,
  chatName,
  setChatName,
}) {
  const inputRef = useRef(null);
  const [selectedImg, setSelectedImg] = useState(null);
  const [croppedImg, setCroppedImg] = useState(null);
  const [showCropper, setShowCropper] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [showEditInput, setShowEditInput] = useState(false);
  const myId = useSelector(selectMyId);
  let accessEdit = false;

  const mutation = useMutation(
    async (avatarImg) => {
      const data = await uploadFile(avatarImg, () => {});
      const formData = new FormData();
      formData.append('avatar', data.data.id);
      updateChat(chatId, formData);
      return true;
    },
    {
      onMutate: () => setUploading(true),
      onSettled: () => setUploading(false),
      onError: () => setCroppedImg(null),
    },
  );

  useEffect(() => {
    inputRef.current.addEventListener('change', (event) => {
      setSelectedImg(event.target.files[0]);
    });
  }, []);

  useEffect(() => {
    if (selectedImg) {
      setShowCropper(true);
    }
  }, [selectedImg]);

  useEffect(() => {
    if (croppedImg) {
      mutation.mutate(croppedImg);
    }
  }, [croppedImg]);

  const onAvatarClick = useCallback(() => {
    if (!uploading && accessEdit) {
      inputRef.current.click();
    }
  }, [uploading]);

  const onEditNameClick = useCallback(() => {
    if (accessEdit) {
      setShowEditInput(true);
    }
  }, [accessEdit]);

  const hideCropper = useCallback(() => setShowCropper(false), []);

  const croppedImgURL = croppedImg ? URL.createObjectURL(croppedImg) : null;

  if (creator === myId) accessEdit = true;

  return (
    <Box sx={{ position: 'relative', textAlign: 'center', maxWidth: '130px' }}>
      <HiddenInput ref={inputRef} type="file" accept="image/*" />
      <Avatar
        sx={(theme) => ({
          width: SIZE,
          height: SIZE,
          fontSize: 48,
          bgcolor: theme.palette.primary.dark,
        })}
        src={croppedImgURL || avatar}
        onClick={onAvatarClick}
      />
      {accessEdit && (<IconSax name="edit" sx={{ position: 'absolute', bottom: '50px', right: '0' }} onClick={onAvatarClick} />)}
      {!showEditInput && (<Typography sx={{ mt: 2, textAlign: 'center' }}>{chatName}</Typography>)}
      {accessEdit && !showEditInput && (
      <IconSax
        name="edit"
        sx={{
          width: '15px', position: 'absolute', bottom: '0px', right: '0',
        }}
        onClick={onEditNameClick}
      />
      )}
      {showEditInput && (
        <Field
          chatId={chatId}
          title=""
          name="name"
          value={chatName}
          done={setShowEditInput}
          setChatName={setChatName}
        />
      )}

      {showCropper && (
        <ImgCropper
          src={URL.createObjectURL(selectedImg)}
          hideCropper={hideCropper}
          setCroppedImg={setCroppedImg}
          cropShape="round"
        />
      )}

      {uploading && <Loading />}
    </Box>
  );
}
