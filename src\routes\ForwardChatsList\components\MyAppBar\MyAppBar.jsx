import { IconButton, Stack, Typography } from '@mui/material';
import ContactSearchBox from 'components/ContactSearchBox/ContactSearchBox';
import CustomAppBar from 'components/CustomAppBar/CustomAppBar';
import BackButton from 'components/CustomAppBar/components/BackButton/BackButton';
import SubTitle from 'components/CustomAppBar/components/SubTitle/SubTitle';
import IconSax from 'components/IconSax/IconSax';

export default function MyAppBar({ onSearchChange }) {
  return (
    <CustomAppBar>
      <Stack sx={{ mb: 2, width: '100%' }}>
        <Stack direction="row" sx={{ mt: 1, mb: 1 }}>
          <BackButton />

          <Stack flexGrow={1}>
            <SubTitle text="انتخاب گفت‌وگو" />
          </Stack>

          <IconButton color="primary">
            <IconSax name="tick-square" />
            <Typography sx={{ fontWeight: 400, fontSize: '14px', ml: 1 }}>ارسال</Typography>
          </IconButton>
        </Stack>

        <ContactSearchBox onSearchChange={onSearchChange} />
      </Stack>
    </CustomAppBar>
  );
}
