import { Stack } from '@mui/material';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';

export default function UpdateAvailableBottomSheet({
  show,
  hideBottomSheet,
  update,
}) {
  return (
    <BottomSheet
      title="به روز رسانی جدید"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        نسخه‌ی جدید در دسترس است. آیا می‌خواهید به روز رسانی کنید؟
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            update();
          }}
        >
          بله
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={hideBottomSheet}>
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
