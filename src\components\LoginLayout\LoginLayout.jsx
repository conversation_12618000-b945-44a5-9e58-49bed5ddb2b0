import {
  alpha, Box, Stack,
} from '@mui/material';
import vector from './img/vector.svg';
import LoginLogo from '../LoginLogo/LoginLogo';

export default function LoginLayout({ children }) {
  return (
    <Stack
      sx={{
        width: '100vw',
        height: '100vh',
        backgroundImage: `url(${vector})`,
        backgroundColor: alpha('#D9D9D9', 0.4),
      }}
      alignItems="center"
    >

      <Stack
        direction="column"
        sx={{
          height: '100%',
          width: '100%',
          maxWidth: 'sm',
        }}
        justifyContent="space-between"
      >
        <Stack flexGrow={1} justifyContent="center">
          <LoginLogo />
        </Stack>

        <Box
          sx={{
            background: 'white',
            borderRadius: '25px 25px 0px 0px',
            padding: 3,
            paddingBottom: 1,
          }}
        >
          <Stack direction="column" spacing={2}>
            {children}
          </Stack>
        </Box>

      </Stack>
    </Stack>
  );
}
