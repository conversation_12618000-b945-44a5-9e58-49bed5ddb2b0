import { Stack, Typography } from '@mui/material';
import { EVENT_MESSAGE_TYPES, MESSAGE_TYPES, PATHS } from 'constants';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { setPathParam } from 'utils';
import { LongPressEventType, useLongPress } from 'use-long-press';
import { useRef, useState } from 'react';
import DeleteMenu from '../MessageMenu/DeleteMenu';

function GenEventText({ message }) {
  const eventType = message.extraData.event_type;
  const username = message.extraData.user_username;

  const buildProfilePath = (userId) => setPathParam(PATHS.profile, 'userId', userId);
  const navigate = useNavigate();
  const onUserClick = () => navigate(buildProfilePath(message.author.id));
  let text = '';

  if (eventType === EVENT_MESSAGE_TYPES.ADD_PARTICIPANT) {
    text = 'به گفت‌وگو اضافه شد.';
  }

  if (eventType === EVENT_MESSAGE_TYPES.REMOVE_PARTICIPANT) {
    text = 'از گفت‌وگو حذف شد.';
  }

  if (eventType === EVENT_MESSAGE_TYPES.ADD_ADMIN) {
    text = 'به لیست مدیران اضافه شد.';
  }

  if (eventType === EVENT_MESSAGE_TYPES.REMOVE_ADMIN) {
    text = 'از لیست مدیر‌ان حذف شد.';
  }

  if (eventType === EVENT_MESSAGE_TYPES.BLOCK_USER) {
    text = 'محدود شد.';
  }

  if (eventType === EVENT_MESSAGE_TYPES.UNBLOCK_USER) {
    text = 'از محدودیت خارج شد.';
  }

  return (
    <Typography fontSize="12px">
      کاربر
      <span
        style={{ color: '#3390ec', cursor: 'pointer' }}
        onClick={onUserClick}
      >
        {username ? ` @${username} ` : ''}
      </span>
      {text}
    </Typography>
  );
}

function GenText({ message }) {
  if (message.type === MESSAGE_TYPES.GROUP_CREATE) {
    const { author } = message;
    const fullName = `${author.firstName} ${author.lastName}`;

    return <Typography>{`گفت‌وگو توسط ${fullName} ایجاد شد. `}</Typography>;
  }

  if (message.type === MESSAGE_TYPES.EVENT) {
    return <GenEventText message={message} />;
  }
  return <Typography />;
}

export default function StatusMessage({ message, isAdmin }) {
  const createdAt = dayjs(message.createdAt).format('HH:mm  YYYY-MM-DD');

  const containerRef = useRef(null);
  const [anchorEl, setAnchorEl] = useState(false);
  const bind = useLongPress(() => setAnchorEl(containerRef.current), {
    filterEvents: () => true,
    captureEvent: true,
    detect: LongPressEventType.Pointer,

  });

  return (
    <>
      <Stack ref={containerRef} alignItems="center" {...bind()}>
        <Typography
          sx={(theme) => ({
            pr: 2,
            pl: 2,
            pt: 0.5,
            pb: 0.5,

            background: theme.palette.secondary.main,
            borderRadius: theme.shape.borderRadius,
            color: theme.palette.secondary.dark,
            fontSize: '12px',
          })}
          align="center"
        >
          <GenText message={message} />
          {createdAt}
        </Typography>
      </Stack>
      {isAdmin
        && (
        <DeleteMenu
          open={!!anchorEl}
          anchorEl={anchorEl}
          handleClose={() => setAnchorEl(null)}
          isAdmin={isAdmin}
          message={message}
        />
        )}
    </>
  );
}
