import { Button, Stack, Typography } from '@mui/material';
import { PATHS } from 'constants';
import { useNavigate } from 'react-router-dom';
import ErrorIcon from '@mui/icons-material/Error';

export default function ErrorPage() {
  const navigate = useNavigate();
  const redirectToHome = () => navigate(PATHS.home);

  return (
    <Stack justifyContent="center" alignItems="center" sx={{ height: '100%' }}>
      <ErrorIcon sx={{ fontSize: 64, marginTop: 10 }} />
      <Typography sx={{ mt: 2 }}>
        خطایی رخ داده است.
        لطفا مجددا تلاش کنید
      </Typography>
      <Button sx={{ mt: 2 }} onClick={redirectToHome}>بازگشت به صفحه اصلی</Button>
    </Stack>
  );
}
