import { Avatar, Stack, Typography } from '@mui/material';
import { PATHS } from 'constants';
import { setPathParam } from 'utils';
import MyLink from 'components/MyLink/MyLink';
import FullScreenImage from 'components/FullScreenImage/FullScreenImage';
import { useState } from 'react';

function MyAvatar({ avatar, fullName, small, username, onAvatarClick }) {
  const SIZE = small ? 30 : 50;
  const avatarPlaceholder = (fullName || username || ' ')[0];
  return (
    <Stack direction="row" alignItems="center">
      <Avatar
        src={avatar}
        sx={theme => ({
          width: SIZE,
          height: SIZE,
          bgcolor: theme.palette.primary.dark,
        })}
        onClick={onAvatarClick}
      >
        {avatarPlaceholder}
      </Avatar>

      <Stack ml={small ? 1 : 2} sx={{ width: '100%' }}>
        <Typography
          variant={small ? '' : 'h6'}
          sx={{ fontWeight: '600', direction: 'rtl', width: 'fit-content' }}
        >
          {fullName}
        </Typography>

        {username && (
          <Typography
            variant="caption"
            sx={{ direction: 'rtl', width: 'fit-content' }}
          >
            {`@${username}`}
          </Typography>
        )}
      </Stack>
    </Stack>
  );
}

export default function MyAvatarWrapper({
  avatar,
  fullName,
  small,
  username,
  userId,
  withLink,
  withFullScreen,
}) {
  if (withLink) {
    return (
      <MyLink to={setPathParam(PATHS.profile, 'userId', userId)}>
        <MyAvatar
          avatar={avatar}
          fullName={fullName}
          small={small}
          username={username}
        />
      </MyLink>
    );
  }

  const [showFullScreen, setShowFullScreen] = useState(false);

  return (
    <>
      <MyAvatar
        avatar={avatar}
        fullName={fullName}
        small={small}
        username={username}
        onAvatarClick={() => setShowFullScreen(true)}
      />
      {withFullScreen && avatar && (
        <FullScreenImage
          show={showFullScreen}
          unShow={() => setShowFullScreen(false)}
          img={avatar}
        />
      )}
    </>
  );
}
