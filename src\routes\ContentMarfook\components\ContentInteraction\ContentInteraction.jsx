import { Stack } from '@mui/material';
import { formatNumber } from 'utils';
import { useSelector } from 'react-redux';
import { useQuery } from 'react-query';
import { useEffect, useState } from 'react';
import DownloadButton from '../DownloadButton/DownloadButton';
import ShareButton from '../ShareButton/ShareButton';
import EvaluateButton from '../EvaluateButton/EvaluateButton';
import { selectMe } from '../../../../store/auth';
import { getContentEvaluationUserResponse } from '../../../../apis/content';
import MarfookButton from '../MarfookButton/MarfookButton';

export function ContentInteraction({
  contentId,
  contentUrl,
  contentName,
  contentType,
  contentDesc,
  contentLabels,
  size,
  evaluationRequested = false,
  myScore = 0,
}) {
  const me = useSelector(selectMe);

  const [activeBtn, setActiveBtn] = useState(true);

  useQuery(
    ['contentEvaluationResponseList', contentId],
    () => getContentEvaluationUserResponse(contentId),
    {
      enabled: !!me?.groups?.includes('MarfookEvaluator'),
      onSuccess: data => {
        if (data.status !== 200) return;
        if (typeof data?.data === 'object' && data?.data?.length > 0) {
          setActiveBtn(false);
        }
      },
    },
  );
  // if (me?.groups?.includes('MarfookEvaluator')) {
  //   useQuery(
  //     ['contentEvaluationResponseList', contentId],
  //     () => getContentEvaluationUserResponse(contentId),
  //     {
  //       onSuccess: data => {
  //         if (data.status !== 200) return;
  //         if (typeof data?.data === 'object' && data?.data?.length > 0) {
  //           setActiveBtn(false);
  //         }
  //       },
  //     },
  //   );
  // }

  const generateShareText = () => {
    let text = contentDesc;
    text += '\n';
    text += contentLabels.join('\n');
    return text;
  };

  return (
    <>
      <Stack direction="row" spacing={1}>
        <ShareButton
          url={contentUrl}
          type={contentType}
          description={contentDesc}
          labels={contentLabels}
          sx={{ flexGrow: 6 }}
          text={generateShareText()}
        />

        <DownloadButton
          id={contentId}
          url={contentUrl}
          name={contentName}
          size={formatNumber(size)}
          sx={{ flexGrow: 3 }}
        />
      </Stack>
      {!!me && !!me.groups?.includes('MarfookEvaluator') && !!evaluationRequested && (
        <Stack direction="row" spacing={1} sx={{ mt: '6px!important' }}>
          <EvaluateButton
            id={contentId}
            active={activeBtn}
            size={formatNumber(size)}
            sx={{ width: '100%' }}
            myScore={myScore}
          />
        </Stack>
      )}
      {!!me && me?.groups?.includes('MarfookExpert') && (
        <Stack direction="row" spacing={1} sx={{ mt: '6px!important' }}>
          <MarfookButton
            id={contentId}
            active={activeBtn}
            size={formatNumber(size)}
            sx={{ width: '100%' }}
          />
        </Stack>
      )}
    </>
  );
}
