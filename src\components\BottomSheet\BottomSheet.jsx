import {
  Box, Stack, Typography, alpha,
} from '@mui/material';
import { grey } from '@mui/material/colors';

export default function BottomSheet({
  hideBottomSheet,
  title,
  children,
  show,
  onOutSideClick,
}) {
  return (
    <Box
      sx={{
        display: show ? '' : 'none',
        position: 'fixed',
        width: '100vw',
        height: '100vh',

        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        marginTop: '0 !important',

        background: alpha(grey[600], 0.6),
        zIndex: 1200,

        color: 'black',
      }}
      onClick={() => {
        if (onOutSideClick) onOutSideClick();
        hideBottomSheet();
      }}
    >
      <Stack
        sx={{
          position: 'absolute',
          bottom: 0,

          width: '100vw',
          borderRadius: '25px 25px 0 0',
          p: 3,
          pb: 2,

          background: 'white',
        }}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Typography sx={{ fontWeight: 700, fontSize: '18p' }}>
          {title}
        </Typography>

        {children}
      </Stack>
    </Box>
  );
}
