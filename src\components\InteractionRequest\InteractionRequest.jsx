import {
  Badge,
  Box,
  IconButton,
  InputBase,
  Stack,
  SvgIcon,
  Typography,
} from '@mui/material';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import RateReviewOutlinedIcon from '@mui/icons-material/RateReviewOutlined';
import FavoriteBorderOutlinedIcon from '@mui/icons-material/FavoriteBorderOutlined';
import TextsmsOutlinedIcon from '@mui/icons-material/TextsmsOutlined';
import PersonAddAltOutlinedIcon from '@mui/icons-material/PersonAddAltOutlined';
import { useMutation, useQueryClient } from 'react-query';
import {
  createInteractionResponse,
  updateInteractionResponse,
} from 'apis/content';
import { INTERACTION_RESPONSE_TYPES, PATHS } from 'constants';
import { useDispatch } from 'react-redux';
import { setSnackbar } from 'store/layout';
import { useRef, useState } from 'react';
import { ReactComponent as HeartRedIcon } from 'static/icons/heart-red.svg';
import { ReactComponent as ChatCircleDotsGreenIcon } from 'static/icons/chat-circle-dots-green.svg';
import { ReactComponent as FollowBlueIcon } from 'static/icons/follow-blue.svg';
import { createIntentUrl, useIsDesktop } from 'utils';
import LoadingButton from '../LoadingButton/LoadingButton';

function createContentIntentUrl(url) {
  return createIntentUrl('openurl', 'openurl', { url });
}

export default function InteractionRequest({
  sx,
  id,
  contentId,
  url,
  likeRequest,
  followRequest,
  commentRequest,
  onDelete,
  followed,
  liked,
  commented,
  note,
  noteId,
}) {
  const ICONS_SIZE = { width: '18px', height: '18px' };
  const ICONS_SIZE2 = { width: '24px', height: '24px' };

  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const isDesktop = useIsDesktop();

  const [showNoteSection, setShowNoteSection] = useState(false);
  const [noteInput, setNoteInput] = useState(note);
  const [likeLoclal, setLikeLocal] = useState(liked);
  const [commentLoclal, setCommentLocal] = useState(commented);
  const [followeLoclal, setFollowLocal] = useState(followed);

  const like = useMutation(
    () =>
      createInteractionResponse({
        interactionRequestId: id,
        interactionType: INTERACTION_RESPONSE_TYPES.LIKE,
      }),
    {
      onSuccess: () => {
        dispatch(
          setSnackbar({
            message: 'لایک با موفقیت ثبت شد.',
            severity: 'info',
          }),
        );
        setLikeLocal(true);
        queryClient.invalidateQueries({
          queryKey: [PATHS.content, contentId],
        });
      },
    },
  );

  const comment = useMutation(
    () =>
      createInteractionResponse({
        interactionRequestId: id,
        interactionType: INTERACTION_RESPONSE_TYPES.COMMENT,
      }),
    {
      onSuccess: () => {
        dispatch(
          setSnackbar({
            message: 'نظر با موفقیت ثبت شد.',
            severity: 'info',
          }),
        );

        setCommentLocal(true);
        queryClient.invalidateQueries({ queryKey: [PATHS.content, contentId] });
      },
    },
  );

  const follow = useMutation(
    () =>
      createInteractionResponse({
        interactionRequestId: id,
        interactionType: INTERACTION_RESPONSE_TYPES.FOLLOW,
      }),
    {
      onSuccess: () => {
        dispatch(
          setSnackbar({
            message: 'دنبال کردن با موفقیت ثبت شد.',
            severity: 'info',
          }),
        );

        setFollowLocal(true);
        queryClient.invalidateQueries({ queryKey: [PATHS.content, contentId] });
      },
    },
  );

  const noteRef = useRef();

  const sendNote = useMutation(async () => {
    if (!noteRef.current.value) return;
    if (noteId) {
      await updateInteractionResponse({
        interactionResponseId: noteId,
        note: noteRef.current.value,
      });
    } else {
      await createInteractionResponse({
        interactionRequestId: id,
        interactionType: INTERACTION_RESPONSE_TYPES.NOTE,
        note: noteRef.current.value,
      });
    }
    // noteRef.current.value = '';
    dispatch(
      setSnackbar({
        message: 'یادداشت با موفقیت ثبت شد.',
        severity: 'info',
      }),
    );
    setShowNoteSection(!showNoteSection);
    queryClient.invalidateQueries({ queryKey: [PATHS.content, contentId] });
  });

  return (
    <>
      <Stack direction="row" spacing={1} sx={sx}>
        {onDelete && (
          <IconButton
            sx={{ bgcolor: '#E7EAF4', borderRadius: 2 }}
            onClick={onDelete}
          >
            <DeleteOutlineOutlinedIcon sx={ICONS_SIZE} />
          </IconButton>
        )}
        <IconButton
          sx={{ bgcolor: '#E7EAF4', borderRadius: 2 }}
          onClick={() => setShowNoteSection(!showNoteSection)}
        >
          <Badge
            color="warning"
            variant="dot"
            sx={{ marginRight: 0, marginTop: -2 }}
            invisible={!note}
          />
          <RateReviewOutlinedIcon sx={ICONS_SIZE} />
        </IconButton>

        <Stack
          direction="row"
          sx={{
            bgcolor: '#E7EAF4',
            flexGrow: 1,
            flexShrink: 2,
            borderRadius: 2,
            overflow: 'hidden',
          }}
        >
          {likeRequest && (
            <IconButton
              disabled={likeLoclal}
              onClick={like.mutate}
              isLoading={like.isLoading}
              type="submit"
            >
              {likeLoclal ? (
                // <FavoriteIcon sx={ICONS_SIZE} />
                <SvgIcon component={HeartRedIcon} sx={ICONS_SIZE2} />
              ) : (
                <FavoriteBorderOutlinedIcon sx={ICONS_SIZE} />
              )}
            </IconButton>
          )}

          {commentRequest && (
            <IconButton
              disabled={commentLoclal}
              onClick={comment.mutate}
              isLoading={comment.isLoading}
            >
              {commentLoclal ? (
                // <TextsmsIcon sx={ICONS_SIZE} />
                <SvgIcon component={ChatCircleDotsGreenIcon} sx={ICONS_SIZE2} />
              ) : (
                <TextsmsOutlinedIcon sx={ICONS_SIZE} />
              )}
            </IconButton>
          )}

          {followRequest && (
            <IconButton
              disabled={followeLoclal}
              onClick={follow.mutate}
              isLoading={follow.isLoading}
            >
              {followeLoclal ? (
                <SvgIcon component={FollowBlueIcon} sx={ICONS_SIZE2} />
              ) : (
                // <PersonAddAltOutlinedIcon sx={ICONS_SIZE} />
                <PersonAddAltOutlinedIcon sx={ICONS_SIZE} />
                // <PersonAddAltOutlinedIcon sx={ICONS_SIZE} />
              )}
            </IconButton>
          )}

          <Typography
            sx={{
              flexGrow: 1,
              flexShrink: 1,
              margin: 'auto',
              paddingRight: 2,
              paddingLeft: 4,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              direction: 'rtl',
            }}
            component="a"
            align="right"
          >
            <a
              href={!isDesktop ? createContentIntentUrl(url) : url}
              target={isDesktop ? '_blank' : ''}
              rel="noreferrer"
            >
              {url}
            </a>
          </Typography>
        </Stack>
      </Stack>
      {!!showNoteSection && (
        <Stack
          mt="8px!important"
          p={2}
          sx={{
            bgcolor: '#E7EAF4',
            borderRadius: 2,
          }}
        >
          <Typography sx={{ fontSize: '16px', fontWeight: '700', ...sx }}>
            یادداشت:
          </Typography>

          <Box
            sx={theme => ({
              border: '1px #2C2C2E solid',
              borderRadius: '8px',
              padding: '16px',
              borderColor: theme.palette.grey.A400,
            })}
          >
            <InputBase
              fullWidth
              placeholder="در رابطه با این لینک مرجع٬ یک یادداشت بنویسید. این یادداشت را تنها شما و صاحب محتوا می‌بیند"
              multiline
              rows={3}
              inputRef={noteRef}
              value={noteInput}
              onChange={() => setNoteInput(noteRef?.current?.value)}
            />
            <LoadingButton
              variant="contained"
              sx={{ width: '100%', mt: 2 }}
              onClick={sendNote.mutate}
              loading={sendNote.isLoading}
            >
              ذخیره
            </LoadingButton>
          </Box>
        </Stack>
      )}
    </>
  );
}
