import { Box, Grid, Typography } from '@mui/material';
import dayjs from 'dayjs';
import { useIsDesktop } from '../../../../../utils';

function SampleAnnounceSection({ announce }) {
  const isDesktop = useIsDesktop();

  return (
    <Grid sx={{
      py: 2,
      px: 3,
      mb: 3,
      ml: '10px',
      borderRadius: '8px',
      background: 'white',
      boxShadow: '0px 2px 20px 0px #00000012',
      width: isDesktop ? 'calc(50% - 5px)' : '100%',
    }}
    >

      <Box display="flex" width="100%" justifyContent="space-between" mb={5}>
        <Typography variant="h6" fontWeight="bold" fontSize={16}>نمونه پیام</Typography>
      </Box>
      <Box display="flex flex-row" width="100%">
        {announce.banner && <Box style={{ borderRadius: '6px' }}><img src={announce.banner} alt="image" style={{ width: '100%', borderRadius: '8px' }} /></Box>}
        <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: 'bold' }}>{announce.title}</Typography>
        <Typography sx={{ fontSize: '14px', color: '#A0A0A0', mb: 2 }}>{dayjs(announce.created_at).format('YYYY-MM-DD hh:ss')}</Typography>
        <Typography sx={{ fontSize: '14px' }}>{announce.body}</Typography>
      </Box>
    </Grid>
  );
}

export default SampleAnnounceSection;
