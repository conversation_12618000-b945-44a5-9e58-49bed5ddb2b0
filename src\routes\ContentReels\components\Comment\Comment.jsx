import { IconButton, Stack, Typography } from '@mui/material';
import { useSelector } from 'react-redux';
import { selectMyId } from 'store/auth';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import { useState } from 'react';
import MyAvatar from '../../../../components/MyAvatar/MyAvatar';
import DeleteCommentBottomSheet from '../DeleteCommentBottomSheet/DeleteCommentBottomSheet';

export default function Comment({
  avatar,
  username,
  comment,
  commentId,
  authorId,
  fullName,
  contentId,
}) {
  const myId = useSelector(selectMyId);
  const [wantToDeleteComment, setWantToDeleteComment] = useState(null);

  return (
    <>
      <Stack direction="row">
        <Stack direction="column" flexGrow={1}>
          <MyAvatar
            avatar={avatar}
            username={username}
            fullName={fullName}
            small
            userId={authorId}
            withLink
          />
          <Typography
            sx={{
              textAlign: 'justify',
              fontSize: '14px',
              fontWeight: '400',
              color: '#2C2C2E',
              marginTop: '4px',
            }}
          >
            {comment}
          </Typography>
        </Stack>
        {myId === authorId
          && (
            <IconButton
              disableRipple
              onClick={() => {
                setWantToDeleteComment(commentId);
              }}
            >
              <DeleteOutlineOutlinedIcon />
            </IconButton>
          )}
      </Stack>

      <DeleteCommentBottomSheet
        show={!!wantToDeleteComment}
        hideBottomSheet={() => setWantToDeleteComment(null)}
        commentId={commentId}
        contentId={contentId}
      />
    </>
  );
}
