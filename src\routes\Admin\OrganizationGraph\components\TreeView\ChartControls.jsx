import React, { useState } from 'react';
import {
  Box,
  Button,
  IconButton,
  InputAdornment,
  SvgIcon,
  TextField,
} from '@mui/material';
import {
  ArrowForward,
  CropSquare,
  DragIndicator,
  LinearScale,
  PictureAsPdf,
  ZoomIn,
  ZoomOut,
} from '@mui/icons-material';
import { ReactComponent as SearchIcon } from 'static/icons/search-normal.svg';

import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { font } from './IRANSansX-Bold.base64';

function ChartControls({ chartRef, organizationName = '' }) {
  const [isCompact, setIsCompact] = useState(true);

  const [active1, setActive1] = useState(false);
  const [active2, setActive2] = useState(true);
  const [active3, setActive3] = useState(false);

  const toggleCompactView = () => {
    setIsCompact(false);
    setActive1(false);
    setActive2(false);
    setActive3(true);
    chartRef.current.compact(false).layout('top').render().fit();
  };
  const toggleCompactView2 = () => {
    setIsCompact(true);
    // chartRef.current.layout('top').render().fit()
    setActive1(false);
    setActive2(true);
    setActive3(false);
    chartRef.current.compact(true).layout('top').render().fit();
  };

  // const downloadStyledImage = async () => {
  //   const fontPath = './IRANSans.ttf';
  //   const font = await opentype.load(fontPath);
  //   console.log(font);
  //
  //   chartRef.current.exportImg({
  //     backgroundColor: '#fff',
  //     save: false,
  //     full: true,
  //     onLoad: async base64 => {
  //       const canvas = document.createElement('canvas');
  //       const ctx = canvas.getContext('2d');
  //
  //       // Draw the chart onto the canvas
  //       const img = new Image();
  //       img.src = base64;
  //
  //       img.onload = () => {
  //         canvas.width = img.width;
  //         canvas.height = img.height;
  //         ctx.drawImage(img, 0, 0);
  //
  //         // Apply custom font
  //         ctx.font = `16px IRANSans`;
  //         ctx.fillStyle = '#000';
  //         ctx.textAlign = 'center';
  //         ctx.fillText('عنوان چارت سازمانی', canvas.width / 2, 30);
  //
  //         // Save the canvas as an image
  //         const styledImage = canvas.toDataURL('image/png');
  //         const link = document.createElement('a');
  //         link.download = 'chart.png';
  //         link.href = styledImage;
  //         link.click();
  //       };
  //     },
  //   });
  // };

  const loadImageAsDataURI = async url => {
    const response = await fetch(url, { mode: 'cors' });
    const blob = await response.blob();
    return new Promise(resolve => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.readAsDataURL(blob);
    });
  };

  // Replace /logo.png with a Base64 Data URI
  const replaceImagesWithBase64 = async () => {
    const images = document.querySelectorAll('img');
    for (const img of images) {
      const src = img.getAttribute('src');
      if (src.startsWith('/')) {
        // Adjust condition for local or relative paths
        const dataURI = await loadImageAsDataURI(src);
        img.src = dataURI;
      }
    }
  };

  const downloadWithHtml2Canvas = async () => {
    const chartElement = document.getElementById('chart-container'); // Replace with your chart's container ID
    chartRef.current.fit();
    await replaceImagesWithBase64();
    setTimeout(() => {
      html2canvas(chartElement, {
        backgroundColor: '#fff',
        useCORS: true,
        scale: 2, // High-resolution export
      }).then(canvas => {
        const enhancedCanvas = document.createElement('canvas');
        const ctx = enhancedCanvas.getContext('2d');

        // Set dimensions for the new canvas
        enhancedCanvas.width = canvas.width + 20;
        enhancedCanvas.height = canvas.height + 230; // Add extra height for text

        // Draw the chart onto the new canvas
        ctx.fillStyle = '#fff'; // Set background color
        ctx.fillRect(0, 0, enhancedCanvas.width, enhancedCanvas.height);
        ctx.drawImage(canvas, 10, 220); // Add 100px padding for text at the top

        // Add text overlay
        const title = 'نمای چارت سازمانی';
        const description = `نمایش چارت سازمانی مربوط به ${organizationName}`;
        const date = `(${new Date().toLocaleDateString('fa-IR')})`;

        ctx.font = 'bold 44px IRANSansX'; // Use your custom font
        ctx.fillStyle = '#000'; // Black text
        ctx.textAlign = 'center';

        // Draw title
        ctx.fillText(title, enhancedCanvas.width / 2, 80); // Centered, 40px from the top

        // Draw description
        ctx.font = '32px IRANSansX'; // Smaller font for description
        ctx.fillText(description, enhancedCanvas.width / 2, 130); // Centered, below title

        // Draw date
        ctx.font = '28px IRANSansX'; // Even smaller font for date
        ctx.fillText(date, enhancedCanvas.width / 2, 180); // Centered, below description

        // Convert enhanced canvas to an image and download it
        const image = enhancedCanvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.download = 'chart.png';
        link.href = image;
        link.click();
      });
    }, 1000);
  };

  const downloadPdf = async () => {
    const chartElement = document.getElementById('chart-container'); // Replace with your chart's container ID
    chartRef.current.fit();
    // Ensure all images are converted to Base64
    await replaceImagesWithBase64();

    setTimeout(() => {
      html2canvas(chartElement, {
        backgroundColor: '#fff',
        useCORS: true,
        scale: 2, // High-resolution export
      }).then(canvas => {
        const base64 = canvas.toDataURL('image/jpeg', 1.0); // Export the canvas as a Base64 string

        const pdf = new jsPDF('landscape');
        const img = new Image();
        img.src = base64;
        img.onload = () => {
          const margin = 10;
          const pageWidth = pdf.internal.pageSize.getWidth();
          const pageHeight = pdf.internal.pageSize.getHeight();

          const imgWidth = pageWidth - margin * 2; // Adjust width to fit PDF with margins
          const imgHeight = (img.height / img.width) * imgWidth; // Maintain aspect ratio

          // Add title and text above the image
          const title = 'نمای چارت سازمانی';
          const description = `نمایش چارت سازمانی مربوط به ${organizationName}`;
          const date = `(${new Date().toLocaleDateString('fa-IR')})`;

          pdf.addFileToVFS('IRANSans.ttf', font);
          pdf.addFont('IRANSans.ttf', 'IRANSans', 'normal');
          pdf.setFont('IRANSans', 'normal');

          pdf.setFontSize(16);
          pdf.text(title, pageWidth / 2, margin + 10, {
            align: 'center',
            lang: 'fa',
          });

          pdf.setFontSize(12);
          pdf.text(description, pageWidth / 2, margin + 20, {
            align: 'center',
            lang: 'fa',
          });

          pdf.setFontSize(9);
          pdf.text(date, pageWidth / 2, margin + 26, {
            align: 'center',
            lang: 'fa',
          });

          // Add the image
          pdf.addImage(img, 'JPEG', margin, margin + 40, imgWidth, imgHeight);

          // Save the PDF
          pdf.save('chart.pdf');
        };
      });
    }, 1000);
  };

  const buttons = [
    // {
    //   icon: isFullscreen ? <FullscreenExit /> : <Fullscreen />,
    //   label: isFullscreen ? 'خروج از تمام صفحه' : 'تمام صفحه',
    //   action: toggleFullscreen,
    // },
    {
      icon: <CropSquare />,
      label: 'نمایش همه در یک قاب',
      active: false,
      action: () => chartRef.current.fit(),
    },
    // {
    //   icon: <ArrowUpward />,
    //   label: '',
    //   action: () => chartRef.current.layout('bottom').render().fit(),
    // },
    {
      icon: <ArrowForward />,
      label: 'نمایش افقی',
      active: active1,
      action: () => {
        chartRef.current.layout('left').render().fit();
        setActive1(true);
        setActive2(false);
        setActive3(false);
      },
    },
    // {
    //   icon: <ArrowBack />,
    //   label: '',
    //   action: () => chartRef.current.layout('right').render().fit(),
    // },
    // {
    //   icon: <ArrowDownward />,
    //   label: 'نمایش عمودی',
    //   action: () => chartRef.current.layout('top').render().fit(),
    // },
    {
      icon: <DragIndicator />,
      label: 'نمایش خوشه ای عمودی',
      active: active2,
      action: toggleCompactView2,
    },
    {
      icon: <LinearScale />,
      label: 'نمایش سطری عمودی',
      active: active3,
      action: toggleCompactView,
    },
    // {
    //   icon: <Filter />,
    //   label: 'خروجی عکس',
    //   // action: () => chartRef.current.exportImg({ full: true }),
    //   action: () => downloadWithHtml2Canvas(),
    // },
    {
      icon: <PictureAsPdf />,
      label: 'خروجی PDF',
      action: () => downloadPdf(),
    },
    {
      icon: <ZoomOut />,
      label: 'کوچک کردن',
      action: () => chartRef.current.zoomOut(),
    },
    {
      icon: <ZoomIn />,
      label: 'بزرگ کردن',
      action: () => chartRef.current.zoomIn(),
    },
  ];

  const filterChart = e => {
    const { value } = e.target;

    chartRef.current.clearHighlighting();

    const data = chartRef.current.data();

    data.forEach(d => (d._expanded = false));

    data.forEach(d => {
      if (value !== '' && d.name.toLowerCase().includes(value.toLowerCase())) {
        // If matches, mark node as highlighted
        d._highlighted = true;
        d._expanded = true;
      }
    });

    chartRef.current.data(data).render().fit();
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexGrow: 1,
        gap: '5px',
        alignItems: 'center',
        mb: '5px',
      }}
    >
      <TextField
        fullWidth
        label="جستجو"
        onChange={filterChart}
        InputProps={{
          endAdornment: (
            <InputAdornment position="start">
              <IconButton type="submit" edge="end">
                <SvgIcon component={SearchIcon} sx={{ fill: 'none' }} />
              </IconButton>
            </InputAdornment>
          ),
        }}
        sx={{
          '& input': {
            height: '0px',
          },
          '& label': {
            top: '-9px',
            fontSize: '13px',
          },
          background: '#F3F3F3',
          height: '32px',
          width: '300px',
          borderRadius: '5px',
        }}
      />
      {buttons.map((btn, index) => (
        <Button
          key={index}
          size="small"
          sx={{
            fontSize: '12px',
            backgroundColor: btn.active ? '#0B7470' : '#11a6a1',
          }}
          variant="contained"
          onClick={btn.action}
        >
          {btn.icon} {btn.label}
        </Button>
      ))}
    </Box>
  );
}

export default ChartControls;
