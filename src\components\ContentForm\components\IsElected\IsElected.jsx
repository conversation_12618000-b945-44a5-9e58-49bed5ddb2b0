import {
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  Grid,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  Switch,
  TextField,
  Typography,
} from '@mui/material';
import { useState } from 'react';
import dayjs from 'dayjs';
import { SEARCH_PARAMS } from '../../../SearchBox/searchConfig';
import JalaliDatePicker from '../../../SearchBox/components/JalaliDatePicker/JalaliDatePicker';
import { CONTENT_SHARE_TIME } from '../../../../constants';

export default function IsElected({ elected, electedHours, electedDate }) {
  const [showTimeInput, setShowTimeInput] = useState(elected);
  const [showTimeSwitch, setShowTimeSwitch] = useState(
    !!electedHours || !!electedDate,
  );
  const [shareTimeName, setShareTimeName] = useState(
    CONTENT_SHARE_TIME.map(item => item.value),
  );

  const handleChange = event => {
    const { value } = event.target;
    if (value.includes('ALL')) {
      setShareTimeName(
        shareTimeName.length === CONTENT_SHARE_TIME.length
          ? []
          : CONTENT_SHARE_TIME.map(item => item.value),
      );
    } else {
      setShareTimeName(value);
    }
  };

  const isAllSelected = shareTimeName.length === CONTENT_SHARE_TIME.length;

  return (
    <FormGroup>
      <FormControlLabel
        control={
          <Switch
            defaultChecked={elected}
            name="elected"
            id="elected"
            onChange={e => {
              setShowTimeSwitch(e.target.checked);
              setShowTimeInput(false);
            }}
          />
        }
        label="محتوا منتخب شود"
        labelPlacement="end"
      />
      {showTimeSwitch && (
        <>
          <FormControlLabel
            control={
              <Switch
                defaultChecked={!!electedHours || !!electedDate}
                onChange={e => {
                  setShowTimeInput(e.target.checked);
                }}
              />
            }
            label="انتخاب زمان انتشار برای این محتوا"
            labelPlacement="end"
          />
          {showTimeInput && (
            <>
              <Typography variant="body2" style={{ fontSize: '13px' }} color="textSecondary" mb={1}>
                ** مجوز انتشار این محتوا فقط در روز و ساعات تعیین شده زیر خواهد
                بود
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <JalaliDatePicker
                    inputName="elected_start_date"
                    label="از تاریخ"
                    size="medium"
                    setMinDateToday
                    defaultValue={dayjs()}
                  />
                </Grid>
                <Grid item xs={6}>
                  <JalaliDatePicker
                    inputName="elected_end_date"
                    label="تا تاریخ"
                    size="medium"
                    setMinDateToday
                    defaultValue={dayjs()}
                  />
                </Grid>
              </Grid>
              {/* <Grid container mt={1} spacing={2}> */}
              {/*  <Grid item xs={6}> */}
              {/*    <JalaliDatePicker */}
              {/*      inputName="elected_date" */}
              {/*      label="تاریخ" */}
              {/*      size="medium" */}
              {/*      setMinDateToday */}
              {/*      defaultValue={dayjs()} */}
              {/*    /> */}

              {/*  </Grid> */}
              {/*  <Grid item xs={6}> */}
              {/*    <FormControl fullWidth> */}
              {/*      <InputLabel>بازه زمانی</InputLabel> */}
              {/*      <Select */}
              {/*        label="دسته بندی" */}
              {/*        name="elected_hours" */}
              {/*        defaultValue="همه" */}
              {/*        required */}
              {/*        multiple */}
              {/*        value={shareTimeName} */}
              {/*        onChange={handleChange} */}
              {/*        renderValue={(selected) => selected.map(val => { */}
              {/*          const found = CONTENT_SHARE_TIME.find(item => item.value === val); */}
              {/*          return found ? found.name : ''; */}
              {/*        }).join(', ')} */}
              {/*      > */}
              {/*        <MenuItem value="ALL"> */}
              {/*          <Checkbox checked={isAllSelected} /> */}
              {/*          <ListItemText primary="انتخاب همه" /> */}
              {/*        </MenuItem> */}
              {/*        {CONTENT_SHARE_TIME.map((category) => ( */}
              {/*          <MenuItem value={category.value} key={category.value}> */}
              {/*            {category.name} */}
              {/*          </MenuItem> */}
              {/*        ))} */}
              {/*      </Select> */}
              {/*    </FormControl> */}
              {/*  </Grid> */}
              {/* </Grid> */}
            </>
          )}
        </>
      )}
    </FormGroup>
  );
}
