import BottomNavigation from '@mui/material/BottomNavigation';
import BottomNavigationAction from '@mui/material/BottomNavigationAction';
import { Badge, Paper } from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { selectHasUnreadMessage } from 'store/messenger';
import { PATHS } from '../../constants';
import { selectMe } from '../../store/auth';
import { setPathParam } from '../../utils';
import IconSax from '../IconSax/IconSax';

export const BOTTOM_NAV_HEIGHT = '56px';

export default function MyBottomNavigation() {
  const location = useLocation();
  const navigate = useNavigate();
  const me = useSelector(selectMe);

  const changePath = (e, newPath) => navigate(newPath);

  const hasUnreadMessage = useSelector(selectHasUnreadMessage);

  return (
    <Paper
      sx={{
        position: 'fixed', bottom: 0, right: 0, left: 0,
      }}
      elevation={3}
    >
      <BottomNavigation
        showLabels
        value={location.pathname}
        onChange={changePath}
      >
        <BottomNavigationAction
          label="پروفایل"
          icon={<IconSax name="user-square" />}
          value={setPathParam(PATHS.profile, 'userId', me?.id)}
        />
        <BottomNavigationAction
          label="پیام‌رسان"
          icon={(
            <Badge color="error" variant={hasUnreadMessage ? 'dot' : ''}>
              <IconSax name="messages-3" />
            </Badge>
          )}
          value={PATHS.chats}
        />
        <BottomNavigationAction
          label="جست‌وجو"
          icon={<IconSax name="search-normal" />}
          value={PATHS.search}
        />
        <BottomNavigationAction
          label="تنظیمات"
          icon={<IconSax name="setting-2" />}
          value={PATHS.settings}
        />
        <BottomNavigationAction
          label="خانه"
          icon={<IconSax name="home-2" />}
          value={PATHS.home}
        />
      </BottomNavigation>
    </Paper>
  );
}
