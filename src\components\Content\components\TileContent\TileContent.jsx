import { Stack, Typography, Grid } from '@mui/material';
import ContentAnalytics from 'components/ContentAnalytics/ContentAnalytics';
import ContentPreview from 'components/ContentPreview/ContentPreview';
import MyLink from 'components/MyLink/MyLink';

const BORDER_SIEZE = '1px';

function ContentTitle({ title, size }) {
  return (
    <Typography
      noWrap
      color="white"
      sx={{ width: size, mt: 1, fontSize: '12px' }}
      textAlign="center"
    >
      {title}
    </Typography>
  );
}

export default function TileContent({
  size,
  rounded,
  content,
  withTypeIcon,
  withTitle,
  withAnalytics,
  link,
}) {
  return (
    <Grid item xs={4} lg={2}>
      <MyLink to={link}>
        <Stack sx={{ position: 'relative' }}>
          <ContentPreview
            rounded={rounded}
            preview={content.preview}
            fileType={content.file_type}
            withTypeIcon={withTypeIcon}
            status={content.status}
          />

          {withTitle && <ContentTitle title={content.title} size={size} />}

          {withAnalytics && (
            <ContentAnalytics
              small
              like={content.likes_count}
              dislike={content.dislikes_count}
              view={content.views_count}
              download={content.downloads_count}
              sx={{
                position: 'absolute',
                height: '30px',
                bottom: BORDER_SIEZE,
                left: BORDER_SIEZE,
                right: BORDER_SIEZE,
                background:
                  'linear-gradient(360deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%)',
                color: '#fff',
              }}
            />
          )}
        </Stack>
      </MyLink>
    </Grid>
  );
}
