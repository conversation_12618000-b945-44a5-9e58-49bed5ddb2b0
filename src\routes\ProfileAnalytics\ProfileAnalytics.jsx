import {
  Avatar,
  Box,
  Divider,
  Grid,
  I<PERSON><PERSON><PERSON>on,
  Stack,
  styled,
  Typography,
} from '@mui/material';
import {
  getContent,
  getContentAnalytics,
  getContentSupportInfo,
} from 'apis/content';
import LoadingPage from 'components/LoadingPage/LoadingPage';
import { PATHS } from 'constants';
import { useQuery } from 'react-query';
import { useLoaderData } from 'react-router-dom';
import { setPathParam, useIsDesktop } from 'utils';
import { useState } from 'react';
import { DEFAULT_APPBAR_HEIGHT } from 'components/CustomAppBar/constants';
import DesktopAppBar from 'components/DesktopAppBar/DesktopAppBar';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import { ReactComponent as LikeIcon } from 'static/icons/heart.svg';
import { ReactComponent as CommentIcon } from 'static/icons/message-text.svg';
import { ReactComponent as FollowIcon } from 'static/icons/user-square.svg';
import { ReactComponent as NoteIcon } from 'static/icons/archive-book.svg';
import { useSelector } from 'react-redux';
import dayjs from 'dayjs';
import { selectMe } from '../../store/auth';
import MyTabs from '../../components/MyTabs/MyTabs';
import MyAppBar from './components/MyAppBar/MyAppBar';
import MyChart from './components/MyChart/MyChart';
import SumRow from './components/SumRow/SumRow';

export const TABS = [
  {
    label: 'لایک',
    icon: LikeIcon,
    value: 0,
    count: 0,
  },
  {
    label: 'کامنت',
    icon: CommentIcon,
    value: 1,
    count: 0,
  },
  {
    label: 'فالو',
    icon: FollowIcon,
    value: 2,
    count: 0,
  },
  {
    label: 'یادداشت ها',
    icon: NoteIcon,
    value: 3,
    count: 0,
  },
];

const Name = styled(Typography)(() => ({
  display: 'flex',
  alignItems: 'center',
  fontSize: '15px',
  fontWeight: 'bold',
  color: '#2C2C2E',
  flexGrow: 1,
}));

const LastTime = styled(Typography)(() => ({
  fontSize: '12px',
  fontWeight: 500,
  color: '#64676A',
  direction: 'rtl',
}));

const Comment = styled(Typography)(() => ({
  fontSize: '13px',
  fontWeight: 500,
  color: '#64676A',
  textAlign: 'left',
}));

export async function loader({ params }) {
  return params.contentId;
}

function Header({ title, sx, hasCopy = false, copyAction }) {
  return (
    <Typography sx={{ fontSize: '16px', fontWeight: '700', ...sx }}>
      {title}
      {!!hasCopy && (
        <IconButton sx={{ display: 'inline-block' }} onClick={copyAction}>
          <FileCopyIcon />
        </IconButton>
      )}
    </Typography>
  );
}

function CustomTabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ minHeight: '300px' }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

export default function ProfileAnalytics() {
  const me = useSelector(selectMe);

  const isDesktop = useIsDesktop();
  const [tab, setTab] = useState(TABS[0].value);

  const contentId = useLoaderData();
  const { isLoading, data } = useQuery(
    [PATHS.contentAnalytics, contentId],
    () => getContentAnalytics(contentId),
  );

  const [title, setTitle] = useState('');
  useQuery(
    setPathParam(PATHS.content, 'contentId', contentId),
    () => getContent(contentId),
    { onSuccess: data => setTitle(data.data.title) },
  );

  const [likeList, setLikeList] = useState([]);
  const [commentList, setCommentList] = useState([]);
  const [followList, setFollowList] = useState([]);
  const [noteList, setNoteList] = useState([]);
  useQuery(
    ['contentSupport', contentId],
    () => getContentSupportInfo({ contentId, interactionType: '' }),
    {
      onSuccess: data => {
        if (data.status !== 200) return;

        const clikeList = [];
        const ccommentList = [];
        const cfollowList = [];
        const cnoteList = [];

        for (let i = 0; i < data.data.length; i++) {
          if (data.data[i].interaction_type === 'like')
            clikeList.push(data.data[i]);
          if (data.data[i].interaction_type === 'comment')
            ccommentList.push(data.data[i]);
          if (data.data[i].interaction_type === 'follow')
            cfollowList.push(data.data[i]);
          if (data.data[i].interaction_type === 'note')
            cnoteList.push(data.data[i]);
        }

        TABS[0].count = clikeList.length;
        TABS[1].count = ccommentList.length;
        TABS[2].count = cfollowList.length;
        TABS[3].count = cnoteList.length;

        setLikeList(clikeList);
        setCommentList(ccommentList);
        setFollowList(cfollowList);
        setNoteList(cnoteList);
      },
    },
  );

  if (isLoading) return <LoadingPage />;

  const {
    likes_count: likesCount,
    likes_list: likesList,
    views_count: viewsCount,
    views_list: viewsList,
  } = data.data;
  return (
    <>
      {!isDesktop && <MyAppBar title={title} />}
      {isDesktop && <DesktopAppBar />}

      <Grid
        container
        sx={{
          height: '100%',
          overflowY: 'scroll',
          pb: 2,
          mt: DEFAULT_APPBAR_HEIGHT,
          p: 2,
          pt: 0,
        }}
        columnSpacing={2}
        className="no-scrollbar"
        // sx={{
        //   overflowY: 'scroll', mt: DEFAULT_APPBAR_HEIGHT, p: 2, pt: 0,
        // }}
      >
        <Grid item xs={12} lg={6}>
          <MyChart data={viewsList} />
          <SumRow title="مجموع بازدید:" value={viewsCount} sx={{ mt: 1 }} />
        </Grid>

        <Grid item xs={12} lg={6}>
          <MyChart data={likesList} sx={{ mt: 2 }} />
          <SumRow title="مجموع لایک:" value={likesCount} sx={{ mt: 1 }} />
        </Grid>
      </Grid>
      <Divider />
      {!!me && !!me.can_see_admin_panel && (
        <Stack spacing={2}>
          <Header title="آمار حمایت از پست:" sx={{ mt: 2 }} />

          <MyTabs
            tabs={TABS}
            selectedTab={tab}
            setSelectedTab={setTab}
            variant="fullWidth"
          />
          <CustomTabPanel value={tab} index={0}>
            <Grid
              container
              sx={{ height: '100%' }}
              flex
              justifyContent="center"
            >
              <Grid
                item
                xs={12}
                lg={6}
                sx={{
                  overflowY: 'scroll',
                  height: '100%',
                  p: 2,
                  textAlign: 'center',
                }}
                direction="row"
                rowSpacing={2}
                container
              >
                {likeList.map(item => (
                  <Grid
                    item
                    xs={12}
                    pt={1}
                    pb={1}
                    borderBottom="1px solid #ccc"
                  >
                    <Stack direction="row" spacing={2}>
                      <Avatar src={item.author.avatar} />
                      <Stack
                        direction="column"
                        sx={{ minWidth: 0, flexGrow: 1 }}
                        spacing={1}
                      >
                        <Stack
                          direction="row"
                          alignItems="center"
                          sx={{ flexGrow: 1 }}
                        >
                          <Name>
                            <span>{`${item.author.first_name} ${item.author.last_name}`}</span>
                          </Name>

                          <LastTime>
                            {dayjs(item.created_at).format('YYYY-MM-DD HH:mm')}
                          </LastTime>
                        </Stack>
                      </Stack>
                    </Stack>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </CustomTabPanel>
          <CustomTabPanel value={tab} index={1}>
            <Grid
              container
              sx={{ height: '100%' }}
              flex
              justifyContent="center"
            >
              <Grid
                item
                xs={12}
                lg={6}
                sx={{
                  overflowY: 'scroll',
                  height: '100%',
                  p: 2,
                  textAlign: 'center',
                }}
                direction="row"
                rowSpacing={2}
                container
              >
                {commentList.map(item => (
                  <Grid
                    item
                    xs={12}
                    pt={1}
                    pb={1}
                    borderBottom="1px solid #ccc"
                  >
                    <Stack direction="row" spacing={2}>
                      <Avatar src={item.author.avatar} />
                      <Stack
                        direction="column"
                        sx={{ minWidth: 0, flexGrow: 1 }}
                        spacing={1}
                      >
                        <Stack
                          direction="row"
                          alignItems="center"
                          sx={{ flexGrow: 1 }}
                        >
                          <Name>
                            <span>{`${item.author.first_name} ${item.author.last_name}`}</span>
                          </Name>

                          <LastTime>
                            {dayjs(item.created_at).format('YYYY-MM-DD HH:mm')}
                          </LastTime>
                        </Stack>
                      </Stack>
                    </Stack>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </CustomTabPanel>
          <CustomTabPanel value={tab} index={2}>
            <Grid
              container
              sx={{ height: '100%' }}
              flex
              justifyContent="center"
            >
              <Grid
                item
                xs={12}
                lg={6}
                sx={{
                  overflowY: 'scroll',
                  height: '100%',
                  p: 2,
                  textAlign: 'center',
                }}
                direction="row"
                rowSpacing={2}
                container
              >
                {followList.map(item => (
                  <Grid
                    item
                    xs={12}
                    pt={1}
                    pb={1}
                    borderBottom="1px solid #ccc"
                  >
                    <Stack direction="row" spacing={2}>
                      <Avatar src={item.author.avatar} />
                      <Stack
                        direction="column"
                        sx={{ minWidth: 0, flexGrow: 1 }}
                        spacing={1}
                      >
                        <Stack
                          direction="row"
                          alignItems="center"
                          sx={{ flexGrow: 1 }}
                        >
                          <Name>
                            <span>{`${item.author.first_name} ${item.author.last_name}`}</span>
                          </Name>

                          <LastTime>
                            {dayjs(item.created_at).format('YYYY-MM-DD HH:mm')}
                          </LastTime>
                        </Stack>
                      </Stack>
                    </Stack>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </CustomTabPanel>
          <CustomTabPanel value={tab} index={3}>
            <Grid
              container
              sx={{ height: '100%' }}
              flex
              justifyContent="center"
            >
              <Grid
                item
                xs={12}
                lg={6}
                sx={{
                  overflowY: 'scroll',
                  height: '100%',
                  p: 2,
                  textAlign: 'center',
                }}
                direction="row"
                rowSpacing={2}
                container
              >
                {noteList.map(item => (
                  <Grid
                    item
                    xs={12}
                    pt={1}
                    pb={1}
                    borderBottom="1px solid #ccc"
                  >
                    <Stack direction="row" spacing={2}>
                      <Avatar src={item.author.avatar} />
                      <Stack
                        direction="column"
                        sx={{ minWidth: 0, flexGrow: 1 }}
                        spacing={1}
                      >
                        <Stack
                          direction="row"
                          alignItems="center"
                          sx={{ flexGrow: 1 }}
                        >
                          <Name>
                            <span>{`${item.author.first_name} ${item.author.last_name}`}</span>
                          </Name>

                          <LastTime>
                            {dayjs(item.created_at).format('YYYY-MM-DD HH:mm')}
                          </LastTime>
                        </Stack>
                        <Stack>
                          <Comment>{item.note}</Comment>
                        </Stack>
                      </Stack>
                    </Stack>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </CustomTabPanel>
        </Stack>
      )}
    </>
  );
}
