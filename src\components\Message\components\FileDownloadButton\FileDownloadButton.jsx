import CircularProgressWithValue from 'components/CircularProgressWithValue/CircularProgressWithValue';
import IconSax from 'components/IconSax/IconSax';
import { useState } from 'react';
import { ajaxDownload } from 'utils';

export default function FileDownloadButton({ url, name, sx }) {
  const [progress, setProgress] = useState(0);

  const downloadFile = async () => {
    await ajaxDownload(url, name, setProgress);
  };

  return progress > 0
    ? <CircularProgressWithValue variant="determinate" value={progress} />
    : (
      <IconSax sx={{ ...sx }} name="arrow-down-2" onClick={downloadFile} />
    );
}
