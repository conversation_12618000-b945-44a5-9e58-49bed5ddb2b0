import { Stack } from '@mui/material';
import { useLoaderData } from 'react-router-dom';
import SearchBox from 'components/SearchBox/SearchBox';
import {
  SEARCH_PARAMS,
  buildSearchConfigFromURL,
} from 'components/SearchBox/searchConfig';
import Contents from 'components/Contents/Contents';
import { useState } from 'react';
import MyTabs from 'components/MyTabs/MyTabs';
import { CONTENT_TYPES, CONTENT_STATUS } from 'constants';
import { useIsDesktop } from 'utils';
import DesktopAppBar, { DESKTOP_APPBAR_HEIGHT } from 'components/DesktopAppBar/DesktopAppBar';
import dayjs from 'dayjs';
import { CONTENT_SHARE_TIME } from '../../constants';

const TABS = [{ label: 'همه', value: '' }, { label: 'کاربران', value: 'U' }, ...CONTENT_TYPES.map((type) => ({ label: type.name, value: type.value }))];

export async function loader({ request }) {
  const url = new URL(request.url);
  const searchConfig = {
    ...buildSearchConfigFromURL(url),
    [SEARCH_PARAMS.STATUS]: CONTENT_STATUS.APPROVED,
  };
  const storedViewMode = localStorage.getItem(SEARCH_PARAMS.VIEW_MODE);
  if (storedViewMode) {
    searchConfig[SEARCH_PARAMS.VIEW_MODE] = storedViewMode;
  }
  return [searchConfig];
}

export default function Search() {
  const [searchConfig] = useLoaderData();
  const [contentType, setContentType] = useState(
    searchConfig[SEARCH_PARAMS.TYPE],
  );
  const [selectedShareTime, setSelectedShareTime] = useState('NOW');
  const isDesktop = useIsDesktop();

  const currentHour = dayjs().hour();

  const filteredShareTime = CONTENT_SHARE_TIME.filter(
    (timeSlot) => timeSlot.startHour > currentHour);

  const TimeTABS = [{ label: 'اکنون', value: 'NOW' }, ...filteredShareTime.map((type) => ({ label: type.name, value: type.value }))];

  return (
    <>
      {isDesktop && <DesktopAppBar />}

      <Stack sx={{ height: '100%', marginTop: isDesktop ? DESKTOP_APPBAR_HEIGHT : 0 }}>
        <SearchBox searchConfig={searchConfig} />

        <MyTabs
          tabs={TABS}
          selectedTab={contentType}
          setSelectedTab={(newValue) => setContentType(newValue)}
          variant={isDesktop ? 'standard' : 'scrollable'}
          centered
        />
        {searchConfig.elected && (
          <MyTabs
            tabs={TimeTABS}
            selectedTab={selectedShareTime}
            setSelectedTab={(newValue) => setSelectedShareTime(newValue)}
            variant="scrollable"
          />
        )}

        <Contents
          searchConfig={searchConfig}
          withAnalytics
          withTypeIcon
          contentType={contentType}
          viewMode={searchConfig[SEARCH_PARAMS.VIEW_MODE]}
        />
      </Stack>
    </>
  );
}
