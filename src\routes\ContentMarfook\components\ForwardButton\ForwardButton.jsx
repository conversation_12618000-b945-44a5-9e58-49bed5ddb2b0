import { IconButton } from '@mui/material';
import IconSax from 'components/IconSax/IconSax';
import { PATHS } from 'constants';
import { useNavigate } from 'react-router-dom';
import { isLoggedIn, setPathParam } from 'utils';
import { useState } from 'react';
import LoginBottomSheet from '../LoginBottomSheet/LoginBottomSheet';

export default function ForwardButton({
  contentId,
  iconColor = '#64676A',
  iconSize = 'medium',
}) {
  const navigate = useNavigate();
  const navigateToSelectChat = () =>
    navigate(setPathParam(PATHS.forwardContent, 'contentId', contentId));
  const [showLoginBottomSheet, setShowLoginBottomSheet] = useState(false);

  const onForwardClick = () => {
    if (!isLoggedIn()) {
      setShowLoginBottomSheet(true);
    } else {
      navigateToSelectChat();
    }
  };

  return (
    <>
      <IconButton onClick={onForwardClick}>
        <IconSax
          name="direct-left"
          sx={{ color: iconColor, pb: 0.2, pt: 0.2 }}
          fontSize={iconSize}
        />
      </IconButton>

      <LoginBottomSheet
        show={showLoginBottomSheet}
        hideBottomSheet={() => setShowLoginBottomSheet(false)}
        message="لطفا برای اشتراک گذاری محتوا وارد شوید."
      />
    </>
  );
}
