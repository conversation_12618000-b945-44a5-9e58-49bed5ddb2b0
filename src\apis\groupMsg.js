import myAxios from './myAxios';

export async function createGroupMsg(data, onProgress, abortController) {
  return myAxios.post('/notification/group-messages/', data, {
    signal: abortController.signal,
    headers: { 'Content-Type': 'application/json' },
    onUploadProgress: e => {
      onProgress(Math.floor((e.loaded / e.total) * 100));
    },
  });
}

export async function getGroupMsgs({ page = 1, pageSize = 10 }) {
  return myAxios.get(
    `/notification/group-messages/?page=${page}&page_size=${pageSize}`,
  );
}

export async function getGroupMsg(id) {
  return myAxios.get(`/notification/group-messages/${id}/`);
}
