import { IconButton, SvgIcon } from '@mui/material';
import { PATHS } from 'constants';
import { useNavigate } from 'react-router-dom';
import { ReactComponent as CrownIcon } from 'static/icons/crown.svg';

export default function AdminButton() {
  const navigate = useNavigate();

  return (
    <IconButton onClick={() => navigate(PATHS.admin.homepage)}>
      <SvgIcon component={CrownIcon} />
    </IconButton>
  );
}
