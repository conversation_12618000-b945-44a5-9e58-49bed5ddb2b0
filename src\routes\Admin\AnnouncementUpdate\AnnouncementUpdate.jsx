import {
  Box,
  Button,
  Divider, FormControl,
  Grid,
  IconButton,
  InputLabel, MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { CONTENT_CATEGORY, PATHS } from 'constants';
import { useIsDesktop } from 'utils';
import ContentPasteOutlinedIcon from '@mui/icons-material/ContentPasteOutlined';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import { useEffect, useRef, useState } from 'react';
import { useLoaderData, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import File from '../../../components/ContentForm/components/File/File';
import MyBackdrop from '../../../components/ContentForm/components/MyBackdrop/MyBackdrop';
import CircularProgressWithValue from '../../../components/CircularProgressWithValue/CircularProgressWithValue';
import {
  createContent, createInteractionRequest,
  deleteInteractionRequest as deleteInteractionRequestAPI, getContent,
  updateContent,
} from '../../../apis/content';
import { setSnackbar } from '../../../store/layout';
import { selectMe } from '../../../store/auth';
import { updateAnnouncement, getAnnouncement } from '../../../apis/announcement';
import MultiSelectWithTabs from '../GroupMsgCreate/components/MultiSelectWithTabs';
import uploadFile from '../../../apis/storage';
import LoadingPage from '../../../components/LoadingPage/LoadingPage';

export async function loader({ params }) {
  return params.announcementId;
}

export default function AnnouncementUpdate() {
  const isDesktop = useIsDesktop();

  const announcementId = useLoaderData();
  const { isLoading, data } = useQuery([PATHS.announcement, announcementId], () => getAnnouncement(announcementId));

  const announce = isLoading ? {} : data.data;

  const descriptionRef = useRef(null);
  const formRef = useRef(null);

  const [filePreview, setFilePreview] = useState(null);
  const [fileType, setFileType] = useState(undefined);

  const [selectedMembers, setSelectedMembers] = useState([]);
  const [selectedOrganizations, setSelectedOrganizations] = useState([]);

  const navigate = useNavigate();

  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);

  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const abortController = useRef(null);
  const mutation = useMutation(
    async ({ event }) => {
      setLoading(true);

      const {
        body,
        title,
        file,
      } = event.target;

      if (body.value === '' || title.value === '') {
        throw { message: 'عنوان و متن اعلان اجباری است', severity: 'error' };
      } else if (selectedMembers.length === 0 && selectedOrganizations.length === 0) {
        throw { message: 'حداقل یک مخاطب انتخاب کنید', severity: 'error' };
      }

      let uploadedFile;

      if (file?.files[0]) uploadedFile = await uploadFile(file.files[0], setProgress);

      return updateAnnouncement(announce.id,
        {
          banner: uploadedFile?.data?.id || '',
          title: title.value,
          body: body.value || '',
          receiving_organizations: selectedOrganizations || [],
          receiving_users: selectedMembers || [],
        },
      );
    },
    {
      onSuccess: async data => {
        // await submitInteractionRequest(data.data.id, interactionRequests);

        console.log(data);
        dispatch(setSnackbar({ message: 'اعلان با موفقیت ویرایش و ارسال شد', severity: 'success' }));
        // navigate(-1);
        // queryClient.invalidateQueries('contents');
        // queryClient.invalidateQueries({
        //   queryKey: [PATHS.content, content.id],
        // });

        setProgress(0);
        setLoading(false);
        formRef.current.reset();
        setSelectedMembers([]);
        setSelectedOrganizations([]);
      },
      onError: error => {
        if (error?.response?.status === 400) {
          dispatch(setSnackbar({ message: 'خطا در ویرایش اعلان', severity: 'error' }));
        } else dispatch(setSnackbar(error));

        setProgress(0);
        setLoading(false);
      },
    },
  );

  const errors = mutation.error?.response?.data || {};

  const submitForm = event => {
    abortController.current = new AbortController();
    event.preventDefault();
    mutation.mutate({ event });
  };

  const getFilePreview = () => {
    if (filePreview) return URL.createObjectURL(filePreview);
    if (announce.banner) return announce.banner;
    return null;
  };

  useEffect(() => () => {
    abortController.current?.abort();
  }, []);
  const cancelCreate = () => {
    abortController.current.abort();
  };

  const pasteDescription = async () => {
    descriptionRef.current.value = await navigator.clipboard.readText();
  };

  const me = useSelector(selectMe);

  const DIVIDER_MY = 3;

  if (isLoading) {
    return <LoadingPage />;
  }

  return (
    <Box sx={{
      width: '100%', height: '100%', overflowY: 'scroll', mt: 2,
    }}
    >
      <form onSubmit={submitForm} ref={formRef}>
        <Grid container columnSpacing={2}>
          <Grid item xs={12} lg={4}>
            <File
              file={announce.banner}
              fileType="image/jpeg"
              errors={errors.banner}
              disabled
              setPreview={setFilePreview}
              preview={getFilePreview()}
              setFileType={setFileType}
            />
          </Grid>

          <Grid item xs={12} lg={8} sx={{ mt: isDesktop ? 0 : 2 }}>

            <TextField
              variant="outlined"
              required
              label="عنوان"
              name="title"
              defaultValue={announce.title}
              helperText={errors.title}
              error={!!errors.title}
              disabled={mutation.isLoading}
              inputProps={{ maxLength: 100 }}
              fullWidth
            />

            <TextField
              sx={{ mt: 2 }}
              label="متن اعلان"
              name="body"
              defaultValue={announce.body}
              variant="outlined"
              multiline
              rows={4}
              helperText={errors.body}
              disabled={mutation.isLoading}
              fullWidth
              inputRef={descriptionRef}
              InputProps={{
                endAdornment: (
                  <IconButton edge="end" onClick={pasteDescription}>
                    <ContentPasteOutlinedIcon />
                  </IconButton>
                ),
              }}
            />

            <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

            <Typography sx={{ fontSize: 16, fontWeight: 'bold' }}>ارسال به</Typography>

            <FormControl fullWidth sx={{ mt: 2 }}>
              <MultiSelectWithTabs
                selectedMembers={selectedMembers}
                setSelectedMembers={setSelectedMembers}
                selectedOrganizations={selectedOrganizations}
                setSelectedOrganizations={setSelectedOrganizations}
              />
            </FormControl>
            <Button
              variant="contained"
              size="large"
              type="submit"
              sx={{ mt: 2, width: '100%' }}
            >
              ذخیره و ارسال
            </Button>

          </Grid>
        </Grid>
      </form>

      <MyBackdrop open={loading}>
        <Stack spacing={2}>
          <CircularProgressWithValue
            variant="indeterminate"
            value={progress}
            color="inherit"
            size={84}
          />
          <Button
            color="error"
            variant="contained"
            disableElevation
            startIcon={<HighlightOffIcon />}
            onClick={cancelCreate}
          >
            لغو
          </Button>
        </Stack>
      </MyBackdrop>
    </Box>
  );
}
