import { IconButton, Stack, Typography } from '@mui/material';
import ContactSearchBox from 'components/ContactSearchBox/ContactSearchBox';
import CustomAppBar from 'components/CustomAppBar/CustomAppBar';
import BackButton from 'components/CustomAppBar/components/BackButton/BackButton';
import SubTitle from 'components/CustomAppBar/components/SubTitle/SubTitle';
import { useDispatch } from 'react-redux';
import { setSelectedContacts } from 'store/messenger';
import { useNavigate } from 'react-router-dom';
import { PATHS, CHAT_TYPES } from 'constants';
import IconSax from 'components/IconSax/IconSax';

export default function MyAppBar({ onSearchChange, selectedContacts, chatType }) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const onCreateClick = () => {
    dispatch(setSelectedContacts(selectedContacts));

    if (chatType === CHAT_TYPES.GROUP) {
      navigate(PATHS.createGroupSetDetail);
    } else {
      navigate(PATHS.createChannelSetDetail);
    }
  };

  return (
    <CustomAppBar>
      <Stack sx={{ mb: 2, width: '100%' }}>
        <Stack direction="row" sx={{ mt: 1, mb: 1 }}>
          <BackButton />

          <Stack flexGrow={1}>
            <SubTitle text="اضافه کردن عضو" />
            <Typography sx={{ fontWeight: 400, fontSize: '12px', color: '#64676A' }}>
              {selectedContacts.length}
              {' '}
              عضو
            </Typography>
          </Stack>

          <IconButton color="primary" onClick={onCreateClick}>
            <IconSax name="tick-square" />
            <Typography sx={{ fontWeight: 400, fontSize: '14px', ml: 1 }}>ایجاد</Typography>
          </IconButton>
        </Stack>

        <ContactSearchBox onSearchChange={onSearchChange} />
      </Stack>
    </CustomAppBar>

  );
}
