import {
  ListItemIcon,
  ListItemText, Menu, MenuItem,
} from '@mui/material';
import ReplyIcon from '@mui/icons-material/Reply';
import EditIcon from '@mui/icons-material/Edit';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import { useDispatch } from 'react-redux';
import { setSelectedMessage } from 'store/messenger';
import { useNavigate } from 'react-router-dom';
import { PATHS, SELECT_MESSAGE_REASONS, MESSAGE_TYPES } from 'constants';
import { setPathParam } from 'utils';
import { useState } from 'react';
import DeleteMessageMenuItem from '../DeleteMessageMenuItem/DeleteMessageMenuItem';
import DeleteMessageBottomSheet from '../DeleteMessageBottomSheet/DeleteMessageBottomSheet';

function isFileOrAudio(message) {
  return [MESSAGE_TYPES.AUDIO, MESSAGE_TYPES.FILE].includes(message.type);
}

function isContent(message) {
  return [MESSAGE_TYPES.CONTENT].includes(message.type);
}

export default function MessageMenu(props) {
  const {
    open,
    anchorEl,
    handleClose,
    message,
    sentByMe,
    isAdmin,
  } = props;

  const dispatch = useDispatch();

  const [isCopied, setIsCopied] = useState(false);
  const selectMessage = (reason) => {
    dispatch(setSelectedMessage({
      chatId: message.chatId,
      reason,
      message,
    }));
    handleClose();
  };
  const reply = () => {
    selectMessage(SELECT_MESSAGE_REASONS.REPLY);
  };
  const edit = () => {
    selectMessage(SELECT_MESSAGE_REASONS.EDIT);
  };

  const navigate = useNavigate();
  const forwardMessage = () => {
    navigate(setPathParam(PATHS.forwardChatsList, 'messageDeliveryToken', message.deliveryToken));
  };

  const copyText = () => {
    navigator.clipboard.writeText(message.displayText)
      .then(() => {
        setIsCopied(true);
        handleClose();
        setTimeout(() => {
          setIsCopied(false);
        }, 500);
      });
  };

  const isEditable = () => sentByMe && !isFileOrAudio(message) && !isContent(message);

  const [showDeleteBottomSheet, setShowDeleteBottomSheet] = useState(false);
  const onDeleteMessageClick = () => {
    handleClose();
    setShowDeleteBottomSheet(true);
  };

  return (
    <>
      <Menu
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: sentByMe ? 'right' : 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: sentByMe ? 'right' : 'left',
        }}
      >
        {isEditable() && (
          <MenuItem onClick={edit}>
            <ListItemIcon><EditIcon /></ListItemIcon>
            <ListItemText>ویرایش</ListItemText>
          </MenuItem>
        )}

        <MenuItem onClick={reply}>
          <ListItemIcon><ReplyIcon /></ListItemIcon>
          <ListItemText>پاسخ</ListItemText>
        </MenuItem>

        <MenuItem onClick={forwardMessage}>
          <ListItemIcon><ReplyIcon sx={{ transform: 'scaleX(-1)' }} /></ListItemIcon>
          <ListItemText>ارسال مجدد</ListItemText>
        </MenuItem>

        {!isContent(message) && (
          <MenuItem onClick={copyText}>
            <ListItemIcon><FileCopyIcon /></ListItemIcon>
            {!isCopied && (<ListItemText>کپی متن</ListItemText>)}
            {isCopied && (<ListItemText>کپی شد</ListItemText>)}
          </MenuItem>
        )}

        {(isAdmin || sentByMe) && (
          <DeleteMessageMenuItem
            onClick={onDeleteMessageClick}
          />
        )}
      </Menu>

      <DeleteMessageBottomSheet
        show={showDeleteBottomSheet}
        hideBottomSheet={() => setShowDeleteBottomSheet(false)}
        messageDeliveryToken={message.deliveryToken}
      />
    </>
  );
}
