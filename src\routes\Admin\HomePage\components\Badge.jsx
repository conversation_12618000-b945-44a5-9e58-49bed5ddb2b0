import { Grid, Paper, Typography } from '@mui/material';
import { Box } from '@mui/system';

export default function Badge({ icon, data, color }) {
  return (
    <Paper
      display="flex"
      justifyContent="center"
      alignItems="center"
      textAlign="center"
      flexDirection="column"
      elevation={2}
      sx={{
        flexGrow: 1,
        width: '100%',
        height: '100px',
        borderLeft: `5px solid ${color}`,
        p: 2,
      }}
    >
      <Typography
        sx={{
          fontSize: '15px',
          fontWeight: '400',
          textAlign: 'left',
          display: 'flex',
          alignItems: 'center',
          gap: '5px',
        }}
      >
        <span style={{ color: `${color}`, height: '20px' }}>{icon}</span>
        <span>{data.title}</span>
      </Typography>
      <Typography
        sx={{
          fontSize: '30px',
          fontWeight: '400',
          textAlign: 'left',
          mt: 1,
          pl: '10px',
          color: { color },
        }}
      >
        {data.value}
      </Typography>
    </Paper>
  );
}
