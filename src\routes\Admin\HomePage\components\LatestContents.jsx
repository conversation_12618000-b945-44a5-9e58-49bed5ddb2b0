import { Button, Grid, Paper, Typography, useMediaQuery } from '@mui/material';
import { Box, Stack } from '@mui/system';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useEffect, useState } from 'react';
import ButtonGroup from '@mui/material/ButtonGroup';
import { CONTENT_STATUS } from 'constants';
import dayjs from 'dayjs';
import { useQuery } from 'react-query';
import TopItems from '../../../Home/components/TopItems/TopItems';
import { jalaliToGregorian } from '../../../../utils';
import Content from '../../../../components/Content/Content';
import { getContents } from '../../../../apis/content';
import { SEARCH_PARAMS } from '../../../../components/SearchBox/searchConfig';
import CardTitle from './CardTitle';

export default function LatestContents() {
  const thirtyDaysBefore = jalaliToGregorian(
    dayjs().subtract(30, 'day').startOf('day'),
  );

  const { isLoading, data } = useQuery(['contents', '', '-id'], () =>
    getContents({
      [SEARCH_PARAMS.TYPE]: '',
      [SEARCH_PARAMS.ORDERING]: '-id',
      [SEARCH_PARAMS.PAGE_SIZE]: 9,
      [SEARCH_PARAMS.STATUS]: CONTENT_STATUS.APPROVED,
      [SEARCH_PARAMS.DATE_FROM]: thirtyDaysBefore,
    }),
  );

  let contents = isLoading ? [] : data.data.results;
  contents = contents.slice(0, 6);

  return (
    <Paper
      display="flex"
      justifyContent="center"
      alignItems="center"
      textAlign="center"
      flexDirection="column"
      elevation={2}
      sx={{ flexGrow: 1, width: '100%', padding: '20px' }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <CardTitle title="آخرین مطالب منتشر شده توسط اعضا" />
        <ButtonGroup variant="contained" aria-label="Basic button group">
          <Button size="small">صوتی</Button>
          <Button size="small">ویدیو</Button>
          <Button size="small">تصویر</Button>
          <Button size="small">همه</Button>
        </ButtonGroup>
      </Box>
      <Grid container spacing={2} mt={1}>
        {contents.map(content => (
          <>
            <Content
              key={content.id}
              withAnalytics
              // withTitle
              withTypeIcon
              content={content}
            />
            {/*<Typography>sdlfaksd</Typography>*/}
          </>
        ))}
      </Grid>
    </Paper>
  );
}
