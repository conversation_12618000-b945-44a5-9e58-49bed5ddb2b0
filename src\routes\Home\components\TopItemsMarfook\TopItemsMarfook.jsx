import { Grid, Typography, useMediaQuery, useTheme } from '@mui/material';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import { useQuery } from 'react-query';
import { SEARCH_PARAMS } from 'components/SearchBox/searchConfig';
import { getContents } from 'apis/content';
import Content from 'components/Content/Content';
import MyLink from 'components/MyLink/MyLink';
import { PATHS, CONTENT_STATUS } from 'constants';
import { getContentsMarfookFolder } from 'apis/contentMarfook';

function Header({ height, type, title, link = '' }) {
  return (
    <Grid
      container
      xs={12}
      direction="row"
      justifyContent="space-between"
      sx={{
        bgcolor: '#23262F',
        borderRadius: '8px',
        pr: 1,
        pl: 1,
        height,
        mt: 2,
      }}
    >
      <Typography
        color="white"
        sx={{
          height,
          lineHeight: height,
          paddingLeft: '8px',
          fontSize: '14px',
        }}
      >
        {title}
      </Typography>

      <MyLink to="/content-marfook">
        <Typography
          color="white"
          sx={{
            height,
            lineHeight: height,
            fontSize: '10px !important',
          }}
        >
          لیست کامل
          <ArrowBackIosNewIcon
            sx={{
              fontSize: '12px !important',
              verticalAlign: 'middle',
              marginLeft: '4px',
            }}
          />
        </Typography>
      </MyLink>
    </Grid>
  );
}

function Contents({ contents, marfook = false }) {
  return (
    <Grid container spacing={2} mt={1}>
      {contents.map(content => (
        <Content
          key={content.id}
          withTitle
          withTypeIcon
          rounded
          content={content}
          marfook={marfook}
        />
      ))}
    </Grid>
  );
}

export default function TopItemsMarfook({ title, type }) {
  const theme = useTheme();
  const isTablet = useMediaQuery(theme.breakpoints.up('sm'));

  const HEIGHT = isTablet ? '48px' : '30px';

  const { isLoading, status, data, refetch } = useQuery(
    ['contentsMarfookFolder'],
    async () => {
      let response = {};
      response = await getContentsMarfookFolder();

      return response.data;
    },
  );

  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
  let contents = isLoading ? [] : data?.[0]?.events || [];
  contents = isDesktop ? contents.slice(0, 6) : contents.slice(0, 3);

  if (contents.length <= 0) return null;
  return (
    !isLoading && (
      <>
        <Header height={HEIGHT} type={type} title={title} />
        <Contents contents={contents} marfook />
      </>
    )
  );
}
