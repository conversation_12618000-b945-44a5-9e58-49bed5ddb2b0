import {
  CircularProgress, ListItemIcon, ListItemText, MenuItem,
} from '@mui/material';
import { createDirectChat } from 'apis/chat';
import { PATHS } from 'constants';
import { useMutation } from 'react-query';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { setSnackbar } from 'store/layout';
import { setPathParam } from 'utils';
import SendIcon from '@mui/icons-material/Send';

export default function SendMessageMenuItem({ directChatId, userId }) {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { mutate, isLoading } = useMutation(createDirectChat, {
    onError: () => {
      dispatch(setSnackbar({ message: 'خطا در ساخت گفت‌وگو.', severity: 'error' }));
    },
    onSuccess: (data) => {
      const chatId = data.data.id;
      navigate(
        setPathParam(PATHS.chat, 'chatId', chatId),
      );
    },
  });

  const onClick = () => {
    if (directChatId) {
      navigate(
        setPathParam(PATHS.chat, 'chatId', directChatId),
      );
    } else {
      mutate({ participants: [userId] });
    }
  };

  return (
    <MenuItem onClick={onClick}>
      <ListItemIcon>
        {isLoading ? <CircularProgress size={24} /> : <SendIcon />}
      </ListItemIcon>
      <ListItemText>ارسال پیام</ListItemText>
    </MenuItem>
  );
}
