import { Stack } from '@mui/material';
import { useState } from 'react';
import { useIsDesktop } from 'utils';
import DesktopAppBar, {
  DESKTOP_APPBAR_HEIGHT,
} from 'components/DesktopAppBar/DesktopAppBar';
import LoadingPage from 'components/LoadingPage/LoadingPage';
import SearchBoxMarfook from 'components/SearchBoxMarfook/SearchBox';
import { useSelector } from 'react-redux';
import { selectMe } from 'store/auth';
import ContentsMarfook from 'components/ContentsMarfook/ContentsMarfook';
import { useNavigate } from 'react-router-dom';

export default function MarfookAdmin() {
  const isDesktop = useIsDesktop();

  const navigate = useNavigate();

  const [filter, setFilter] = useState({
    status: ['MA', 'MR', 'MW'],
    search: '',
    publish_date_from__gt: '',
    publish_date_to__lt: '',
    // statusOrder: '-MW',
    // timeOrder: '-created_at',
    ordering: '-created_at',
    page: 1,
    limit: 10,
  });

  const me = useSelector(selectMe);

  if (!me) return <LoadingPage />;

  if (
    !me?.groups?.includes('MarfookManager') &&
    !me?.groups?.includes('MarfookExpert')
  )
    return navigate('/');
  return (
    <>
      {isDesktop && <DesktopAppBar />}

      <Stack
        sx={{
          height: '100%',
          marginTop: isDesktop ? DESKTOP_APPBAR_HEIGHT : 0,
        }}
      >
        <SearchBoxMarfook searchConfig={filter} setFilter={setFilter} />

        <ContentsMarfook searchConfig={filter} withTypeIcon />
      </Stack>
    </>
  );
}
