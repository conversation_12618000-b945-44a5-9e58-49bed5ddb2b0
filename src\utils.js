import { useTheme } from '@emotion/react';
import { useMediaQuery } from '@mui/material';
import { downloadFile } from 'apis/content';
import { Cookies } from 'react-cookie';

export function setToken(token) {
  const cookie = new Cookies();
  cookie.set('token', token, {
    path: '/',
    expires: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
    secure: true,
    httpOnly: true,
  });
  localStorage.setItem('token', token);
}

export function removeToken() {
  const cookie = new Cookies();
  cookie.remove('token', { path: '/' });
  cookie.remove('sessionid');
  localStorage.removeItem('token');
}

export function getToken() {
  return localStorage.getItem('token');
}

export function isLoggedIn() {
  return !!getToken();
}

export function removeLeadingZeroFromMobile(mobile) {
  if (mobile && mobile.charAt(0) === '0') return mobile.slice(1);
  return mobile;
}

export function setPathParam(path, param, value) {
  return path.replace(`:${param}`, value);
}

export function formatNumber(num, digits = 0) {
  const lookup = [
    { value: 1, symbol: '' },
    { value: 1e3, symbol: 'k' },
    { value: 1e6, symbol: 'M' },
    { value: 1e9, symbol: 'G' },
    { value: 1e12, symbol: 'T' },
    { value: 1e15, symbol: 'P' },
    { value: 1e18, symbol: 'E' },
  ];
  const item = lookup
    .slice()
    .reverse()
    .find(i => num >= i.value);

  return item ? (num / item.value).toFixed(digits) + item.symbol : '0';
}

export function fa2En(s) {
  return s.replace(/[۰-۹]/g, d => '۰۱۲۳۴۵۶۷۸۹'.indexOf(d));
}
export function a2En(s) {
  return s.replace(/[٠-٩]/g, d => '٠١٢٣٤٥٦٧٨٩'.indexOf(d));
}

export function buildFullName(firstName, lastName) {
  const hasName = !!firstName || !!lastName;
  return hasName ? `${firstName} ${lastName}` : 'بدون نام';
}

export function isImage(fileType) {
  return fileType?.startsWith('image');
}

export function isVideo(fileType) {
  return fileType?.startsWith('video');
}

export function isAudio(fileType) {
  return fileType?.startsWith('audio');
}

export function isFile(fileType) {
  return fileType?.startsWith('application');
}

export function trimWhiteSpaces(inStr) {
  return inStr.replaceAll(' ', '').replaceAll('\n', '').replaceAll('\t', '');
}

export async function ajaxDownload(url, name, setProgress) {
  const response = await downloadFile(url, setProgress);
  const href = URL.createObjectURL(response.data);

  // create "a" HTML element with href to file & click
  const link = document.createElement('a');
  link.href = href;
  link.setAttribute('download', name); // or any other extension
  document.body.appendChild(link);
  link.click();

  // clean up "a" element & remove ObjectURL
  document.body.removeChild(link);
  URL.revokeObjectURL(href);

  setProgress(0);
}

export function createIntentUrl(scheme, action, data) {
  const APP_PACKAGE_NAME = 'ir.haghighatgram.twa';
  const HOST = 'tam';
  const queries = Object.entries(data).reduce(
    (queryParam, [key, value]) => `${queryParam}&${key}=${value}`,
    '',
  );
  return `intent://${HOST}?${queries}#Intent;scheme=${scheme};package=${APP_PACKAGE_NAME};action=${action};end`;
}

export function useIsDesktop() {
  const theme = useTheme();
  return useMediaQuery(theme.breakpoints.up('lg'));
}

export function jalaliToGregorian(date) {
  return date.calendar('gregory').format('YYYY-MM-DD');
}
