import { Stack } from '@mui/material';
import { useSelector } from 'react-redux';
import { selectMessageUploadProgress } from 'store/messenger';
import CircularProgressWithValue from 'components/CircularProgressWithValue/CircularProgressWithValue';
import MessageContainer from '../MessageContainer/MessageContainer';
import MessageText from '../MessageText/MessageText';
import FileDownloadButton from '../FileDownloadButton/FileDownloadButton';

export default function FileMessage({
  message, myId, withAvatar, isAdmin,
}) {
  const { file, text, deliveryToken } = message;
  const uploadProgress = useSelector(selectMessageUploadProgress(deliveryToken));

  return (
    <MessageContainer message={message} myId={myId} withAvatar={withAvatar} isAdmin={isAdmin}>
      <Stack direction="row-reverse" spacing={1} alignItems="center">

        {uploadProgress && uploadProgress < 100
          ? <CircularProgressWithValue value={uploadProgress} />
          : <FileDownloadButton url={file} name={text} />}

        <MessageText sx={{ whiteSpace: 'nowrap', textOverflow: 'ellipsis', overflow: 'hidden' }}>{text}</MessageText>
      </Stack>
    </MessageContainer>
  );
}
