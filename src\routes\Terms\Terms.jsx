/* eslint-disable max-len */
import { Stack, Typography, useTheme } from '@mui/material';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import MyLink from 'components/MyLink/MyLink';
import IconSax from 'components/IconSax/IconSax';
import { useIsDesktop } from 'utils';
import DesktopAppBar from 'components/DesktopAppBar/DesktopAppBar';
import { Download } from '@mui/icons-material';

function Title({ children, sx }) {
  return (
    <Typography sx={{ color: 'white', ...sx }} variant="h6">
      {children}
    </Typography>
  );
}

function Body({ children, sx }) {
  return (
    <Typography sx={{ color: 'white', ...sx }} align="justify">
      {children}
    </Typography>
  );
}

function ExternalLinkButton({ to, children }) {
  return (
    <MyLink to={to} target="_blank" rel="noopener noreferrer">
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          bgcolor: '#C4F8F3',
          borderRadius: '8px',
          pr: 2,
          pl: 2,
          mx: 2,
          mt: '30px',
          height: '50px',
        }}
      >
        {children}
      </Stack>
    </MyLink>
  );
}

export default function Terms() {
  const theme = useTheme();

  const isDesktop = useIsDesktop();

  return (
    <>
      {isDesktop && <DesktopAppBar />}

      <Stack
        sx={{
          backgroundColor: 'black',
          width: '100%',
          overflowY: 'scroll',
        }}
        className="no-scrollbar"
      >
        <Stack flexGrow={1} justifyContent="center">
          <IconSax
            name="shield-tick"
            sx={{
              mt: 4,
              color: theme.palette.primary.main,
              width: '128px',
              height: '128px',
              marginLeft: 'auto',
              marginRight: 'auto',
            }}
          />
          <Typography
            sx={{
              color: '#8C8C9F',
              fontWeight: 700,
              fontSize: '16px',
              mt: 2,
            }}
            align="center"
          >
            مقررات و آموزش بهره‌برداری
          </Typography>
        </Stack>

        <MyLink to={'#'}>
          <Stack
            direction="row"
            justifyContent="space-between"
            sx={{
              bgcolor: '#23262F',
              borderRadius: '8px',
              pr: 2,
              pl: 2,
              mr: 2,
              ml: 2,
              mt: '50px',
              height: '54px',
            }}
          >
            <Typography
              color="white"
              sx={{
                height: '54px',
                lineHeight: '54px',
                paddingLeft: '8px',
                fontSize: '14px',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <IconSax
                name="copy-file"
                sx={{ color: '#ccc', mr: 1, height: '100%' }}
              />
              <Typography style={{ color: '#aaa ' }}>
                سوالات متداول و راهنمایی (در حال آماده‌سازی)
              </Typography>
            </Typography>

            <Typography
              color="white"
              sx={{
                height: '54px',
                lineHeight: '54px',
                fontSize: '15px !important',
              }}
            >
              <ArrowBackIosNewIcon
                sx={{
                  fontSize: '18px !important',
                  verticalAlign: 'middle',
                  marginLeft: '4px',
                }}
              />
            </Typography>
          </Stack>
        </MyLink>

        <ExternalLinkButton
          mt="20px"
          to="https://tam-front-production.s3.ir-thr-at1.arvanstorage.ir/tutorial%2Ftam.help.mp4?versionId="
        >
          <Typography
            color="black"
            sx={{
              height: '54px',
              lineHeight: '54px',
              paddingLeft: '8px',
              fontSize: '14px',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <IconSax
              name="video-circle"
              sx={{ color: '#000', mr: 1, height: '100%' }}
            />
            <Typography sx={{ fontSize: '16px' }}>آموزش تام</Typography>
          </Typography>

          <Typography
            color="black"
            sx={{
              height: '54px',
              lineHeight: '54px',
              fontSize: '12px !important',
            }}
          >
            تماشای فیلم
            <ArrowBackIosNewIcon
              sx={{
                fontSize: '18px !important',
                verticalAlign: 'middle',
                marginLeft: '4px',
              }}
            />
          </Typography>
        </ExternalLinkButton>

        <ExternalLinkButton mt="20px" to="/tutorial/about-taam.mp4">
          <Typography
            color="black"
            sx={{
              height: '54px',
              lineHeight: '54px',
              paddingLeft: '8px',
              fontSize: '14px',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <IconSax
              name="video-circle"
              sx={{ color: '#000', mr: 1, height: '100%' }}
            />
            <Typography sx={{ fontSize: '16px' }}>تیزر معرفی تام</Typography>
          </Typography>

          <Typography
            color="black"
            sx={{
              height: '54px',
              lineHeight: '54px',
              fontSize: '12px !important',
            }}
          >
            تماشای فیلم
            <ArrowBackIosNewIcon
              sx={{
                fontSize: '18px !important',
                verticalAlign: 'middle',
                marginLeft: '4px',
              }}
            />
          </Typography>
        </ExternalLinkButton>

        <ExternalLinkButton to="https://storage.haghighatgram.ir/tam-apk-v3.apk">
          <Typography
            color="black"
            sx={{
              height: '54px',
              lineHeight: '54px',
              fontSize: '14px',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <IconSax
              name="video-circle"
              sx={{ color: '#000', mr: 1, height: '100%' }}
            />
            <Typography sx={{ fontSize: '16px' }}>
              دانلود آخرین نسخه تام
            </Typography>
          </Typography>

          <Typography
            color="black"
            sx={{
              height: '54px',
              lineHeight: '54px',
              fontSize: '12px !important',
            }}
          >
            دانلود
            <Download
              sx={{
                fontSize: '18px !important',
                verticalAlign: 'middle',
                marginLeft: '4px',
              }}
            />
          </Typography>
        </ExternalLinkButton>

        <Stack
          sx={{
            background: '#292D38',
            borderRadius: isDesktop ? '24px' : '24px 24px 0 0',
            mt: 4,
            p: 4,
            flexGrow: 1,
          }}
        >
          <Title sx={{ color: '#11a6a1', fontWeight: 'bold' }}>
            قوانین عمومی
          </Title>
          <Body>
            تام بر مبنای قوانین جمهوری اسلامی ایران تهیه شده و کاربر ملزم به
            رعایت شئونات اسلامی و قوانین جاری جمهوری اسلامی ایران است.
            <br /> در صورت نقض، کلیه مسئولیت‌ها و عواقب ناشی از آن بر عهده کاربر
            می‌باشد.
          </Body>

          <Title sx={{ mt: 2, color: '#11a6a1', fontWeight: 'bold' }}>
            رعایت حریم شخصی
          </Title>
          <Body>
            تام نسبت به اطلاعات کاربرانی که از آن استفاده می‌کنند، مسئولیت داشته
            و از آنها محافظت می‌کند. محتوای منتشر شده توسط کاربران برای دیگر
            استفاده کنندگان در این سامانه قابل استفاده و بهره‌برداری خواهد بود.
          </Body>

          <Title sx={{ mt: 2, color: '#11a6a1', fontWeight: 'bold' }}>
            قوانین تام
          </Title>
          <Body>
            کاربران تام، مسئولیت کامل محتوای منتشر شده توسط خود را بر عهده
            خواهند داشت. کاربران مجاز به انتشار مطالب تحت قانون حقوق مالکیت
            معنوی نبوده و در صورت مشخص شدن، مطالب منتشر شده حذف خواهد شد.
          </Body>
          <Body>
            در سامانه تام، کاربران ترجیحا محتوایی که توسط خود تولید کرده‌اند
            منتشر خواهند نمود.
          </Body>
        </Stack>
      </Stack>
    </>
  );
}
