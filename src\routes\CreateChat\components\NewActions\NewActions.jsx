import { Paper, Stack, Typography } from '@mui/material';
import IconSax from 'components/IconSax/IconSax';
import MyLink from 'components/MyLink/MyLink';
import { CHAT_TYPES, PATHS } from 'constants';

function Action({ action, icon, to }) {
  return (
    <MyLink to={to}>
      <Stack direction="row" spacing={1}>
        <IconSax name={icon} />
        <Typography>{action}</Typography>
      </Stack>
    </MyLink>
  );
}

function genSelectContactPageUrl(chatType) {
  return `${PATHS.createChatSelectUser}?chatType=${chatType}`;
}

export default function NewActions() {
  return (
    <Paper sx={{ p: 2 }}>
      <Stack spacing={2}>
        <Action
          action="ایجاد گروه جدید"
          icon="profile-2user"
          to={genSelectContactPageUrl(CHAT_TYPES.GROUP)}
        />

        <Action
          action="ایجاد کانال جدید"
          icon="bold/direct"
          to={genSelectContactPageUrl(CHAT_TYPES.CHANNEL)}
        />

        <Action
          action="ایجاد مخاطب جدید"
          icon="user-circle-add"
          to={PATHS.createContact}
        />
      </Stack>
    </Paper>
  );
}
