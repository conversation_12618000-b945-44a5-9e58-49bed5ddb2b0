import { useTheme } from '@emotion/react';
import { IconButton } from '@mui/material';
import SendIcon from '@mui/icons-material/Send';

export default function SendMessageButton({ disabled, onSendMessageClick }) {
  const theme = useTheme();

  return (
    <IconButton onClick={onSendMessageClick} disabled={disabled}>
      <SendIcon sx={{
        color: disabled ? theme.palette.secondary.main : theme.palette.primary.main,
      }}
      />
    </IconButton>
  );
}
