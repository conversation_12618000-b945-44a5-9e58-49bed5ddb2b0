import { Box, Stack } from '@mui/material';
import { useQuery } from 'react-query';
import { getContent } from 'apis/content';
import MyLink from 'components/MyLink/MyLink';
import { setPathParam } from 'utils';
import { PATHS } from 'constants';
import MessageContainer from '../MessageContainer/MessageContainer';
import Image from '../Image/Image';
import MessageText from '../MessageText/MessageText';

function trimText(text) {
  if (text.length > 50) {
    return `${text.substring(0, 50)}...`;
  }
  return text;
}

export default function ContentMessage({
  myId, withAvatar, message, isAdmin,
}) {
  const contentId = message.text;
  const { isLoading, data, error } = useQuery(
    ['content', contentId],
    () => getContent(contentId),
  );

  if (isLoading) return '';
  const isDataRemoved = error?.response?.status === 404;
  return (
    <MessageContainer message={message} myId={myId} withAvatar={withAvatar} isAdmin={isAdmin}>
      {isDataRemoved && <MessageText>محتوا حذف شده است.</MessageText>}

      {data?.data && (
        <MyLink to={setPathParam(PATHS.content, 'contentId', contentId)}>
          <Stack spacing={1} alignItems="center">
            <Box sx={{ position: 'relative' }}>
              <Image src={data.data.preview} />
            </Box>

            <MessageText sx={{ width: '100%', fontStyle: 'italic' }}>{trimText(data.data.description)}</MessageText>
          </Stack>
        </MyLink>
      )}
    </MessageContainer>
  );
}
