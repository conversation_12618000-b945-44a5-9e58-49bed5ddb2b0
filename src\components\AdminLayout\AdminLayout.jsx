import Container from '@mui/material/Container';
import { useNavigate, Outlet, useLocation, Link } from 'react-router-dom';
import { Box, useMediaQuery, useTheme, Grid, Typography } from '@mui/material';
import { PATHS } from 'constants';
import { useDispatch, useSelector } from 'react-redux';
import { selectSidebarCollapsed, selectTitle } from 'store/layout';
import { isLoggedIn } from 'utils';
import { useQuery } from 'react-query';
import { GET_ME_URL, getMe } from 'apis/auth';
import { setMe } from 'store/auth';
import { useEffect, useState } from 'react';
import MyAppBar from './components/MyAppBar/MyAppBar';
import MySnackbar from './components/MySnackbar/MySnackbar';
import Sidebar from './components/Sidebar/Sidebar';
import DesktopAppBar from '../DesktopAppBar/DesktopAppBar';

const APP_NAME = 'تام';
const CONTENT_PAGE_REGEX = /content.+\d$/;
const ANNOUNCEMENT_VIEW_PAGE_REGEX = /announcement\/view.+\d$/;
const ORGANIZATION_VIEW_PAGE_REGEX = /organization\/view.+\d$/;
const GROUP_MSG_VIEW_PAGE_REGEX = /group-msg\/view.+\d$/;
const CONTENT_ANALYTICS_PAGE = /content.+\d.analytics$/;
const CHAT_PAGE_REGEX = /chat.+\d$/;
const CHAT_INFO_PAGE_REGEX = /chat.+\d.info$/;
const CONTENT_INTERACTIONS_PAGE = /content.+\d.interactions.+$/;

function isContentPage(path) {
  return path.search(CONTENT_PAGE_REGEX) > 0;
}

function getLayoutConfig(path) {
  const config = {
    background: '#F9F9F9',
    appbar: {
      hasAppBar: true,
      title: APP_NAME,
      titleCenter: true,
      titleVariant: 'h6',
      hasBack: false,
      hasLogo: false,
    },
    hasBottomNav: true,
    containerGutter: true,
    hasSidebar: true,
  };

  if (path.search(ANNOUNCEMENT_VIEW_PAGE_REGEX) > 0) {
    config.appbar.title = 'لیست اعلانات';
    config.appbar.hasBack = true;
    config.appbar.titleCenter = false;
    config.appbar.titleVariant = '';
    config.hasBottomNav = false;
    return config;
  }

  if (path.search(ORGANIZATION_VIEW_PAGE_REGEX) > 0) {
    config.appbar.title = 'مشاهده سازمان';
    config.appbar.hasBack = true;
    config.appbar.titleCenter = false;
    config.appbar.titleVariant = '';
    config.hasBottomNav = false;
    return config;
  }

  if (path.search(GROUP_MSG_VIEW_PAGE_REGEX) > 0) {
    config.appbar.title = 'مشاهده پیام گروهی';
    config.appbar.hasBack = true;
    config.appbar.titleCenter = false;
    config.appbar.titleVariant = '';
    config.hasBottomNav = false;
    return config;
  }

  switch (path) {
    case PATHS.admin.homepage:
      config.appbar.hasBack = true;
      config.appbar.title = 'پنل مدیریت';
      config.appbar.titleCenter = false;
      config.appbar.titleVariant = '';
      config.containerGutter = false;
      config.hasBottomNav = false;
      config.hasSidebar = false;
      return config;

    case PATHS.admin.announcementCreate:
      config.appbar.title = 'اعلان جدید';
      config.appbar.hasBack = true;
      config.appbar.titleCenter = false;
      config.appbar.titleVariant = '';
      config.hasBottomNav = false;
      return config;

    case PATHS.admin.announcementList:
      config.appbar.title = 'لیست اعلانات';
      config.appbar.hasBack = true;
      config.appbar.titleCenter = false;
      config.appbar.titleVariant = '';
      config.hasBottomNav = false;
      return config;

    case PATHS.admin.groupMsgCreate:
      config.appbar.title = 'ایجاد پیام گروهی';
      config.appbar.hasBack = true;
      config.appbar.titleCenter = false;
      config.appbar.titleVariant = '';
      config.hasBottomNav = false;
      return config;

    case PATHS.admin.groupMsgList:
      config.appbar.title = 'مشاهده لیست پیام ها';
      config.appbar.hasBack = true;
      config.appbar.titleCenter = false;
      config.appbar.titleVariant = '';
      config.hasBottomNav = false;
      return config;

    case PATHS.admin.organizationCreate:
      config.appbar.title = 'ایجاد سازمان';
      config.appbar.hasBack = true;
      config.appbar.titleCenter = false;
      config.appbar.titleVariant = '';
      config.hasBottomNav = false;
      return config;

    case PATHS.admin.organizationList:
      config.appbar.title = 'لیست سازمان ها';
      config.appbar.hasBack = true;
      config.appbar.titleCenter = false;
      config.appbar.titleVariant = '';
      config.hasBottomNav = false;
      return config;

    case PATHS.admin.organizationView:
      config.appbar.title = 'مشاهده سازمان';
      config.appbar.hasBack = true;
      config.appbar.titleCenter = false;
      config.appbar.titleVariant = '';
      config.hasBottomNav = false;
      return config;

    default:
      return config;
  }
}

function useGetLayoutConfig(pathname) {
  const [config, setConfig] = useState({
    background: 'white',
    appbar: {
      hasAppBar: true,
      title: APP_NAME,
      titleCenter: true,
      titleVariant: 'h6',
      hasBack: false,
      hasLogo: false,
    },
    hasBottomNav: true,
    containerGutter: true,
  });

  useEffect(() => {
    setConfig(getLayoutConfig(pathname));
  }, [pathname]);

  return config;
}

export default function AdminLayout({ children }) {
  const location = useLocation();
  const config = useGetLayoutConfig(location.pathname);
  const collapsed = useSelector(selectSidebarCollapsed);

  const title = useSelector(selectTitle);
  if (isContentPage(location.pathname)) {
    config.appbar.title = title;
  }

  const bottomPadding = (() => {
    if (config.hasBottomNav) return 7;
    if (config.containerGutter) return 2;
    return 0;
  })();

  const navigate = useNavigate();
  if (isLoggedIn()) {
    const dispatch = useDispatch();
    useQuery(GET_ME_URL, getMe, {
      onSuccess: data => {
        dispatch(setMe(data.data));
        if (data.data && !data.data.filled_registration_form) {
          navigate(PATHS.registration_form);
        }
      },
    });
  }

  // todo: check admin access

  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));

  if (!isDesktop) {
    return (
      <Box
        sx={{
          position: 'fixed',
          pb: isDesktop ? 0 : bottomPadding,
          pt: config.appbar.hasAppBar ? 7 : 2,
          bottom: 0,
          top: 0,
          right: 0,
          left: 0,
          background: config.background,
          height: '100%',
          overflowY: 'scroll',
        }}
      >
        <MyAppBar {...config.appbar} />

        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            gap: '5px',
          }}
        >
          <Typography>
            پنل مدیریت فقط از طریق مرورگر دسکتاپ قابل مشاهده است.
          </Typography>
          <Typography>
            <Link to="/">بازگشت به صفحه اصلی</Link>
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        position: 'fixed',
        pb: 0,
        pt: 7,
        bottom: 0,
        top: 0,
        right: 0,
        left: 0,
        background: config.background,
        height: '100%',
        overflowY: 'scroll',
      }}
      className="scrollbar-thin"
    >
      <DesktopAppBar />
      <Sidebar />
      <Container
        maxWidth="100%"
        disableGutters={!config.containerGutter}
        sx={{
          p: '0!important',
          pl: collapsed ? '100px!important' : '320px!important',
          height: '100%',
        }}
        className="scrollbar-thin"
      >
        {children}

        <Grid container xs={12}>
          <Grid xs={12} px={2}>
            <Outlet />
          </Grid>
        </Grid>
      </Container>
      <MySnackbar />
    </Box>
  );
}
