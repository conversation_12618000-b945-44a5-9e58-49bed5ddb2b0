import { useEffect } from 'react';
import { WS_MESSAGE_ACTIONS, MESSAGE_DELIVER_STATUS } from 'constants';
import {
  Message, Author,
} from 'dtos/messenger';
import useAuthenticatedWebsocket from './useAuthenticatedWebSocket';
import useSendMessageReceived from './useSendMessageReceived';

function parseReplyTo(replyTo) {
  if (!replyTo) return null;

  const author = new Author(
    replyTo.author.id,
    replyTo.author.avatar,
    replyTo.author.first_name,
    replyTo.author.last_name,
    replyTo.author.display_username,
  );
  return new Message(
    author,
    replyTo.chat,
    replyTo.text,
    replyTo.type,
    null,
    replyTo.delivery_token,
    null,
    null,
    null,
    replyTo.file,
    null,
    null,
  );
}

function parseMessage(message) {
  /** Convert message received from service to Message dto. */
  const author = new Author(
    message.author.id,
    message.author.avatar,
    message.author.first_name,
    message.author.last_name,
    message.author.display_username,
  );

  let forwardedFrom = null;
  if (message.forwarded_from) {
    forwardedFrom = new Author(
      message.forwarded_from.id,
      message.forwarded_from.avatar,
      message.forwarded_from.first_name,
      message.forwarded_from.last_name,
      message.forwarded_from.display_username,
    );
  }

  return new Message(
    author,
    message.chat,
    message.text,
    message.type,
    message.delivery_status,
    message.delivery_token,
    message.created_at,
    message.sent_by_me,
    null,
    message.file,
    forwardedFrom,
    parseReplyTo(message.reply_to),
    message.display_text,
    message.extra_data,
  );
}

function shouldSendReceiveEvent(message) {
  return !message.sentByMe && message.deliveryStatus === MESSAGE_DELIVER_STATUS.SENT;
}

function handleMessageAction(addMessage, sendMessageReceived, messageData) {
  const parsedMessage = parseMessage(messageData);
  if (shouldSendReceiveEvent(parsedMessage)) sendMessageReceived(parsedMessage.deliveryToken);

  addMessage(parsedMessage);
}

function handleReceivedMessageAction(updateMessageDeliveryStatus, messageData) {
  updateMessageDeliveryStatus({
    deliveryToken: messageData.delivery_token,
    deliveryStatus: MESSAGE_DELIVER_STATUS.RECEIVED,
  });
}

function handleDeleteMessageAction(deleteMessage, messageData) {
  deleteMessage({
    deliveryToken: messageData.delivery_token,
    chatId: messageData.chat_id,
  });
}

function handleReadMessageAction(updateMessageDeliveryStatus, messageData) {
  updateMessageDeliveryStatus({
    deliveryToken: messageData.delivery_token,
    deliveryStatus: MESSAGE_DELIVER_STATUS.READ,
  });
}

function handleWsMessage(
  chatId,
  message,
  sendMessageReceived,
  addMessage,
  updateMessageDeliveryStatus,
  deleteMessage,
) {
  if (!message) return;

  const { action, body } = message;
  if (Number(body.chat_id).toString() !== Number(chatId).toString()) return;

  if (action === WS_MESSAGE_ACTIONS.MESSAGE) {
    handleMessageAction(addMessage, sendMessageReceived, body);
  }

  if (action === WS_MESSAGE_ACTIONS.RECEIVED_MESSAGE) {
    handleReceivedMessageAction(updateMessageDeliveryStatus, body);
  }

  if (action === WS_MESSAGE_ACTIONS.DELETE_MESSAGE) {
    handleDeleteMessageAction(deleteMessage, body);
  }

  if (action === WS_MESSAGE_ACTIONS.READ_MESSAGE) {
    handleReadMessageAction(updateMessageDeliveryStatus, body);
  }
}

export default function useListenOnChatMessages(
  chatId,
  addMessage,
  updateMessageDeliveryStatus,
  deleteMessage,
) {
  const sendMessageReceived = useSendMessageReceived();
  const { lastJsonMessage } = useAuthenticatedWebsocket();

  useEffect(() => {
    if (lastJsonMessage) {
      handleWsMessage(
        chatId,
        lastJsonMessage,
        sendMessageReceived,
        addMessage,
        updateMessageDeliveryStatus,
        deleteMessage,
      );
    }
  }, [lastJsonMessage]);
}
