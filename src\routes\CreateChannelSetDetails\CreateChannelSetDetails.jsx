import { Stack, TextField, Typography } from '@mui/material';
import { DEFAULT_APPBAR_HEIGHT } from 'components/CustomAppBar/constants';
import { useDispatch, useSelector } from 'react-redux';
import { selectSelectedContacts } from 'store/messenger';
import IconSax from 'components/IconSax/IconSax';
import EditableAvatar from 'components/EditableAvatar/EditableAvatar';
import { useState } from 'react';
import { useMutation } from 'react-query';
import { createChat } from 'apis/chat';
import { CHAT_TYPES, PATHS } from 'constants';
import { setSnackbar } from 'store/layout';
import { useNavigate } from 'react-router-dom';
import uploadFile from 'apis/storage';
import MyAppBar from './components/MyAppBar/MyAppBar';
import Contacts from './components/Contacts/Contacts';

export default function CreateChannelSetDetails() {
  const contacts = useSelector(selectSelectedContacts);
  const [icon, setIcon] = useState(null);
  const [name, setName] = useState('');
  const [dsc, setDsc] = useState('');

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const createChatMutation = useMutation(async () => {
    let avatarId = null;
    if (icon) {
      const uploadedAvatar = await uploadFile(icon, () => { });
      avatarId = uploadedAvatar.data.id;
    }

    const contactsIds = contacts.map((contact) => contact.user.id);
    return createChat(contactsIds, name, avatarId, CHAT_TYPES.CHANNEL, dsc);
  }, {
    onSuccess: () => {
      dispatch(setSnackbar({ message: 'کانال با موفقیت ساخته شد', severity: 'success' }));
      navigate(PATHS.chats);
    },
    onError: () => {
      dispatch(setSnackbar({ message: 'خطا در ساخت کانال', severity: 'error' }));
    },
    onSettled: () => { },
  });

  return (
    <>
      <MyAppBar onCreateClick={() => createChatMutation.mutate()} />
      <Stack sx={{ pt: DEFAULT_APPBAR_HEIGHT }}>

        <Stack direction="row" alignItems="center" sx={{ mt: 2 }}>
          <EditableAvatar
            size="56px"
            placeholderIcon={<IconSax name="bold/camera" />}
            avatar={icon}
            setAvatar={setIcon}
          />
          <TextField
            sx={{ flexGrow: 1, ml: 2 }}
            label="نام کانال را وارد کنید"
            value={name}
            onChange={(e) => setName(e.target.value)}
          />
        </Stack>

        <TextField
          label="توضیحات"
          sx={{ mt: 2 }}
          multiline
          rows={4}
          value={dsc}
          onChange={(e) => setDsc(e.target.value)}
        />
        <Typography sx={{
          fontSize: '14px', fontWeight: 400, color: '#AEAEB2', mt: 1,
        }}
        >
          شما می‌توانید یک توضیح دلخواه برای کانال خود ارایه کنید
        </Typography>

        <Contacts contacts={contacts} />
      </Stack>
    </>
  );
}
