import { Stack } from '@mui/material';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { setPathParam } from 'utils';
import { PATHS, CHAT_TYPES } from 'constants';
import { useDispatch } from 'react-redux';
import { addContact } from 'store/messenger';
import { createContact } from 'dtos/messenger';
import useLoadContacts from 'hooks/useLoadContacts';
import { useMutation } from 'react-query';
import { createChat } from 'apis/chat';
import { setSnackbar } from 'store/layout';
import MyAppBar from './components/MyAppBar/MyAppBar';
import NewActions from './components/NewActions/NewActions';
import Contacts from '../../components/Contacts/Contacts';

const APPBAR_HEIGHT = '128px';

function createDirectChatWithUser({ userId }) {
  return createChat(
    [userId],
    undefined,
    undefined,
    CHAT_TYPES.DIRECT,
  );
}

export default function CreateChat() {
  const navigate = useNavigate();

  const dispatch = useDispatch();
  const { mutate } = useMutation(createDirectChatWithUser, {
    onError: () => {
      dispatch(setSnackbar({ message: 'خطا در ساخت گفت‌وگو.', severity: 'error' }));
    },
    onSuccess: (data) => {
      const chatId = data.data.id;
      navigate(
        setPathParam(PATHS.chat, 'chatId', chatId),
      );
    },
  });

  const gotoChatPage = (contact) => {
    if (contact.direct_chat_id) {
      navigate(
        setPathParam(PATHS.chat, 'chatId', contact.direct_chat_id),
      );
    } else {
      mutate({ userId: contact.user.id });
    }
  };

  const [searchValue, setSearchValue] = useState('');

  const handleSearchChange = (event) => {
    setSearchValue(event.target.value);
  };

  const contacts = useLoadContacts();

  useEffect(() => {
    Object.values(contacts || []).forEach(
      (contact) => dispatch(addContact(createContact(
        contact.id,
        contact.user.id,
        contact.user_first_name,
        contact.user_last_name,
        contact.direct_chat_id,
      ))),
    );
  }, [contacts]);

  return (
    <>
      <MyAppBar onSearchChange={handleSearchChange} />
      <Stack
        sx={{
          paddingTop: APPBAR_HEIGHT,
          pb: 2,
          overflowY: 'scroll',
          height: '100%',
        }}
      >
        <NewActions />

        <Contacts
          sx={{ mt: 3 }}
          onContactClick={gotoChatPage}
          contacts={contacts}
          searchValue={searchValue}
        />
      </Stack>
    </>
  );
}
