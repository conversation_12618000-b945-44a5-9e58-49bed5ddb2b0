import StarIcon from '@mui/icons-material/Star';
import { Button, IconButton } from '@mui/material';
import { updateContentElection, updateContentEvaluation } from 'apis/content';
import { PATHS } from 'constants';
import { useMutation, useQueryClient } from 'react-query';
import { useSelector } from 'react-redux';
import { selectMe } from 'store/auth';
import { useState } from 'react';
import { QuestionMark } from '@mui/icons-material';

export default function ElectButton({
  contentId,
  elected = false,
  evaluationRequested = false,
}) {
  const queryClient = useQueryClient();
  const { mutate: mutateElect, isLoading } = useMutation(
    elected => updateContentElection({ contentId, elected }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries([PATHS.content, contentId.toString()]);
      },
    },
  );
  const { mutate: mutateEvaluate, isLoading: isLoadingEvaluate } = useMutation(
    evaluation_requested =>
      updateContentEvaluation({ contentId, evaluation_requested }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries([PATHS.content, contentId.toString()]);
      },
    },
  );

  const me = useSelector(selectMe);
  const [state, setState] = useState(
    elected ? 'elect' : evaluationRequested ? 'evaluate' : '!elect',
  );

  if (!me || (!elected && !me.can_see_admin_panel)) return '';
  if (!me.can_see_admin_panel)
    return (
      <Button disabled startIcon={<StarIcon />} variant="contained">
        منتخب
      </Button>
    );

  function onSelect(action) {
    if (action === 'elect') {
      mutateEvaluate(false);
      mutateElect(true);
      setState('elect');
    } else if (action === '!elect') {
      mutateEvaluate(false);
      mutateElect(false);
      setState('!elect');
    } else if (action === 'evaluate') {
      mutateElect(false);
      mutateEvaluate(true);
      setState('evaluate');
    }
  }

  return (
    <IconButton variant="contained" loading={isLoading || isLoadingEvaluate}>
      {state === 'elect' && (
        <StarIcon color="warning" onClick={() => onSelect('!elect')} />
      )}
      {state === 'evaluate' && (
        <QuestionMark onClick={() => onSelect('elect')} />
      )}
      {state === '!elect' && (
        <StarIcon color="secondary" onClick={() => onSelect('evaluate')} />
      )}
    </IconButton>
  );
}
