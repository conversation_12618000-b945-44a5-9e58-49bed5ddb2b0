import { Stack, Typography } from '@mui/material';

export default function SumRow({ title, value, sx }) {
  const color = '#2C2C2E';

  return (
    <Stack direction="row" justifyContent="center" sx={{ ...sx }}>
      <Typography sx={{ color, fontSize: '16px', fontWeight: 400 }} mr={2}>{title}</Typography>
      <Typography sx={{ color, fontSize: '16px', fontWeight: 700 }}>{value}</Typography>
    </Stack>
  );
}
