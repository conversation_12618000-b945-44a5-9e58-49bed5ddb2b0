import useAuthenticatedWebsocket from './useAuthenticatedWebSocket';

export default function useSendGetChatMessages() {
  const { sendJsonMessage } = useAuthenticatedWebsocket();

  return (chatId, startFromMsgId, pageLen) => {
    sendJsonMessage({
      action: 'get_chat_messages',
      body: {
        chat_id: chatId,
        start_from_message_id: startFromMsgId,
        page_len: pageLen,
      },
    });
  };
}
