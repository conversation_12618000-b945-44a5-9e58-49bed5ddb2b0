import { Box, Fade, IconButton, SvgIcon, Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import { addInteraction, deleteInteraction } from 'apis/content';
import { useQueryClient } from 'react-query';
import { setPathParam } from 'utils';
import { PATHS, LIKE, FAVORITE, DISLIKE } from 'constants';
import { useSelector } from 'react-redux';
import { selectMe } from 'store/auth';
import { useNavigate } from 'react-router-dom';
import LoginBottomSheet from '../LoginBottomSheet/LoginBottomSheet';

export default function InteractionButton({
  text,
  icon,
  filledIcon,
  type,
  interaction,
  contentId,
  authRequired,
  iconColor = '#64676A',
  textColor = '#64676A',
  flexDirection = 'row',
  iconSize = 'medium',
  setToggledState,
  toggledState,
}) {
  const [state, setState] = useState(!!interaction);
  const [loading, setLoading] = useState(false);
  const [textCopy, setTextCopy] = useState(text);
  const color = state ? 'red' : iconColor;
  const queryClient = useQueryClient();

  const me = useSelector(selectMe);
  const [showLoginBS, setShowLoginBS] = useState(false);

  useEffect(() => {
    if (toggledState === type) {
      setState(false);
      if (state) setTextCopy(Number(textCopy) - 1);
    }
  }, [toggledState]);

  async function toggleStateAtServer() {
    if (state) {
      await deleteInteraction({ contentId, interaction: type });
    } else {
      await addInteraction({ contentId, interaction: type });
      if (type === LIKE) {
        await deleteInteraction({ contentId, interaction: DISLIKE });
        setToggledState(DISLIKE);
      } else if (type === DISLIKE) {
        await deleteInteraction({ contentId, interaction: LIKE });
        setToggledState(LIKE);
      }
    }
  }

  async function toggleState() {
    if (authRequired && !me) {
      setShowLoginBS(true);
    } else if (!loading) {
      setLoading(true);

      if (!state) setTextCopy(Number(textCopy) + 1);
      else setTextCopy(Number(textCopy) - 1);
      setState(prev => !prev);
      await toggleStateAtServer();
      queryClient.invalidateQueries({
        queryKey: setPathParam(PATHS.content, 'contentId', contentId),
      });

      setLoading(false);
    }
  }

  const genBottomSheetMsg = () => {
    let msg = '';
    if (type === LIKE) {
      msg = 'برای لایک کردن محتوا، لطفا وارد شوید.';
    }
    if (type === FAVORITE) {
      msg = 'برای نشان کردن محتوا لطفا وارد شوید.';
    }
    return msg;
  };

  const startIcon = state ? (
    <Fade in>
      <SvgIcon
        component={filledIcon}
        sx={{ fill: 'none' }}
        fontSize={iconSize}
      />
    </Fade>
  ) : (
    <SvgIcon component={icon} sx={{ fill: 'none' }} fontSize={iconSize} />
  );

  const navigate = useNavigate();
  const onInteractionsClick = () => {
    let interactionsPath = setPathParam(
      PATHS.contentInteractions,
      'contentId',
      contentId,
    );
    interactionsPath = setPathParam(interactionsPath, 'interactionType', type);
    navigate(interactionsPath);
  };

  return (
    <>
      <Box
        style={{
          display: 'flex',
          flexDirection,
          alignItems: 'center',
          justifyContent: 'center',
          gap: 10,
        }}
      >
        <IconButton
          onClick={toggleState}
          sx={{ color }}
          disabled={loading}
          edge="end"
          style={{ marginLeft: flexDirection === 'column' ? '0' : '-12px' }}
        >
          {startIcon}
        </IconButton>

        {/* <IconButton
          onClick={onInteractionsClick}
          style={{ marginTop: flexDirection === 'column' ? '-10px' : '0' }}
        >
          <Typography color={textColor}>{textCopy}</Typography>
        </IconButton> */}
        <Typography color={textColor}>{textCopy}</Typography>
      </Box>

      <LoginBottomSheet
        show={showLoginBS}
        hideBottomSheet={() => setShowLoginBS(false)}
        message={genBottomSheetMsg()}
      />
    </>
  );
}
