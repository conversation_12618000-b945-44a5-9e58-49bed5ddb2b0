import { useLoaderData, useLocation } from 'react-router-dom';
import { CHAT_TYPES } from 'constants';
import ChatInputBox from 'components/ChatInputBox/ChatInputBox';
import Message from 'components/Message/Message';
import useChatMessages from 'hooks/useChatMessages';
import { useInView } from 'react-intersection-observer';
import { useEffect } from 'react';
import TextProgress from 'components/TextProgress/TextProgress';
import { Stack } from '@mui/material';
import { useQuery } from 'react-query';
import { getChat } from 'apis/messenger';
import useLoadContacts from 'hooks/useLoadContacts';
import { GET_ME_URL, getMe } from 'apis/auth';
import { useDispatch } from 'react-redux';
import { setMe } from 'store/auth';
import Messages from './components/Messages/Messages';
import MyAppBar from './components/MyAppBar/MyAppBar';

export async function loader({ params }) {
  return params.chatId;
}

function isAllowedToSendMessage(chatCreator, chatType, myId) {
  const isChannel = chatType === CHAT_TYPES.CHANNEL;
  const isCreator = chatCreator && myId && chatCreator === myId;
  return !isChannel || isCreator;
}

export default function Chat() {
  const isFromDesktop = useLocation().search.search('fromDesktop') !== -1;

  const chatId = useLoaderData();
  useLoadContacts();

  const { data } = useQuery(
    ['get-chat', chatId],
    () => getChat({ chatId }),
    { initialData: { data: {} } },
  );
  const chat = data.data;

  const chatType = chat.type;
  const chatCreator = chat.creator;

  const dispatch = useDispatch();
  const { data: myData } = useQuery(
    GET_ME_URL,
    getMe,
    { onSuccess: (data) => dispatch(setMe(data.data)) },
  );
  const myId = myData?.data?.id;

  const allowedToSendMessage = isAllowedToSendMessage(
    chatCreator,
    chatType,
    myId,
  ) && !chat.is_user_blocked;

  const isCreator = myId === chat.creator;
  const isDirect = chat.type === CHAT_TYPES.DIRECT;
  const isAdmin = chat.admins?.includes(myId);

  const {
    messages,
    addMessage,
    fetchNextPage,
    hastNextPage,
  } = useChatMessages(chatId);

  const { ref, inView } = useInView();
  useEffect(() => {
    if (inView) {
      fetchNextPage();
    }
  }, [inView]);

  const withAvatar = chatType === CHAT_TYPES.GROUP;

  const reversedMessagesList = Array.from(messages).toReversed();

  return (
    <Stack sx={{ height: '100vh', background: '#fff' }}>
      {myId && (
        <>
          <MyAppBar
            chatId={chatId}
            isCreator={isCreator}
            isDirect={isDirect}
            hasBack={!isFromDesktop}
          />

          <Messages chatId={chatId} myId={myId} withAvatar={withAvatar}>
            {reversedMessagesList.map(([deliveryToken, message]) => (
              <Message
                key={deliveryToken}
                myId={myId}
                withAvatar={withAvatar}
                message={message}
                isAdmin={isAdmin}
              />
            ))}
            {hastNextPage && <TextProgress ref={ref} />}
          </Messages>

          {allowedToSendMessage && <ChatInputBox chatId={chatId} addMessage={addMessage} />}
        </>
      )}
    </Stack>
  );
}
