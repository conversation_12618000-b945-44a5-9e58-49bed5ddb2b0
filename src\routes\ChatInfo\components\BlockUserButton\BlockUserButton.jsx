import { IconButton } from '@mui/material';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import { useState } from 'react';
import BlockUserBottomSheet from '../BlockUserBottomSheet/BlockUserBottomSheet';

export default function BlockUserButton({ chatId, userId }) {
  const [showBS, setShowBS] = useState(false);

  return (
    <>
      <IconButton onClick={() => setShowBS(true)}><LockOutlinedIcon /></IconButton>
      <BlockUserBottomSheet
        show={showBS}
        hideBottomSheet={() => setShowBS(false)}
        chatId={chatId}
        userId={userId}
      />
    </>
  );
}
