import { Typography, IconButton, CircularProgress, Box } from '@mui/material';
import { useLoaderData, useNavigate } from 'react-router-dom';
import { getContentAroundId, getContents } from 'apis/content';
import { useDispatch } from 'react-redux';
import { setAppbarMenu } from 'store/layout';
import { useCallback, useEffect, useState } from 'react';
import { useSwipeable } from 'react-swipeable';
import { ReactComponent as HeartIcon } from 'static/icons/heart.svg';
import { ReactComponent as HeartBoldIcon } from 'static/icons/heart-bold.svg';
import { ReactComponent as FavoriteIcon } from 'static/icons/archive-book.svg';
import { ReactComponent as FavoriteBoldIcon } from 'static/icons/archive-book-bold.svg';
import { KeyboardBackspace, MoreVert } from '@mui/icons-material';
import { FAVORITE, LIKE, CONTENT_STATUS } from 'constants';
import { SEARCH_PARAMS } from 'components/SearchBox/searchConfig';
import InteractionButton from '../Content/components/InteractionButton/InteractionButton';
import ForwardButton from '../Content/components/ForwardButton/ForwardButton';
import MyReactPlayer from '../../components/MyReactPlayer/MyReactPlayer';
import { isAudio, isVideo } from '../../utils';
import MyAvatar from '../../components/MyAvatar/MyAvatar';
import Description from '../Content/components/Description/Description';

export async function loader({ params }) {
  return params.contentId;
}

function getInteraction(interactions, interactionType) {
  return interactions?.find(i => i.type === interactionType);
}

export default function ContentReels() {
  const contentId = useLoaderData();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [posts, setPosts] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Fetch content around the current content ID
  const loadContentData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getContentAroundId(contentId, 5, 5);
      const { results, currentIndex: initialIndex } = response.data;

      setPosts(results);
      setCurrentIndex(initialIndex);
    } catch (err) {
      console.error('Error loading content:', err);
      setError('خطا در بارگذاری محتوا');

      // Fallback: try to get recent content
      try {
        const fallbackResponse = await getContents({
          [SEARCH_PARAMS.PAGE_SIZE]: 11,
          [SEARCH_PARAMS.ORDERING]: '-created_at',
          [SEARCH_PARAMS.STATUS]: CONTENT_STATUS.APPROVED,
        });

        const results = fallbackResponse.data.results;
        const foundIndex = results.findIndex(
          content => content.id === contentId,
        );

        setPosts(results);
        setCurrentIndex(foundIndex >= 0 ? foundIndex : 0);
        setError(null);
      } catch (fallbackErr) {
        console.error('Fallback also failed:', fallbackErr);
        setError('خطا در بارگذاری محتوا');
      }
    } finally {
      setLoading(false);
    }
  }, [contentId]);

  // Load initial data
  useEffect(() => {
    loadContentData();
  }, [loadContentData]);

  // Preload additional content when approaching the ends
  const preloadContent = useCallback(
    async direction => {
      if (posts.length === 0) return;

      try {
        const currentContent = posts[currentIndex];
        if (!currentContent) return;

        if (direction === 'next' && currentIndex >= posts.length - 3) {
          // Preload newer content
          const response = await getContents({
            [SEARCH_PARAMS.PAGE_SIZE]: 5,
            [SEARCH_PARAMS.ORDERING]: 'created_at',
            [SEARCH_PARAMS.STATUS]: CONTENT_STATUS.APPROVED,
            created_at__gt: currentContent.created_at,
          });

          const newContent = response.data.results.reverse();
          if (newContent.length > 0) {
            setPosts(prev => [...prev, ...newContent]);
          }
        } else if (direction === 'prev' && currentIndex <= 2) {
          // Preload older content
          const response = await getContents({
            [SEARCH_PARAMS.PAGE_SIZE]: 5,
            [SEARCH_PARAMS.ORDERING]: '-created_at',
            [SEARCH_PARAMS.STATUS]: CONTENT_STATUS.APPROVED,
            created_at__lt: currentContent.created_at,
          });

          const newContent = response.data.results;
          if (newContent.length > 0) {
            setPosts(prev => [...newContent, ...prev]);
            setCurrentIndex(prev => prev + newContent.length);
          }
        }
      } catch (err) {
        console.error('Error preloading content:', err);
      }
    },
    [posts, currentIndex],
  );

  const handleNext = useCallback(() => {
    if (currentIndex < posts.length - 1 && !isTransitioning) {
      setIsTransitioning(true);
      setCurrentIndex(prev => prev + 1);
      preloadContent('next');

      setTimeout(() => setIsTransitioning(false), 300);
    }
  }, [currentIndex, posts.length, isTransitioning, preloadContent]);

  const handlePrev = useCallback(() => {
    if (currentIndex > 0 && !isTransitioning) {
      setIsTransitioning(true);
      setCurrentIndex(prev => prev - 1);
      preloadContent('prev');

      setTimeout(() => setIsTransitioning(false), 300);
    }
  }, [currentIndex, isTransitioning, preloadContent]);

  // Swipe handlers
  const handlers = useSwipeable({
    onSwipedLeft: handleNext,
    onSwipedRight: handlePrev,
    preventDefaultTouchmoveEvent: true,
    trackMouse: true,
    trackTouch: true,
    delta: 10,
  });

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = event => {
      if (event.key === 'ArrowLeft') {
        handleNext();
      } else if (event.key === 'ArrowRight') {
        handlePrev();
      } else if (event.key === 'Escape') {
        navigate(`/content/${contentId}`);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [handleNext, handlePrev, navigate, contentId]);

  useEffect(() => () => dispatch(setAppbarMenu(null)), [dispatch]);

  // Early return for loading state
  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: '#000',
          width: '100%',
        }}
      >
        <CircularProgress sx={{ color: 'white' }} />
      </Box>
    );
  }

  // Early return for error state
  if (error) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: '#000',
          color: 'white',
          padding: 2,
        }}
      >
        <Typography variant="h6" sx={{ mb: 2 }}>
          {error}
        </Typography>
        <Typography
          variant="body2"
          sx={{ cursor: 'pointer', textDecoration: 'underline' }}
          onClick={() => navigate(`/content/${contentId}`)}
        >
          بازگشت به محتوا
        </Typography>
      </Box>
    );
  }

  // Early return if no posts
  if (!posts.length || !posts[currentIndex]) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: '#000',
          color: 'white',
        }}
      >
        <Typography>محتوایی یافت نشد</Typography>
      </Box>
    );
  }

  const currentPost = posts[currentIndex];
  const isVideoOrAudio =
    isVideo(currentPost.file_type) || isAudio(currentPost.file_type);

  return (
    <Box
      {...handlers}
      sx={{
        position: 'relative',
        height: '100vh',
        width: '100vw',
        backgroundColor: '#000',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {/* Back button */}
      <IconButton
        sx={{
          position: 'absolute',
          top: 16,
          right: 16,
          zIndex: 10,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          color: 'white',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
          },
        }}
        onClick={() => navigate(`/content/${contentId}`)}
      >
        <KeyboardBackspace />
      </IconButton>

      {/* Content indicator */}
      <Box
        sx={{
          position: 'absolute',
          top: 16,
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 10,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          borderRadius: '12px',
          padding: '4px 12px',
          color: 'white',
          fontSize: '12px',
        }}
      >
        {currentIndex + 1} / {posts.length}
      </Box>

      {/* Main content */}
      <Box
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transition: isTransitioning ? 'opacity 0.3s ease-in-out' : 'none',
          opacity: isTransitioning ? 0.7 : 1,
        }}
      >
        {isVideoOrAudio ? (
          <MyReactPlayer
            url={currentPost.file}
            isAudio={isAudio(currentPost.file_type)}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
            }}
          />
        ) : (
          <img
            src={currentPost.file}
            alt={currentPost.title}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
            }}
          />
        )}
      </Box>

      {/* Bottom overlay with user info and description */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 70,
          background: 'linear-gradient(transparent, rgba(0, 0, 0, 0.8))',
          padding: '40px 16px 16px',
          color: 'white',
        }}
      >
        <MyAvatar
          avatar={currentPost.author.avatar}
          fullName={
            currentPost.author.first_name || currentPost.author.last_name
              ? `${currentPost.author.first_name || ''} ${
                  currentPost.author.last_name || ''
                }`.trim()
              : 'بدون نام'
          }
          username={currentPost.author.display_username}
          userId={currentPost.author.id}
          withLink
          sx={{ mb: 1 }}
        />
        <Description dsc={currentPost.description} />
      </Box>

      {/* Right side interaction buttons */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 16,
          right: 16,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <InteractionButton
          text={currentPost.favorites_count}
          icon={FavoriteIcon}
          filledIcon={FavoriteBoldIcon}
          type={FAVORITE}
          interaction={getInteraction(currentPost.interactions, FAVORITE)}
          contentId={currentPost.id}
          authRequired
          iconColor="white"
          textColor="white"
          flexDirection="column"
          iconSize="large"
        />
        <InteractionButton
          text={currentPost.likes_count}
          icon={HeartIcon}
          filledIcon={HeartBoldIcon}
          type={LIKE}
          interaction={getInteraction(currentPost.interactions, LIKE)}
          contentId={currentPost.id}
          authRequired
          iconColor="white"
          textColor="white"
          flexDirection="column"
          iconSize="large"
        />
        <ForwardButton
          contentId={currentPost.id}
          iconColor="white"
          iconSize="large"
        />
        <MoreVert
          fontSize="large"
          sx={{ color: 'white', mt: 1, cursor: 'pointer' }}
        />
      </Box>

      {/* Navigation hints */}
      {currentIndex > 0 && (
        <Box
          sx={{
            position: 'absolute',
            left: 16,
            top: '50%',
            transform: 'translateY(-50%)',
            opacity: 0.5,
            color: 'white',
            fontSize: '12px',
            textAlign: 'center',
          }}
        >
          ←
          <br />
          قبلی
        </Box>
      )}

      {currentIndex < posts.length - 1 && (
        <Box
          sx={{
            position: 'absolute',
            right: 16,
            top: '50%',
            transform: 'translateY(-50%)',
            opacity: 0.5,
            color: 'white',
            fontSize: '12px',
            textAlign: 'center',
          }}
        >
          →
          <br />
          بعدی
        </Box>
      )}
    </Box>
  );
}
