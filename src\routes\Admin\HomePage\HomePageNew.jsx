import { Grid } from '@mui/material';
import { useQuery } from 'react-query';
import { getMe, GET_ME_URL } from 'apis/auth';
import LoadingPage from 'components/LoadingPage/LoadingPage';
import { Box } from '@mui/system';
import {
  CorporateFare,
  Download,
  Favorite,
  Person,
  RssFeed,
  Share,
  Stars,
  Visibility,
} from '@mui/icons-material';
import FilterBox from './components/FilterBox';
import Badge from './components/Badge';
import CustomBarChart from './components/CustomBarChart';
import CustomLineChart from './components/CustomLineChart';
import HashtagCloud from './components/HashtagCloud';
import LatestContents from './components/LatestContents';
import StatisticalInfo from './components/StatisticalInfo';
import ContentCategoryChart from './components/ContentCategoryChart';
import ContentProcessChart from './components/ContentProcessChart';
import ContentAnalyseChart from './components/ContentAnalyseChart';
import ContentCategoryPieChart from './components/ContentCategoryPieChart';

export default function AdminHomePage() {
  const { isLoading, data } = useQuery(GET_ME_URL, () => getMe());

  if (isLoading) {
    return <LoadingPage />;
  }

  return (
    <Grid
      container
      sx={{ p: 2, flexDirection: 'column', gap: 2 }}
      xs={12}
      lg={12}
    >
      <FilterBox />
      <StatisticalInfo />

      <ContentProcessChart />
      <ContentCategoryChart />
      <ContentAnalyseChart />
      <ContentCategoryPieChart />
      {/*<CustomBarChart />*/}
      {/*<CustomLineChart />*/}

      <HashtagCloud />
      <LatestContents />
    </Grid>
  );
}
