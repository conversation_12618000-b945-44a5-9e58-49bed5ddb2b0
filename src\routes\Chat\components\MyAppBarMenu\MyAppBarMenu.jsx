import MoreVertIcon from '@mui/icons-material/MoreVert';
import {
  IconButton, ListItemIcon, ListItemText, Menu, MenuItem,
} from '@mui/material';
import { useState } from 'react';
import ReportGmailerrorredIcon from '@mui/icons-material/ReportGmailerrorred';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import VideoCallOutlinedIcon from '@mui/icons-material/VideoCallOutlined';
import useSendMessage from 'hooks/useSendMessage';
import { createCallMessage } from 'dtos/messenger';
import { useSelector } from 'react-redux';
import { selectMe } from 'store/auth';
import DeleteBottomSheet from '../DeleteBottomSheet/DeleteBottomSheet';
import ReportBottomSheet from '../ReportBottomSheet/ReportBottomSheet';

export default function MyAppBarMenu({ chatId, isCreator, isDirect }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = !!anchorEl;

  function handleClick(event) {
    setAnchorEl(event.currentTarget);
  }

  function handleClose() {
    setAnchorEl(null);
  }

  const [showReportBottomSheet, setShowReportBottomSheet] = useState(false);
  function onReportClick() {
    handleClose();
    setShowReportBottomSheet(true);
  }

  const [showDeleteBottomSheet, setShowDeleteBottomSheet] = useState(false);
  function onDeleteClick() {
    handleClose();
    setShowDeleteBottomSheet(true);
  }

  const me = useSelector(selectMe);
  const sendMessageToServer = useSendMessage();
  const createMeet = () => {
    handleClose();
    sendMessageToServer(createCallMessage(me, chatId));
  };

  return (
    <>
      <IconButton onClick={handleClick}><MoreVertIcon /></IconButton>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
      >
        {!isCreator && (
          <MenuItem onClick={onReportClick}>
            <ListItemIcon><ReportGmailerrorredIcon /></ListItemIcon>
            <ListItemText>گزارش گفت‌وگو</ListItemText>
          </MenuItem>
        )}

        {(isCreator || isDirect) && (
          <MenuItem onClick={onDeleteClick}>
            <ListItemIcon><DeleteOutlineIcon /></ListItemIcon>
            <ListItemText>حذف گفت‌وگو</ListItemText>
          </MenuItem>
        )}

        <MenuItem onClick={createMeet}>
          <ListItemIcon><VideoCallOutlinedIcon /></ListItemIcon>
          <ListItemText>ایجاد تماس</ListItemText>
        </MenuItem>
      </Menu>

      <ReportBottomSheet
        chatId={chatId}
        show={showReportBottomSheet}
        hideBottomSheet={() => setShowReportBottomSheet(false)}
      />

      <DeleteBottomSheet
        chatId={chatId}
        show={showDeleteBottomSheet}
        hideBottomSheet={() => setShowDeleteBottomSheet(false)}
      />
    </>
  );
}
