import { useEffect, useState } from 'react';
import {
  Box, Button, FormControl,
  Grid,
  Stack, styled,
  Table,
  TableBody,
  TableCell,
  TableContainer, TableHead,
  TableRow,
} from '@mui/material';
import { Link } from 'react-router-dom';
import {
  ControlPointDuplicate, Delete, Edit, RemoveRedEye,
} from '@mui/icons-material';
import { useIsDesktop } from '../../../../../utils';
import SearchInput from './components/SearchInput';
import { updateOrganization } from '../../../../../apis/organization';
import BottomSheetMessage from '../../../../../components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from '../../../../../components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton
  from '../../../../../components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import BottomSheet from '../../../../../components/BottomSheet/BottomSheet';
import UserSelect from '../../../OrganizationCreate/components/UserSelect';
import AdminSelect from '../MembersSection/components/AdminSelect';

const CTableHead = styled(TableHead)(() => ({
  '& th': {
    fontSize: '14px',
    color: '#737373',
    border: 'none',
  },
}));
const CTableRow = styled(TableRow)(() => ({
  '& td': {
    border: 0,
    color: '#222222',
    fontSize: '16px',
  },
}));
function CActionCell({
  sx, actions = [], basePath, onDelete, isDesktop, id,
}) {
  const btnStyle = {
    color: '#222',
    background: '#ECEDF7',
    width: isDesktop ? '35px' : 'calc(33% - 10px)',
    minWidth: '35px',
    height: '35px',
    margin: '0 5px',
    boxShadow: 'none',
    '&:hover': {
      background: '#E7EAF4',
    },
  };

  const actionButtons = [
    { action: 'view', icon: <RemoveRedEye sx={{ fontSize: '14px' }} />, link: (!!basePath && basePath !== '#' ? `/${basePath}/view/${id}` : '#') },
    // { action: 'view', icon: <RemoveRedEye sx={{ fontSize: '14px' }} />, link: '#' },
    { action: 'edit', icon: <Edit sx={{ fontSize: '14px' }} />, link: (basePath ? `/${basePath}/${id}/update/` : '#') },
    // { action: 'edit', icon: <Edit sx={{ fontSize: '14px' }} />, link: '#' },
    { action: 'delete', icon: <Delete sx={{ fontSize: '14px' }} />, onClick: () => onDelete(id) },
  ];

  return (
    <TableCell sx={sx} colSpan={isDesktop ? 1 : 2}>
      {actionButtons.map(
        ({
          action, icon, link, onClick,
        }) =>
          actions.includes(action) && (
            <Link key={action} to={link} style={{ textDecoration: 'none' }}>
              <Button variant="contained" size="small" sx={btnStyle} onClick={onClick}>
                {icon}
              </Button>
            </Link>
          ),
      )}
    </TableCell>
  );
}

function AdminsSection({ organization = {} }) {
  const isDesktop = useIsDesktop();

  const [admins, setAdmins] = useState(organization.admins);

  const [searchText, setSearchText] = useState('');
  const [filteredData, setFilteredData] = useState(organization.admins || []);
  const [showDeleteBottomSheet, setShowDeleteBottomSheet] = useState(false);
  const [showAdminAddForm, setShowAdminAddForm] = useState(false);
  const [idToDelete, setIdToDelete] = useState(null);

  const [selectedMembers, setSelectedMembers] = useState([]);

  useEffect(() => {
    const filtered = admins.filter(item =>
      item?.display_username?.toLowerCase().includes(searchText?.toLowerCase())
      || item?.last_name?.toLowerCase().includes(searchText?.toLowerCase())
      || item?.first_name?.toLowerCase().includes(searchText?.toLowerCase()),
    );
    setFilteredData(filtered);
  }, [searchText, admins]);
  const handleInputChange = (e) => {
    setSearchText(e.target.value);
  };
  const handleDelete = (id) => {
    setShowDeleteBottomSheet(true);
    setIdToDelete(id);
  };
  const handleAddAdmin = async () => {
    const updatedAdminIds = admins.map(admin => admin.id);
    const combinedAdminIds = [...updatedAdminIds, ...selectedMembers];
    await updateOrganization(organization.id, {
      admins: Array.from(new Set(combinedAdminIds)),
    });
    setSelectedMembers([]);
    setShowAdminAddForm(false);
    window.location.reload();
  };

  const colTitles = ['عکس', 'نام مدیر', 'اقدامات'];

  return (
    <Grid sx={{
      py: 2,
      px: 3,
      mb: 3,
      borderRadius: '8px',
      background: 'white',
      boxShadow: '0px 2px 20px 0px #00000012',
      width: '100%',
    }}
    >
      <Box sx={{
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
        gap: '10px',
        paddingBottom: '10px',
        borderBottom: '1px solid #ccc',
      }}
      >
        <SearchInput label="جستجو" onChange={handleInputChange} sx={{ maxWidth: '400px', zIndex: '10' }} />
        <Button
          variant="contained"
          size="large"
          type="submit"
          sx={{ width: '180px' }}
          onClick={() => { setShowAdminAddForm(!showAdminAddForm); }}
        >
          <ControlPointDuplicate sx={{ marginRight: '6px' }} />
          افزودن مدیر
        </Button>

      </Box>
      {showAdminAddForm && (
      <Box sx={{ paddingBottom: '30px', borderBottom: '1px solid #ccc' }}>
        <FormControl fullWidth sx={{ mt: 2 }}>
          <AdminSelect
            selectedMembers={selectedMembers}
            setSelectedMembers={setSelectedMembers}
            list={organization.members}
          />
        </FormControl>
        <Button
          variant="contained"
          size="large"
          type="submit"
          sx={{ mt: 2, width: '100%' }}
          onClick={handleAddAdmin}
        >
          ذخیره
        </Button>
      </Box>
      )}
      <TableContainer>
        <Table>
          <CTableHead>
            <TableRow>
              {colTitles.map((title) => (
                <TableCell>{title}</TableCell>
              ))}
            </TableRow>
          </CTableHead>
          <TableBody>
            {filteredData.map((row) => (
              <CTableRow key={row.id}>
                <TableCell sx={{ maxWidth: '40px' }}><img width="40px" height="40px" alt="avatar" src={row.avatar || '/logo.png'} style={{ borderRadius: '100px' }} /></TableCell>
                <TableCell width="100%">
                  <span style={{ fontSize: '16px', fontWeight: 'bold' }}>{`${row.first_name} ${row.last_name}`}</span>
                  <br />
                  <span style={{ fontSize: '14px' }}>
                    @
                    {row.display_username}
                  </span>
                </TableCell>

                <CActionCell
                  actions={['delete']}
                  basePath="/"
                  onDelete={handleDelete}
                  id={row.id}
                />
              </CTableRow>
            ))}
          </TableBody>
        </Table>

      </TableContainer>
      <BottomSheet
        title="حذف مدیر"
        hideBottomSheet={() => setShowDeleteBottomSheet(false)}
        show={showDeleteBottomSheet}
      >
        <BottomSheetMessage>
          آیا می‌خواهید این مدیر را حذف کنید؟
        </BottomSheetMessage>

        <Stack direction="row">
          <BottomSheetPrimaryButton
            onClick={async () => {
              if (idToDelete) {
                await updateOrganization(organization.id, {
                  admins: admins.map(admin => admin.id).filter(id => id !== idToDelete),
                });
                setAdmins(admins.filter(admin => admin.id !== idToDelete));
                window.location.reload();
              }
              setShowDeleteBottomSheet(false);
            }}
          >
            بله
          </BottomSheetPrimaryButton>
          <BottomSheetSecondaryButton onClick={() => setShowDeleteBottomSheet(false)}>
            خیر
          </BottomSheetSecondaryButton>
        </Stack>
      </BottomSheet>
    </Grid>
  );
}

export default AdminsSection;
