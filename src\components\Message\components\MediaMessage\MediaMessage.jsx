import {
  Box, Stack,
} from '@mui/material';
import FullScreenImage from 'components/FullScreenImage/FullScreenImage';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { playAudio, stopAudio, selectCurrentlyPlaying } from 'store/chatPlayer';
import { MESSAGE_TYPES } from 'constants';
import MyReactPlayer from 'components/MyReactPlayer/MyReactPlayer';
import { selectMessageUploadProgress } from 'store/messenger';
import CircularProgressWithValue from 'components/CircularProgressWithValue/CircularProgressWithValue';
import MessageContainer from '../MessageContainer/MessageContainer';
import MessageText from '../MessageText/MessageText';
import Image from '../Image/Image';

function CenteredCircularProgress({ value }) {
  return (
    <Box sx={{
      position: 'absolute',
      top: 'calc(50% - 12px)',
      left: 'calc(50% - 12px)',

    }}
    >
      <CircularProgressWithValue value={value} />
    </Box>
  );
}

export default function MediaMessage({
  myId, message, withAvatar, isAdmin,
}) {
  const dispatch = useDispatch();
  const currentlyPlaying = useSelector(selectCurrentlyPlaying);
  const {
    file,
    text,
    type,
    deliveryToken,
  } = message;
  const [playing, setPlaying] = useState(false);
  const [showFullScreen, setShowFullScreen] = useState(false);

  const handlePlay = () => {
    if (!playing) {
      dispatch(playAudio(deliveryToken));
    } else {
      dispatch(stopAudio());
    }
    setPlaying(!playing);
  };

  useEffect(() => {
    if (deliveryToken !== currentlyPlaying) {
      setPlaying(false);
    }
  }, [currentlyPlaying]);

  const uploadProgress = useSelector(selectMessageUploadProgress(deliveryToken));

  return (
    <MessageContainer message={message} myId={myId} withAvatar={withAvatar} isAdmin={isAdmin}>
      <Stack spacing={1} alignItems="center">
        <Box sx={{ position: 'relative', maxWidth: '100%' }}>
          {type === MESSAGE_TYPES.IMAGE && (
            <>
              <Image src={file} onClick={() => setShowFullScreen(true)} />
              <FullScreenImage
                img={file}
                show={showFullScreen}
                unShow={() => setShowFullScreen(false)}
              />
            </>
          )}
          {type === MESSAGE_TYPES.VIDEO && (
            <MyReactPlayer
              url={file}
              playing={playing}
              onPlay={handlePlay}
              onPause={() => setPlaying(false)}
            />
          )}

          {uploadProgress && uploadProgress < 100
            && (
              <CenteredCircularProgress value={uploadProgress} variant="determinate" size={24} />
            )}
        </Box>

        <MessageText sx={{ width: '100%' }}>{text}</MessageText>
      </Stack>
    </MessageContainer>
  );
}
