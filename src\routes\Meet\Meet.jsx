import { JitsiMeeting } from '@jitsi/react-sdk';
import { useSelector } from 'react-redux';
import { useLoaderData, useNavigate } from 'react-router-dom';
import { selectMe } from 'store/auth';

export async function loader({ params }) {
  return { chatId: params.chatId, meetId: params.meetId };
}

export default function Meet() {
  const { meetId } = useLoaderData();
  const me = useSelector(selectMe);
  const navigate = useNavigate();

  return (
    <JitsiMeeting
      domain="meet.haghighatgram.ir"
      roomName={meetId}
      configOverwrite={{
        startWithAudioMuted: true,
        disableModeratorIndicator: true,
        startScreenSharing: true,
        enableEmailInStats: false,
        disableDeepLinking: true,
        defaultLogoUrl: '/logo.png',
      }}
      interfaceConfigOverwrite={{
        DISABLE_JOIN_LEAVE_NOTIFICATIONS: true,
      }}
      userInfo={{
        displayName: me.display_username,
      }}
      // eslint-disable-next-line no-param-reassign
      getIFrameRef={(iframeRef) => { iframeRef.style.height = '100vh'; iframeRef.style.width = '100vw'; }}
      onReadyToClose={() => navigate(-1)}
      lang="fa"
    />
  );
}
