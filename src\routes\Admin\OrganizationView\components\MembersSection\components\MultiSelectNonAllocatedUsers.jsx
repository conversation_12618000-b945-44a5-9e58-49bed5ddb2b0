import { useEffect, useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import { Cancel, CheckBox, CheckBoxOutlineBlank } from '@mui/icons-material';
import {
  Autocomplete,
  Avatar,
  Button,
  Checkbox,
  Chip,
  FormControl,
  Grid,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Modal,
  Paper,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import debounce from 'lodash.debounce';
import { Box } from '@mui/system';
import { inviteUser, listUsers } from '../../../../../../apis/auth';

function MultiSelectNonAllocatedUsers({
  selectedMembers = [],
  setSelectedMembers,
}) {
  const [selectedOptions, setSelectedOptions] = useState(selectedMembers);
  const [inputValue, setInputValue] = useState('');
  const [phoneInput, setPhoneInput] = useState('');
  const [options, setOptions] = useState([]);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteMemberError, setInviteMemberError] = useState('');

  const handleOpen = () => {
    setShowInviteModal(true);
  };
  const handleClose = () => {
    setShowInviteModal(false);
  };

  // Function to transform data into the required format
  const transformData = data =>
    data.map(item => ({
      id: item.id,
      label: item.first_name
        ? `${item.first_name} ${item.last_name}`
        : item.display_username
        ? `${item.display_username}`
        : `${item.username}`,
      avatar: item.avatar || '',
      is_member_of_an_organization: item.is_member_of_an_organization || '',
    }));

  // Fetch users based on input value using react-query
  const { data, isSuccess, refetch } = useQuery(
    ['listUsers', inputValue],
    () => {
      const sanitizedInput =
        inputValue?.startsWith('0') || inputValue?.startsWith('۰')
          ? inputValue.slice(1)
          : inputValue;

      return listUsers(sanitizedInput);
    },
    {
      enabled: false, // Do not fetch automatically
    },
  );

  // Debounced input handler
  const debouncedFetch = useMemo(
    () =>
      debounce(value => {
        if (value.length >= 3) {
          refetch(); // Trigger the query manually when input is debounced
        }
      }, 400),
    [],
  );

  // Update options when data is fetched successfully
  useEffect(() => {
    if (isSuccess && data?.status === 200) {
      setOptions(transformData(data?.data?.results || []));
    }
  }, [data, isSuccess]);

  // Handle input change with debounced fetch
  useEffect(() => {
    if (inputValue === '') {
      setOptions([]);
    } else {
      debouncedFetch(inputValue);
    }
  }, [inputValue, debouncedFetch]);

  const handleChange = (event, newValue) => {
    // Handle "Select All" logic
    if (newValue.some(option => option.id === 'select-all')) {
      const allSelected = selectedOptions.length === options.length;
      if (allSelected) {
        setSelectedOptions([]);
        setSelectedMembers([]);
      } else {
        setSelectedOptions(options);
        setSelectedMembers(options);
      }
    } else {
      setSelectedOptions(newValue);
      setSelectedMembers(newValue);
    }
  };

  const renderTags = (value, getTagProps) => {
    const displayedTags = value.slice(0, 5);
    return [
      ...displayedTags
        .reverse()
        .map((option, index) => (
          <Chip
            key={option.label}
            avatar={<Avatar src={option.avatar} />}
            label={option.label}
            {...getTagProps({ index })}
            deleteIcon={<Cancel />}
          />
        )),
      value.length > 5 && <Chip key="more" label={`+${value.length - 5}`} />,
    ];
  };

  function CustomPaper(props) {
    return (
      <Paper {...props}>
        {options.length === 0 ? (
          inputValue !== '' ? (
            <Box sx={{ padding: 1, textAlign: 'center' }}>
              <Typography sx={{ padding: '1em', textAlign: 'center' }}>
                کاربر مد نظر شما در تام عضو نیست!
              </Typography>
              <Button
                variant="contained"
                onMouseDown={e => e.preventDefault()}
                onClick={handleOpen}
              >
                دعوت کاربر به سازمان
              </Button>
            </Box>
          ) : (
            <Typography
              component="div"
              sx={{ padding: '1em', textAlign: 'center', color: '#ccc' }}
            >
              نام/ شماره/نام کاربری شخص مورد نظر را وارد کنید
            </Typography>
          )
        ) : (
          props.children // If there are options, render the default list items
        )}
      </Paper>
    );
  }

  const sendInviteLink = async () => {
    try {
      if (phoneInput === '') {
        return setInviteMemberError('شماره شخص مورد نظر را وارد کنید.');
      }
      const response = await inviteUser({
        phone_number: phoneInput,
      });
      if (response.status === 201 || response.status === 200) {
        const id = response?.data?.id;
        if (id) {
          handleChange(null, [
            ...selectedMembers,
            {
              id,
              label: response?.data?.username,
              avatar: '',
            },
          ]);
          setPhoneInput('');
          handleClose();
        }
      }
    } catch (e) {
      console.error(e);
      setInviteMemberError(
        e?.response?.data?.phone_number?.[0] || 'خطا در افزودن کابر!',
      );
    }
  };

  return (
    <>
      <Autocomplete
        multiple
        options={
          options.length > 0
            ? [{ id: 'select-all', label: 'انتخاب همه' }, ...options]
            : []
        }
        disableCloseOnSelect
        getOptionDisabled={option =>
          option.is_member_of_an_organization === true
        }
        getOptionLabel={option => (option?.label ? option?.label : option)}
        filterOptions={x => x}
        autoComplete
        includeInputInList
        // filterSelectedOptions
        PaperComponent={CustomPaper}
        value={selectedOptions}
        onChange={handleChange}
        onInputChange={(event, newInputValue) => {
          setInputValue(newInputValue);
        }}
        renderTags={renderTags}
        noOptionsText="موردی یافتن نشد"
        isOptionEqualToValue={(option, value) => option.id === value.id}
        renderOption={(props, option, { selected }) => (
          <li {...props}>
            <Checkbox
              icon={<CheckBoxOutlineBlank />}
              checkedIcon={<CheckBox />}
              style={{ marginRight: 8 }}
              checked={selected}
            />
            {option.label && (
              <ListItem>
                {option.avatar && (
                  <ListItemAvatar>
                    <Avatar src={option.avatar} />
                  </ListItemAvatar>
                )}
                <ListItemText
                  primary={
                    option.is_member_of_an_organization
                      ? `${option.label} (عضو سازمان دیگری است) `
                      : option.label
                  }
                />
              </ListItem>
            )}
          </li>
        )}
        renderInput={params => (
          <TextField
            {...params}
            variant="outlined"
            placeholder="انتخاب کاربر/کاربران"
          />
        )}
      />
      <Modal
        open={showInviteModal}
        onClose={handleClose}
        aria-labelledby="child-modal-title"
        aria-describedby="child-modal-description"
      >
        <Grid
          container
          sx={{
            position: 'absolute',
            top: '30%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            pt: 2,
            px: 4,
            pb: 3,
            borderRadius: '8px',
            background: 'white',
            boxShadow: '0px 2px 20px 0px #00000012',
            width: '50%',
          }}
        >
          <Stack width="100%">
            <Typography
              align="center"
              fontWeight="bold"
              fontSize={18}
              sx={{ pb: 2, borderBottom: '1px solid #ccc' }}
            >
              دعوت به سازمان
            </Typography>
            <Typography fontSize={16} sx={{ mt: 2 }}>
              اگر کاربر مورد نظر از اعضای تام نیست شماره شخص مورد نظر را جهت
              دعوت به سازمان وارد کنید:
            </Typography>
            <FormControl fullWidth sx={{ mt: 2, mb: '55px' }}>
              <TextField
                variant="outlined"
                type="number"
                label="شماره موبایل"
                name="name"
                helperText={inviteMemberError}
                error={!!inviteMemberError}
                inputProps={{ maxLength: 100 }}
                fullWidth
                value={phoneInput}
                onChange={e => setPhoneInput(e.target.value)}
              />
            </FormControl>
            <Box
              sx={{
                position: 'absolute',
                bottom: '0',
                left: '0',
                width: '100%',
                px: 3,
                py: 2,
                display: 'flex',
                flexDirection: 'row',
                gap: '10px',
              }}
            >
              <Button
                variant="contained"
                size="large"
                type="submit"
                sx={{ mt: 2, width: '100%' }}
                onClick={sendInviteLink}
              >
                ارسال لینک دعوت
              </Button>
              <Button
                variant="outlined"
                size="large"
                type="button"
                sx={{ mt: 2, width: '50%' }}
                onClick={handleClose}
              >
                انصراف
              </Button>
            </Box>
          </Stack>
        </Grid>
      </Modal>
    </>
  );
}

export default MultiSelectNonAllocatedUsers;
