import {
  IconButton,
  InputBase, Stack, styled,
} from '@mui/material';
import { useRef } from 'react';
import { createMediaMessage } from 'dtos/messenger';
import { useDispatch, useSelector } from 'react-redux';
import {
  progressMessageUpload,
  selectSelectedMessage, unsetSelectedMessage,
} from 'store/messenger';
import uploadFile from 'apis/storage';
import CloseIcon from '@mui/icons-material/Close';
import MyReactPlayer from 'components/MyReactPlayer/MyReactPlayer';
import { isImage, isVideo } from 'utils';
import useSendMessage from 'hooks/useSendMessage';
import SendMessageButton from '../SendMessageButton/SendMessageButton';

const Image = styled('img')({
  maxWidth: '100%',
  maxHeight: '80%',
  objectFit: 'contain',
});

function CloseButton({ closeModal }) {
  return (
    <IconButton
      sx={{
        position: 'absolute',
        top: '16px',
        right: '16px',
        zIndex: 100,
      }}
      onClick={closeModal}
    >
      <CloseIcon sx={{ color: 'white' }} />
    </IconButton>
  );
}

export default function MediaCaptionModal({
  media, closeModal, me, chatId, addMessage,
}) {
  const sendMessageToServer = useSendMessage();
  const inputRef = useRef(null);

  const selectedMessage = useSelector(selectSelectedMessage(chatId));

  const dispatch = useDispatch();
  const onSendMessageClick = async () => {
    const message = createMediaMessage(
      me,
      chatId,
      media,
      inputRef.current.value,
      selectedMessage?.message,
    );

    addMessage(message);
    closeModal();

    const uploadedFile = await uploadFile(media, (progress) => {
      dispatch(progressMessageUpload({
        deliveryToken: message.deliveryToken,
        uploadProgress: progress,
      }));
    });

    sendMessageToServer(message, uploadedFile.data.id);
    dispatch(unsetSelectedMessage({ chatId }));
  };

  return (
    <Stack sx={{
      position: 'fixed',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      height: '100vh',
      backgroundColor: '#000',
      zIndex: 100000,
      justifyContent: 'center',
      alignItems: 'center',
    }}
    >
      <CloseButton closeModal={closeModal} />

      <Stack sx={{ height: '100vh', width: '100vw' }}>
        <Stack justifyContent="center" flexGrow={1} sx={{ minHeight: 0 }}>
          {isImage(media.type) && <Image src={URL.createObjectURL(media)} />}
          {isVideo(media.type) && <MyReactPlayer url={URL.createObjectURL(media)} />}
        </Stack>

        <Stack
          sx={{
            background: 'white',
            left: 0,
            right: 0,
            bottom: 0,
            p: 1,
            pl: 2,
          }}
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <SendMessageButton onSendMessageClick={onSendMessageClick} />

          <InputBase
            placeholder="پیام خود را وارد کنید"
            sx={{ flexGrow: 1, pr: 1 }}
            multiline
            maxRows={2}
            inputRef={inputRef}
          />
        </Stack>
      </Stack>
    </Stack>
  );
}
