import { SvgIcon } from '@mui/material';
import { ReactComponent as EyeIcon } from 'static/icons/eye.svg';
import { ReactComponent as HeartIcon } from 'static/icons/heart.svg';
import { ReactComponent as VideoCircleIcon } from 'static/icons/video-circle.svg';
import { ReactComponent as VideoVerticalIcon } from 'static/icons/video-vertical.svg';
import { ReactComponent as VideoIcon } from 'static/icons/video.svg';
import { ReactComponent as VolumeHighIcon } from 'static/icons/volume-high.svg';
import { ReactComponent as EditIcon } from 'static/icons/edit.svg';
import { ReactComponent as TickCircleIcon } from 'static/icons/tick-circle.svg';
import { ReactComponent as ShieldTickIcon } from 'static/icons/shield-tick.svg';
import { ReactComponent as Home2Icon } from 'static/icons/home-2.svg';
import { ReactComponent as Messages3Icon } from 'static/icons/messages-3.svg';
import { ReactComponent as SearchNormalIcon } from 'static/icons/search-normal.svg';
import { ReactComponent as Setting2Icon } from 'static/icons/setting-2.svg';
import { ReactComponent as UserSquareIcon } from 'static/icons/user-square.svg';
import { ReactComponent as MicrophoneIcon } from 'static/icons/microphone.svg';
import { ReactComponent as DocumentNormalIcon } from 'static/icons/document-normal.svg';
import { ReactComponent as TrashIcon } from 'static/icons/trash.svg';
import { ReactComponent as PlayCircleIcon } from 'static/icons/play-cricle.svg';
import { ReactComponent as DocumentDownloadIcon } from 'static/icons/document-download.svg'; import { ReactComponent as ChartIcon } from 'static/icons/chart.svg';
import { ReactComponent as HierarchyIcon } from 'static/icons/hierarchy-3.svg';
import { ReactComponent as LogoutIcon } from 'static/icons/logout.svg';
import { ReactComponent as TickIcon } from 'static/icons/tick.svg';
import { ReactComponent as UserCircleAddIcon } from 'static/icons/user-cirlce-add.svg';
import { ReactComponent as Send2Icon } from 'static/icons/send-2.svg';
import { ReactComponent as Send1Icon } from 'static/icons/send-1.svg';
import { ReactComponent as Profile2User } from 'static/icons/bold/profile-2user.svg';
import { ReactComponent as TickSquare } from 'static/icons/tick-square.svg';
import { ReactComponent as CameraBold } from 'static/icons/bold/camera.svg';
import { ReactComponent as DirectBold } from 'static/icons/bold/direct.svg';
import { ReactComponent as Key } from 'static/icons/key.svg';
import { ReactComponent as ShieldSecurity } from 'static/icons/shield-security.svg';
import { ReactComponent as Link2 } from 'static/icons/link-2.svg';
import { ReactComponent as DirectInbox } from 'static/icons/direct-inbox.svg';
import { ReactComponent as ArrowDown2 } from 'static/icons/arrow-down-2.svg';
import { ReactComponent as PlayIcon } from 'static/icons/play.svg';
import { ReactComponent as PauseIcon } from 'static/icons/pause.svg';
import { ReactComponent as FavoriteIcon } from 'static/icons/star-1.svg';
import { ReactComponent as DirectLeft } from 'static/icons/direct-left.svg';
import { ReactComponent as CopyFile } from 'static/icons/copy-file.svg';
import { ReactComponent as Camera } from 'static/icons/camera.svg';
import { ReactComponent as Bell } from 'static/icons/Bell.svg';

function getIcon(name) {
  switch (name) {
    case 'video-circle':
      return VideoCircleIcon;
    case 'video-vertical': return VideoVerticalIcon;
    case 'video':
      return VideoIcon;
    case 'volume-high':
      return VolumeHighIcon;
    case 'edit':
      return EditIcon;
    case 'tick-circle':
      return TickCircleIcon;
    case 'shield-tick':
      return ShieldTickIcon;
    case 'home-2':
      return Home2Icon;
    case 'messages-3':
      return Messages3Icon;
    case 'search-normal':
      return SearchNormalIcon;
    case 'setting-2':
      return Setting2Icon;
    case 'user-square':
      return UserSquareIcon;
    case 'microphone':
      return MicrophoneIcon;
    case 'document-normal':
      return DocumentNormalIcon;
    case 'trash':
      return TrashIcon;
    case 'play-circle':
      return PlayCircleIcon;
    case 'eye':
      return EyeIcon;
    case 'heart':
      return HeartIcon;
    case 'document-download':
      return DocumentDownloadIcon;
    case 'chart':
      return ChartIcon;
    case 'hierarchy-3':
      return HierarchyIcon;
    case 'logout':
      return LogoutIcon;
    case 'tick':
      return TickIcon;
    case 'user-circle-add':
      return UserCircleAddIcon;

    case 'send-2':
      return Send2Icon;

    case 'send-1':
      return Send1Icon;

    case 'profile-2user':
      return Profile2User;

    case 'tick-square':
      return TickSquare;

    case 'bold/camera':
      return CameraBold;

    case 'bold/direct':
      return DirectBold;

    case 'direct-inbox':
      return DirectInbox;

    case 'key':
      return Key;

    case 'shield-security':
      return ShieldSecurity;

    case 'link-2':
      return Link2;

    case 'arrow-down-2':
      return ArrowDown2;

    case 'play':
      return PlayIcon;

    case 'pause':
      return PauseIcon;

    case 'favorite':
      return FavoriteIcon;

    case 'direct-left':
      return DirectLeft;

    case 'copy-file':
      return CopyFile;

    case 'camera':
      return Camera;

    case 'bell':
      return Bell;

    default:
      return '';
  }
}

export default function IconSax({
  name, sx, onClick, ...props
}) {
  return (
    <SvgIcon
      component={getIcon(name)}
      sx={{ fill: 'none', ...sx }}
      onClick={onClick}
      {...props}
    />
  );
}
