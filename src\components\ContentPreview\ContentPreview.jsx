import { Box, styled } from '@mui/material';
import { CONTENT_ASPECT_RATIO, CONTENT_STATUS } from 'constants';
import { isAudio, isVideo } from 'utils';
import IconSax from 'components/IconSax/IconSax';
import StatusBadge from 'components/ContentPreview/components/StatusBadge/StatusBadge';
import ImageIcon from '@mui/icons-material/Image';
import KeyboardVoiceIcon from '@mui/icons-material/KeyboardVoice';
import YouTubeIcon from '@mui/icons-material/YouTube';

const BORDER_RADUIS = '8px';

const MyImg = styled('img')(() => ({
  width: '100%',
  height: '100%',
}));

function PlayIcon() {
  return (
    <IconSax
      name="play-circle"
      sx={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        marginLeft: '-32px',
        marginTop: '-32px',
        width: '64px',
        height: '64px',
      }}
    />
  );
}

function FileTypeIcon({ fileType }) {
  const iconStyle = {
    position: 'absolute',
    top: '4px',
    left: '4px',
    color: 'white',
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: '80px',
    p: '4px',
    width: '30px',
    height: '30px',
  };

  if (isAudio(fileType)) {
    return <KeyboardVoiceIcon sx={iconStyle} />;
  }
  if (isVideo(fileType)) {
    return <YouTubeIcon sx={iconStyle} />;
  }
  return <ImageIcon sx={iconStyle} />;
}

/**
 * shows the given preview and if type is viedo or audio show a play button
 */
export default function ContentPreview({
  fileType,
  preview,
  status,
  withTypeIcon,
  withPlayIcon,
  rounded,
  onClick,
  disable = false,
}) {
  const borderRadius = rounded ? BORDER_RADUIS : 0;

  const showStatus =
    status &&
    (status === CONTENT_STATUS.WAITING || status === CONTENT_STATUS.REJECTED);

  return (
    <Box
      sx={{
        position: 'relative',

        width: '100%',
        aspectRatio: `${CONTENT_ASPECT_RATIO}`,
        filter: disable ? 'grayscale(1)' : 'none',
        borderRadius,
        border: '1px solid #D1D1D6',

        display: 'flex',
        justifyContent: 'center',
      }}
      onClick={onClick}
      alignItems="center"
    >
      <MyImg src={preview} sx={{ borderRadius }} />

      {withPlayIcon && <PlayIcon />}

      {withTypeIcon && <FileTypeIcon fileType={fileType} />}

      {showStatus && <StatusBadge status={status} />}
    </Box>
  );
}
