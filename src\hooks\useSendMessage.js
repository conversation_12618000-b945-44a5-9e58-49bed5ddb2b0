import useAuthenticatedWebsocket from './useAuthenticatedWebSocket';

export default function useSendMessage() {
  const { sendJsonMessage } = useAuthenticatedWebsocket();

  return (message, uploadedFileId) => {
    sendJsonMessage({
      action: 'send_message',
      body: {
        text: message.text,
        type: message.type,
        chat: message.chatId,
        file: uploadedFileId,
        delivery_token: message.deliveryToken,
        reply_to: message.replyTo?.deliveryToken,
      },
    });
  };
}
