import { Grid, styled } from '@mui/material';
import { useQuery } from 'react-query';
import { getStickyNotification } from 'apis/notification';
import MyLink from 'components/MyLink/MyLink';

const RoundedImg = styled('img')({ borderRadius: '8px', width: '100%' });
export default function StickyNotification({ ...props }) {
  const { data, isLoading } = useQuery(
    'sticky-notification',
    getStickyNotification,
  );
  const images = isLoading ? [] : data.data;

  if (images.length > 0) {
    return (
      <>
        {images.map((img) => (
          <Grid item xs={12} lg={4}>
            <MyLink to={img.url} key={img} sx={{ cursor: img.url ? 'pointer' : 'auto' }}>
              <RoundedImg src={img.banner} {...props} key={img} />
            </MyLink>
          </Grid>
        ))}
      </>
    );
  }
}
