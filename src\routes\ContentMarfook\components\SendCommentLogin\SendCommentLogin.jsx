import { Button, Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { PATHS } from 'constants';
import SendCommentContainer from '../SendCommentContainer/SendCommentContainer';

export default function SendCommentLogin() {
  const navigate = useNavigate();
  const redirectToLogin = () => {
    navigate(PATHS.enter_mobile);
  };

  return (
    <SendCommentContainer>
      <Typography color="secondary.dark">لطفا برای ارسال نظر وارد شوید.</Typography>
      <Button
        variant="contained"
        fullWidth
        sx={{ mt: 3 }}
        onClick={redirectToLogin}
      >
        ورود
      </Button>
    </SendCommentContainer>
  );
}
