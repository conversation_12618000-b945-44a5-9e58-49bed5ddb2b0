import {
  Typography, styled, Grid, Stack, Avatar, Box,
} from '@mui/material';
import MyLink from 'components/MyLink/MyLink';
import { buildFullName } from 'utils';

const Name = styled(Typography)(() => ({
  display: 'flex',
  alignItems: 'center',
  fontSize: '15px',
  fontWeight: 'bold',
  color: '#2C2C2E',
  flexGrow: 1,
}));

const LastMessage = styled(Typography)(() => ({
  fontSize: '12px',
  fontWeight: 400,
  color: '#64676A',
}));

export default function ListUser({ user, link }) {
  return (
    <Grid container alignItems="stretch" sx={{ borderBottom: '1px solid rgba(232, 232, 232, 1)', padding: '7px 0' }}>
      <MyLink to={link}>
        <Box>
          <Stack direction="row" spacing={2}>
            <Avatar src={user.avatar} />
            <Stack
              direction="column"
              sx={{ minWidth: 0, flexGrow: 1 }}
              spacing={1}
            >
              <Stack direction="row" alignItems="center" sx={{ flexGrow: 1 }}>
                <Name>
                  <span>{buildFullName(user.first_name, user.last_name)}</span>
                </Name>
              </Stack>

              <LastMessage noWrap>
                {user.display_username ? `@${user.display_username}` : ''}
              </LastMessage>
            </Stack>
          </Stack>
        </Box>
      </MyLink>
    </Grid>
  );
}
