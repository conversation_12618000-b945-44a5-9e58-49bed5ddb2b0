import { Button } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

export default function DropBottom({ label, onClick, expanded }) {
  return (
    <Button
      variant="text"
      endIcon={<ExpandMoreIcon sx={{ rotate: expanded ? '180deg' : '', color: expanded ? '#11a6a1' : '#2C2C2E' }} />}
      sx={{
        borderRadius: 0,
        borderBottom: expanded ? '3px solid #11a6a1' : 'none',
        color: expanded ? '#11a6a1' : '#2C2C2E',
        whiteSpace: 'nowrap',
      }}
      onClick={onClick}
    >
      {label}
    </Button>
  );
}
