import {
  Ava<PERSON>, Badge, Grid, Stack, Typography, styled,
} from '@mui/material';
import { MESSAGE_TYPES, TRANSLATED_MESSAGE_TYPES } from 'constants';
import { useSelector } from 'react-redux';
import {
  selectChat, selectChatDisplayAvatar,
} from 'store/messenger';
import CampaignIcon from '@mui/icons-material/Campaign';
import GroupsIcon from '@mui/icons-material/Groups';

const Name = styled(Typography)(() => ({
  display: 'flex',
  alignItems: 'center',
  fontSize: '15px',
  fontWeight: 'bold',
  color: '#2C2C2E',
  flexGrow: 1,
}));

const LastMessageTime = styled(Typography)(() => ({
  fontSize: '14px',
  fontWeight: 500,
  color: '#64676A',
}));

const LastMessage = styled(Typography)(() => ({
  fontSize: '12px',
  fontWeight: 400,
  color: '#64676A',
}));

function getMessageText(messageText, messageType) {
  if (messageType === MESSAGE_TYPES.GROUP_CREATE) return 'گفت‌وگو ایجاد شد';
  if (messageType === MESSAGE_TYPES.CONTENT) return 'محتوای باز ارسال شده';
  if (messageText) return messageText;

  return TRANSLATED_MESSAGE_TYPES[messageType];
}

function getLastMessageHour(lastMessageCreatedAt) {
  const date = new Date(lastMessageCreatedAt);
  const hour = date.getHours() < 10 ? `0${date.getHours()}` : date.getHours();
  const min = date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes();
  return `${hour}:${min}`;
}

export default function ChatItem({
  chatId,
  onClick,
  searchedValue,
  withLastMessage,
  withUnreadBadge,
}) {
  const chat = useSelector(selectChat(chatId));

  const chatDisplayAvatar = useSelector(selectChatDisplayAvatar(chatId));

  const { type } = chat;
  const typeIconStyle = {
    color: '#11a6a1',
    margin: '0 3px!important',
    width: '22px!important',
  };

  if (!!searchedValue && !(chat.displayName.includes(searchedValue))) return null;

  return (
    <Grid item xs={12} onClick={onClick} sx={{ cursor: 'pointer' }}>
      <Stack direction="row" spacing={2}>
        <Avatar src={chatDisplayAvatar} />
        <Stack
          direction="column"
          sx={{ minWidth: 0, flexGrow: 1 }}
          spacing={1}
        >
          <Stack direction="row" alignItems="center" sx={{ flexGrow: 1 }}>
            <Name>
              {type === 'g' && (<GroupsIcon sx={typeIconStyle} />)}
              {type === 'c' && (<CampaignIcon sx={typeIconStyle} />)}
              {type === 'g' && (<span>گروه&nbsp;</span>)}
              {type === 'c' && (<span>کانال&nbsp;</span>)}
              <span>{chat.displayName}</span>
            </Name>

            <Badge
              color="error"
              variant="dot"
              sx={{ marginRight: 2 }}
              invisible={!(withUnreadBadge && chat.hasUnreadMessage)}
            />

            {withLastMessage && (
              <LastMessageTime>
                {getLastMessageHour(chat.lastMessageCreatedAt)}
              </LastMessageTime>
            )}
          </Stack>

          {withLastMessage && (
            <LastMessage noWrap>
              {getMessageText(chat.lastMessageText, chat.lastMessageType)}
            </LastMessage>
          )}
        </Stack>
      </Stack>
    </Grid>
  );
}
