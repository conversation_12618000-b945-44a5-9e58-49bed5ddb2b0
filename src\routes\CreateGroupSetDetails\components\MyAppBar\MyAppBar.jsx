import { IconButton, Stack, Typography } from '@mui/material';
import CustomAppBar from 'components/CustomAppBar/CustomAppBar';
import BackButton from 'components/CustomAppBar/components/BackButton/BackButton';
import SubTitle from 'components/CustomAppBar/components/SubTitle/SubTitle';
import IconSax from 'components/IconSax/IconSax';

export default function MyAppBar({ onCreateClick }) {
  return (
    <CustomAppBar>
      <Stack direction="row" sx={{ mt: 1, mb: 1, width: '100%' }}>
        <BackButton />

        <Stack flexGrow={1}>
          <SubTitle text="گروه جدید" />
        </Stack>

        <IconButton color="primary" onClick={onCreateClick}>
          <IconSax name="tick-square" />
          <Typography sx={{ fontWeight: 400, fontSize: '14px', ml: 1 }}>ایجاد</Typography>
        </IconButton>
      </Stack>
    </CustomAppBar>

  );
}
