import {
  IconButton,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  SvgIcon,
} from '@mui/material';
import { useState } from 'react';
import { ReactComponent as MoreIcon } from 'static/icons/more.svg';
import { useNavigate } from 'react-router-dom';
import { setPathParam } from 'utils';
import { PATHS } from 'constants';
import ReportGmailerrorredIcon from '@mui/icons-material/ReportGmailerrorred';
import InsightsIcon from '@mui/icons-material/Insights';
import EditIcon from '@mui/icons-material/Edit';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { useSelector } from 'react-redux';
import { Quiz } from '@mui/icons-material';
import { selectMe } from '../../../../store/auth';

export default function AppbarMenu({
  contentId, showDeleteBottomSheet, showReportBottomSheet, isMine,
  evaluationRequested = false,
  total_score = 0,
}) {
  const navigate = useNavigate();
  const me = useSelector(selectMe);
  const [anchorEl, setAnchorEl] = useState(null);
  const open = !!anchorEl;

  function onClick(e) {
    setAnchorEl(e.currentTarget);
  }
  function onClose() {
    setAnchorEl(null);
  }

  function deleteContent() {
    onClose();
    showDeleteBottomSheet();
  }

  function onUpdateClick() {
    navigate(setPathParam(PATHS.updateContent, 'contentId', contentId));
  }

  function onAnalyticsClick() {
    navigate(setPathParam(PATHS.contentAnalytics, 'contentId', contentId));
  }

  function onSurveyReportClick() {
    navigate(setPathParam(PATHS.contentEvaluateReport, 'contentId', contentId));
  }

  function onReportClick() {
    onClose();
    showReportBottomSheet();
  }

  return (
    <>
      <IconButton onClick={onClick}>
        <SvgIcon
          component={MoreIcon}
          sx={{ transform: 'rotate(90deg)' }}
        />
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={onClose}
        sx={{ fontSize: '14px' }}
      >
        <MenuItem onClick={onAnalyticsClick}>
          <ListItemIcon>
            <InsightsIcon />
          </ListItemIcon>
          <ListItemText>آمار محتوا</ListItemText>
        </MenuItem>

        { !!me && !!me.can_elect_content && (!!evaluationRequested || !!total_score) && (
        <MenuItem onClick={onSurveyReportClick}>
          <ListItemIcon>
            <Quiz />
          </ListItemIcon>
          <ListItemText>آمار ارزیابی</ListItemText>
        </MenuItem>
        )}

        {!isMine && (
          <MenuItem onClick={onReportClick}>
            <ListItemIcon><ReportGmailerrorredIcon /></ListItemIcon>
            <ListItemText>گزارش محتوا</ListItemText>
          </MenuItem>
        )}

        {isMine && (
          <>
            <MenuItem onClick={onUpdateClick}>
              <ListItemIcon>
                <EditIcon />
              </ListItemIcon>
              <ListItemText>ویرایش</ListItemText>
            </MenuItem>

            <MenuItem onClick={deleteContent}>
              <ListItemIcon>
                <DeleteOutlineIcon />
              </ListItemIcon>
              <ListItemText>حذف محتوا</ListItemText>
            </MenuItem>
          </>
        )}
      </Menu>
    </>
  );
}
