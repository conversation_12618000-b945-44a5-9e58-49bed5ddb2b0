import {
  Container,
  FormControl,
  FormHelperText,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { getMe, getCities, getStates, updateUserProfile } from 'apis/auth';
import LoadingButton from 'components/LoadingButton/LoadingButton';
import { GENDER, MONTHS, PATHS } from 'constants';
import { setMe, selectMe } from 'store/auth';

let USERNAME_TOOLTIP = `نام کاربری ترکیبی از اعداد 0-9 و حروف a-z است

و نباید با عدد شروع شود. حداقل طول مجاز

۶ حرف است.`;
USERNAME_TOOLTIP = (
  <Typography variant="caption">
    نام کاربری ترکیبی از اعداد 0-9 و حروف a-z است
    <br />
    و نباید با عدد شروع شود. حداقل طول مجاز
    <br />۶ حرف است.
  </Typography>
);

const START_YEAR = 1330;
const END_YEAR = 1390;
const YEARS = [];
for (let i = START_YEAR; i < END_YEAR; i++) {
  YEARS.push(i);
}

const DEFAULT_FORM_VALUES = {
  username: '',
  firstName: '',
  lastName: '',
  gender: GENDER[0].value,
  birthdateDay: 1,
  birthdateMonth: MONTHS[0],
  birthdateYear: YEARS[0],
  state: '',
  city: '',
  bio: '',
};

function buildDays(monthId) {
  const DAYS = [];
  const NUMBER_OF_DAYS = monthId < 6 ? 32 : 31;
  for (let i = 1; i < NUMBER_OF_DAYS; i++) {
    DAYS.push(i);
  }
  return DAYS;
}

export default function RegisterationFrom() {
  const dispatch = useDispatch();

  const [values, setValues] = useState(DEFAULT_FORM_VALUES);
  const [errors, setErrors] = useState({});
  const [usernameTooltipOpen, setUsernameTooltipOpen] = useState(false);
  const me = useSelector(selectMe);

  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [loading, setLoading] = useState(false);

  const DAYS = buildDays(values.birthdateMonth.value);

  const navigate = useNavigate();

  useEffect(() => {
    async function getMeAsync() {
      const response = await getMe();
      dispatch(setMe(response.data));
    }
    if (!me) {
      getMeAsync();
    }
  }, []);

  useEffect(() => {
    async function getStatesAsync() {
      const response = await getStates();
      setStates(response?.data?.results || []);
    }
    getStatesAsync();
  }, []);

  useEffect(() => {
    async function getCitiesAsync() {
      const response = await getCities(values.state);
      setCities(response?.data?.results || []);
    }
    getCitiesAsync();
  }, [values.state]);

  function handleChange(e) {
    const { name, value } = e.target;

    const newValues = {
      ...values,
      [name]: value,
    };
    if (name === 'state') {
      newValues.city = '';
      setCities([]);
    }

    setValues(newValues);
    setErrors({});
  }

  async function submitForm() {
    setLoading(true);
    setErrors({});

    const body = {
      display_username: values.username,
      first_name: values.firstName,
      last_name: values.lastName,
      gender: values.gender ? 'F' : 'M',
      birthdate_day: values.birthdateDay,
      birthdate_month: values.birthdateMonth.value,
      birthdate_year: values.birthdateYear,
      city: values.city !== '' ? values.city : undefined,
      bio: values.bio,
    };
    try {
      await updateUserProfile(me.id, body);
      navigate(PATHS.home, { replace: true });
    } catch (e) {
      if (e.response.status === 400) {
        setErrors(e.response.data);
      }
    }
    setLoading(false);
  }

  return (
    <Container maxWidth="md">
      <Stack
        sx={theme => ({
          margin: theme.spacing(3),
          overflowY: 'scroll',
        })}
        spacing={3}
      >
        <TextField
          variant="outlined"
          label="نام کاربری (اجباری)"
          name="username"
          value={values.username}
          onChange={handleChange}
          helperText={errors.display_username}
          error={!!errors.display_username}
          InputProps={{
            endAdornment: (
              <InputAdornment>
                <Tooltip
                  title={USERNAME_TOOLTIP}
                  onClose={() => {
                    setInterval(() => setUsernameTooltipOpen(false), 5000);
                  }}
                  open={usernameTooltipOpen}
                  PopperProps={{
                    disablePortal: true,
                  }}
                  disableFocusListener
                  disableHoverListener
                >
                  <InfoOutlinedIcon
                    onClick={() => {
                      setUsernameTooltipOpen(true);
                    }}
                  />
                </Tooltip>
              </InputAdornment>
            ),
          }}
        />

        <TextField
          variant="outlined"
          label="نام"
          name="firstName"
          value={values.firstName}
          onChange={handleChange}
          helperText={errors.first_name}
        />
        <TextField
          variant="outlined"
          label="نام خانوادگی"
          name="lastName"
          value={values.lastName}
          onChange={handleChange}
          helperText={errors.last_name}
        />

        {/** Gender */}
        <FormControl fullWidth>
          <InputLabel>جنسیت</InputLabel>
          <Select
            label="جنسیت"
            value={values.gender}
            name="gender"
            onChange={handleChange}
          >
            {GENDER.map(gender => (
              <MenuItem key={gender.value} value={gender.value}>
                {gender.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/** Birthdate */}
        <Stack spacing={1} alignItems="stretch">
          <Typography>تاریخ تولد</Typography>
          <Stack direction="row" spacing={2} alignItems="stretch">
            <FormControl fullWidth>
              <InputLabel>روز</InputLabel>
              <Select
                label="روز"
                value={values.birthdateDay}
                name="birthdateDay"
                onChange={handleChange}
              >
                {DAYS.map(d => (
                  <MenuItem key={d} value={d}>
                    {d}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>ماه</InputLabel>
              <Select
                label="ماه"
                value={values.birthdateMonth}
                name="birthdateMonth"
                onChange={handleChange}
              >
                {MONTHS.map(m => (
                  <MenuItem key={m.value} value={m}>
                    {m.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>سال</InputLabel>
              <Select
                label="سال"
                value={values.birthdateYear}
                name="birthdateYear"
                onChange={handleChange}
              >
                {YEARS.map(y => (
                  <MenuItem key={y} value={y}>
                    {y}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Stack>
        </Stack>

        {/** State */}
        <FormControl>
          <InputLabel>استان (اجباری)</InputLabel>
          <Select
            label="استان (اجباری)"
            value={values.state}
            name="state"
            onChange={handleChange}
            required
          >
            {states &&
              states.map(s => (
                <MenuItem key={s.id} value={s.id}>
                  {s.name}
                </MenuItem>
              ))}
          </Select>
        </FormControl>

        <FormControl>
          <InputLabel error={!!errors.city}>شهرستان (اجباری)</InputLabel>

          <Select
            label="شهرستان (اجباری)"
            value={values.city}
            name="city"
            onChange={handleChange}
            disabled={!values.state || cities.length === 0}
            error={!!errors.city}
          >
            {cities &&
              cities.map(city => (
                <MenuItem key={city.id} value={city.id}>
                  {city.name}
                </MenuItem>
              ))}
          </Select>
          <FormHelperText error={!!errors.city}>{errors.city}</FormHelperText>
        </FormControl>

        <TextField
          multiline
          label="بیوگرافی"
          rows={4}
          value={values.bio}
          name="bio"
          onChange={handleChange}
          helperText={errors.bio}
        />

        <LoadingButton
          variant="contained"
          size="large"
          onClick={submitForm}
          loading={loading}
        >
          ذخیره
        </LoadingButton>
      </Stack>
    </Container>
  );
}
