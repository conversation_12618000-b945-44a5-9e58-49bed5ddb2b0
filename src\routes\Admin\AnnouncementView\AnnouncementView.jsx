import {
  Box, Grid, Tab, Tabs, Typography,
} from '@mui/material';
import { useIsDesktop } from 'utils';
import { useState } from 'react';
import { useLoaderData, useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { PATHS } from '../../../constants';
import { getAnnouncement, getAnnouncementDailyChart, getAnnouncementViewers } from '../../../apis/announcement';
import ChartSection from './components/ChartSection/ChartSection';
import SampleAnnounceSection from './components/SampleAnnounceSection/SampleAnnounceSection';
import ViewersSection from './components/ViewersSection/ViewersSection';
import LoadingPage from '../../../components/LoadingPage/LoadingPage';

export async function loader({ params }) {
  return params.announcementId;
}

export default function AnnouncementView() {
  const isDesktop = useIsDesktop();

  const navigate = useNavigate();

  const announcementId = useLoaderData();
  const { isLoading: isLoadingAnnounce, data: announceData } = useQuery([PATHS.admin.announcementView, announcementId], () => getAnnouncement(announcementId));
  const { isLoading: isLoadingChart, data: chartData } = useQuery([PATHS.admin.announcementView, announcementId, 'DailyChart'], () => getAnnouncementDailyChart(announcementId));
  const { isLoading: isLoadingViewers, data: viewersData } = useQuery([PATHS.admin.announcementView, announcementId, 'Viewers'], () => getAnnouncementViewers(announcementId));

  const announcement = isLoadingAnnounce ? {} : announceData?.data;
  const chart = isLoadingChart ? {} : chartData?.data?.results;
  const viewers = isLoadingViewers ? {} : viewersData?.data?.results;

  const tabs = [
    {
      value: 'tab1',
      label: 'روند بازدید',
    },
    {
      value: 'tab2',
      label: 'لیست بازدید',
    },
    {
      value: 'tab3',
      label: 'نمونه اعلان',
    },
  ];
  const [selectedTab, setSelectedTab] = useState(tabs[0].value);

  if (isLoadingViewers || isLoadingAnnounce || isLoadingChart) {
    return <LoadingPage />;
  }

  return (
    <Grid container xs={12} sx={{ height: '100%', overflowY: 'scroll' }}>

      {isDesktop ? (
        <>
          <ChartSection data={chart} />

          <Grid container>
            <ViewersSection data={viewers} />
            <SampleAnnounceSection announce={announcement} />
          </Grid>
        </>
      ) : (
        <>
          <Box sx={{ width: '100%', marginBottom: '20px' }}>
            <Tabs
              variant="fullWidth"
              scrollButtons="auto"
              value={selectedTab}
              onChange={(e, newValue) => setSelectedTab(newValue)}
            >
              {tabs.map((tab) => (
                <Tab
                  key={tab.value}
                  label={<Typography sx={{ fontSize: '14px' }} noWrap>{tab.label}</Typography>}
                  value={tab.value}
                />
              ))}
            </Tabs>
          </Box>

          {selectedTab === 'tab1' && <ChartSection data={chart} />}
          {selectedTab === 'tab2' && <ViewersSection data={viewers} />}
          {selectedTab === 'tab3' && <SampleAnnounceSection announce={announcement} />}
        </>
      )}

    </Grid>
  );
}
