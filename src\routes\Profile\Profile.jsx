import { Grid } from '@mui/material';
import { useSelector } from 'react-redux';
import { useState } from 'react';
import { useLoaderData } from 'react-router-dom';
import { useQuery } from 'react-query';
import SearchBox from 'components/SearchBox/SearchBox';
import { FAVORITE, PATHS } from 'constants';
import { getUser } from 'apis/auth';
import { selectMe } from 'store/auth';
import Contents from 'components/Contents/Contents';
import { buildSearchConfigFromURL } from 'components/SearchBox/searchConfig';
import LoadingPage from 'components/LoadingPage/LoadingPage';
import { ReactComponent as FolderIcon } from 'static/icons/folder-2.svg';
import { ReactComponent as FavoriteIcon } from 'static/icons/archive-book.svg';
import MyTabs from 'components/MyTabs/MyTabs';
import { getViewModeFromURL } from 'components/SearchBox/viewModeConfig';
import { useIsDesktop } from 'utils';
import DesktopAppBar, {
  DESKTOP_APPBAR_HEIGHT,
} from 'components/DesktopAppBar/DesktopAppBar';
import UserDetail from './components/UserDetail/UserDetail';
import UploadFab from './components/UploadFab/UploadFab';

export const TABS = [
  {
    label: 'فایل‌های من',
    icon: FolderIcon,
    value: 0,
  },
  {
    label: 'نشان شده‌',
    icon: FavoriteIcon,
    value: 1,
  },
];

export async function loader({ request, params }) {
  const url = new URL(request.url);
  const searchConfig = buildSearchConfigFromURL(url);
  const viewMode = getViewModeFromURL(url);

  const userId = params.userId === 'undefined' ? undefined : params.userId;
  return { userId, searchConfig, viewMode };
}

function shouldFilterBasedOnAuthor(isMe, isInMyUploadedFilesTab) {
  if (isMe) {
    return isInMyUploadedFilesTab;
  }
  return true;
}

export default function Profile() {
  const [tab, setTab] = useState(TABS[0].value);
  const isDesktop = useIsDesktop();

  const loaderData = useLoaderData();
  const { userId, searchConfig, viewMode } = loaderData;

  const me = useSelector(selectMe);

  const { isLoading, data } = useQuery([PATHS.profile, userId], () =>
    getUser(userId),
  );

  if (isLoading) return <LoadingPage />;

  const user = data.data;
  const isMe = !!me && me.id === user.id;
  const isAdmin = !!me && !!me.can_see_admin_panel;
  const isInMyUploadedFilesTab = tab === 0;

  return (
    <>
      {isDesktop && <DesktopAppBar />}

      <Grid
        container
        columnSpacing={4}
        sx={{ mt: isDesktop ? DESKTOP_APPBAR_HEIGHT : '0' }}
      >
        <Grid item xs={12} lg={3}>
          <UserDetail
            userId={user.id}
            firstName={user.first_name}
            lastName={user.last_name}
            username={user.display_username}
            bio={user.bio}
            state={user.state_name}
            city={user.city_name}
            avatar={user.avatar}
            isMe={isMe}
            isAdmin={isAdmin}
            directChatId={user.direct_chat_id}
          />
        </Grid>

        <Grid item xs={12} lg={9}>
          <SearchBox
            sx={{ marginTop: 2 }}
            searchConfig={searchConfig}
            viewMode={viewMode}
          />

          {isMe && (
            <MyTabs
              tabs={TABS}
              selectedTab={tab}
              setSelectedTab={setTab}
              variant={isDesktop ? 'standard' : 'fullWidth'}
            />
          )}

          {/**
           * when user is at tab = 0, he should be able to see all posts he
           *  has favorited. Eventhough he was not author of
           * */}
          <Contents
            sx={{ marginTop: isMe ? '' : '16px' }}
            author={
              shouldFilterBasedOnAuthor(isMe, isInMyUploadedFilesTab)
                ? user.id
                : ''
            }
            searchConfig={searchConfig}
            interactionType={tab === 1 ? FAVORITE : ''}
            withAnalytics
            withTypeIcon
            viewMode={viewMode}
          />

          {isMe && <UploadFab />}
        </Grid>
      </Grid>
    </>
  );
}
