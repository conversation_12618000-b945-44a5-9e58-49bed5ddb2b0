import { Button, Paper, Typography } from '@mui/material';
import { Box } from '@mui/system';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import Wordcloud from 'highcharts/modules/wordcloud';
import { useEffect, useState } from 'react';
import ButtonGroup from '@mui/material/ButtonGroup';
import CardTitle from './CardTitle';

Wordcloud(Highcharts);

export default function HashtagCloud() {
  const [chartOptions, setChartOptions] = useState({});

  const data = [
    { name: 'برنامه_نویسی#', weight: 15 },
    { name: 'جاوااسکریپت#', weight: 10 },
    { name: 'پایتون#', weight: 12 },
    { name: 'هوش_مصنوعی#', weight: 8 },
    { name: 'توسعه_وب#', weight: 9 },
    { name: 'کدنویسی#', weight: 11 },
    { name: 'چت_جی_پی_تی#', weight: 14 },
    { name: 'آموزش#', weight: 7 },
  ];

  useEffect(() => {
    setChartOptions({
      lang: {
        rtl: true,
      },
      series: [
        {
          type: 'wordcloud',
          data,
          name: 'Frequency',
          rotation: {
            orientations: 1, // Only horizontal words
            from: 0,
            to: 0, // No rotation
          },
          style: {
            fontFamily: 'IRANSansX, serif', // Customize your font family here
            fontWeight: 'normal',
          },
        },
      ],
      title: {
        text: '',
      },
      legend: {
        enabled: false,
      },
      credits: {
        enabled: false,
      },
      tooltip: {
        useHTML: true,
        formatter: function () {
          return `
              <div style="font-family: 'IRANSansX', serif; font-size: 13px; direction: rtl;">
                <span style="color: ${this.color}; font-weight: bold;">${this.point.name.slice(0, -1)}</span>:
                <span>${this.point.weight}</span>
              </div>`;
        },
      },
    });
  }, []);
  return (
    <Paper
      display="flex"
      justifyContent="center"
      alignItems="center"
      textAlign="center"
      flexDirection="column"
      elevation={2}
      sx={{ flexGrow: 1, width: '100%', padding: '20px' }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <CardTitle title="ابر هشتگ" />
        <ButtonGroup variant="contained" aria-label="Basic button group">
          <Button size="small">روزانه</Button>
          <Button size="small">هفتگی</Button>
          <Button size="small">ماهانه</Button>
        </ButtonGroup>
      </Box>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} key={1} />
    </Paper>
  );
}
