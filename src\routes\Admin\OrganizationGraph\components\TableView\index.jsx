import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Button,
  Box,
  Typography,
  Card,
} from '@mui/material';
import { Link } from 'react-router-dom';
import { Delete, Edit, RemoveRedEye } from '@mui/icons-material';

// Helper function to find children
const getChildren = (data, parentId) =>
  data.filter(node => node.parent === parentId);

const NodeTable = ({ data, parentId, parentName, handleAction }) => {
  const children = getChildren(data, parentId);

  if (!children.length) return null; // No children, no table
  const btnStyle = {
    color: '#222',
    background: '#ECEDF7',
    width: '35px',
    minWidth: '35px',
    height: '35px',
    margin: '0 5px',
    boxShadow: 'none',
    '&:hover': {
      background: '#E7EAF4',
    },
  };

  return (
    <Box mb={4}>
      <Typography sx={{ fontSize: 15, fontWeight: 'bold' }} gutterBottom>
        {parentId ? `سازمان مرجع: ${parentName}` : ''}
      </Typography>
      <TableContainer component={Card}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ width: 70 }}>عکس</TableCell>
              <TableCell>نام سازمان</TableCell>
              <TableCell>توضیحات</TableCell>
              <TableCell />
            </TableRow>
          </TableHead>
          <TableBody>
            {children.map(node => (
              <React.Fragment key={node.id}>
                <TableRow>
                  <TableCell>
                    <Avatar src={node.avatar} alt={node.name} />
                  </TableCell>
                  <TableCell>{node.name}</TableCell>
                  <TableCell>{node.description || '---'}</TableCell>
                  <TableCell align={'right'}>
                    <Link
                      key="view"
                      to={`/admin-panel/organization/view/${node.id}`}
                      style={{ textDecoration: 'none' }}
                    >
                      <Button
                        variant="contained"
                        size="small"
                        sx={btnStyle}
                        // onClick={onClick}
                      >
                        <RemoveRedEye sx={{ fontSize: '14px' }} />
                      </Button>
                    </Link>
                    <Link
                      key="edit"
                      to={`/admin-panel/organization/${node.id}/update/`}
                      style={{ textDecoration: 'none' }}
                    >
                      <Button
                        variant="contained"
                        size="small"
                        sx={btnStyle}
                        // onClick={onClick}
                      >
                        <Edit sx={{ fontSize: '14px' }} />
                      </Button>
                    </Link>
                    <Button
                      variant="contained"
                      size="small"
                      sx={btnStyle}
                      // onClick={() => onDelete(node.id)}
                    >
                      <Delete sx={{ fontSize: '14px' }} />
                    </Button>
                  </TableCell>
                </TableRow>
                {/* Render child tables recursively */}
                {getChildren(data, node.id).length > 0 && (
                  <TableRow>
                    <TableCell colSpan={4}>
                      <NodeTable
                        data={data}
                        parentId={node.id}
                        parentName={node.name}
                        handleAction={handleAction}
                      />
                    </TableCell>
                  </TableRow>
                )}
              </React.Fragment>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

const TableView = ({ data, handleAction }) => {
  return (
    <Box
      sx={{
        mb: 3,
        borderRadius: '8px',
        background: 'white',
        boxShadow: '0px 2px 20px 0px #00000012',
        width: '100%',
        height: '100%',
        overflow: 'auto',
        // maxHeight: '70vh',
        minHeight: '50vh',
        p: 2,
      }}
    >
      <NodeTable
        data={data}
        parentId={null}
        parentName={null}
        handleAction={handleAction}
      />
    </Box>
  );
};

export default TableView;
