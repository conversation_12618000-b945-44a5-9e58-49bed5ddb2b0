import {
  IconButton, Menu,
} from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { useState } from 'react';
import SendMessageMenuItem from '../SendMessageMenuItem/SendMessageMenuItem';
import ReportUserMenuItem from '../ReportUserMenuItem/ReportUserMenuItem';
import ReportBottomSheet from '../ReportBottomSheet/ReportBottomSheet';

export default function ProfileMenu({ directChatId, userId }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = !!anchorEl;

  function onClick(e) {
    setAnchorEl(e.currentTarget);
  }
  function onClose() {
    setAnchorEl(null);
  }

  const [showReportBottomSheet, setShowReportBottomSheet] = useState(false);
  function onReportClick() {
    onClose();
    setShowReportBottomSheet(true);
  }

  return (
    <>
      <IconButton onClick={onClick}><MoreVertIcon /></IconButton>

      <Menu
        open={open}
        anchorEl={anchorEl}
        onClose={onClose}
      >
        <SendMessageMenuItem
          directChatId={directChatId}
          userId={userId}
        />

        <ReportUserMenuItem
          userId={userId}
          onClick={onReportClick}
        />
      </Menu>

      <ReportBottomSheet
        show={showReportBottomSheet}
        hideBottomSheet={() => setShowReportBottomSheet(false)}
        userId={userId}
      />
    </>
  );
}
