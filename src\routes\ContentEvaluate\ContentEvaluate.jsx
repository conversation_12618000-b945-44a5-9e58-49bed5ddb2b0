import {
  But<PERSON>,
  CircularProgress,
  Container,
  Divider,
  <PERSON>rid,
  <PERSON><PERSON>,
  <PERSON>ack,
  Typography,
} from '@mui/material';
import { createResponse, getQuestionList } from 'apis/content';
import LoadingPage from 'components/LoadingPage/LoadingPage';
import { PATHS } from 'constants';
import { useMutation, useQuery } from 'react-query';
import { useLoaderData, useNavigate } from 'react-router-dom';
import { setPathParam, useIsDesktop } from 'utils';
import { useState } from 'react';
import DesktopAppBar from 'components/DesktopAppBar/DesktopAppBar';
import { useSelector } from 'react-redux';
import { selectMe } from '../../store/auth';
import MyAppBar from './components/MyAppBar/MyAppBar';

export async function loader({ params }) {
  return params.contentId;
}

export default function ContentEvaluate() {
  const navigate = useNavigate();
  const me = useSelector(selectMe);

  if (!me || !me.groups?.includes('MarfookEvaluator')) return navigate(PATHS.home);

  const [responses, setResponses] = useState(Array(20).fill(0));

  const isDesktop = useIsDesktop();

  const contentId = useLoaderData();
  const { isLoading, data } = useQuery([PATHS.contentEvaluate, contentId], () =>
    getQuestionList(),
  );

  const { mutate, isLoading: isLoadingEvaluate } = useMutation(
    data =>
      createResponse({
        content: contentId,
        question: data.id,
        response: data.value,
      }),
    {
      onSuccess: () => {},
    },
  );

  const handleRatingChange = (index, id, value) => {
    const newResponses = [...responses];
    newResponses[index] = value;
    setResponses(newResponses);
    mutate({ id, value });
  };

  const handleSubmit = () => {
    if (!isLoadingEvaluate) {
      navigate(-1);
    }
  };

  if (isLoading) return <LoadingPage />;

  return (
    <>
      {!isDesktop && <MyAppBar title="" />}
      {isDesktop && <DesktopAppBar />}

      <Container sx={{ my: '20px', width: isDesktop ? '60%' : '100%' }}>
        <Typography variant="h5" align="center" gutterBottom sx={{ mb: '3px' }}>
          فرم ارزیابی محتوا
        </Typography>
        <Typography
          variant="h5"
          align="center"
          gutterBottom
          sx={{ mb: '30px!important', fontSize: '14px' }}
        >
          <span>(به سوالات زیر از ۱ تا ۱۰ امتیاز دهید)</span>
        </Typography>
        <Grid container spacing={3}>
          {data?.data?.results?.map((question, index) => (
            <Grid item xs={12} key={index} textAlign="center">
              <Typography variant="h6" gutterBottom sx={{ fontSize: '16px' }}>
                {question.question}
              </Typography>
              <Rating
                name={`rating-${index}`}
                value={responses[index]}
                onChange={(event, newValue) =>
                  handleRatingChange(index, question.id, newValue)
                }
                max={10}
              />
              <Divider />
            </Grid>
          ))}
        </Grid>
        <Stack direction="row" justifyContent="center" sx={{ mt: 4 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            sx={{ width: '100%' }}
          >
            {isLoadingEvaluate ? (
              <CircularProgress color="secondary" size={24} />
            ) : (
              'ثبت ارزیابی'
            )}
          </Button>
        </Stack>
      </Container>
    </>
  );
}
