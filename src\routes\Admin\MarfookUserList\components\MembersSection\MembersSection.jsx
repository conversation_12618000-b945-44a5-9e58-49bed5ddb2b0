import { useEffect, useState, useRef } from 'react';
import {
  Box,
  Button,
  FormControl,
  Grid,
  IconButton,
  Modal,
  Paper,
  Stack,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
  Checkbox,
  ListItemText,
  Popper,
  ClickAwayListener,
  InputBase,
  MenuItem,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Check,
  Close,
  Delete,
  Key,
  Notifications,
  PersonAdd,
  Quiz,
  VerifiedUser,
  ArrowDropDown,
} from '@mui/icons-material';
import { updateMarfookUser, updateMarfookUserBulk } from 'apis/marfook';
import { setSnackbar } from 'store/layout';
import { useDispatch } from 'react-redux';
import { useQuery } from 'react-query';
import { getOrganizationUsers, getOrganizations } from 'apis/organization';
import SearchInput from './components/SearchInput';
import BottomSheetMessage from '../../../../../components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from '../../../../../components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from '../../../../../components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import BottomSheet from '../../../../../components/BottomSheet/BottomSheet';

const CTableHead = styled(TableHead)(() => ({
  '& th': {
    fontSize: '14px',
    color: '#737373',
    border: 'none',
  },
}));
const CTableRow = styled(TableRow)(() => ({
  '& td': {
    border: 0,
    color: '#222222',
    fontSize: '16px',
  },
}));

const CustomInputBase = styled(InputBase)(() => ({
  border: '1px solid rgba(0,0,0,0.25)',
  borderRadius: 6,
  padding: '6px 8px',
  width: '100%',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
}));

const CustomTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  '& .MuiTabs-indicator': {
    backgroundColor: '#009688',
  },
}));

const CustomTab = styled(Tab)(({ theme }) => ({
  minWidth: 72,
  textTransform: 'none',
  fontWeight: theme.typography.fontWeightRegular,
  marginRight: 0,
  color: 'rgba(0, 0, 0, 0.85)',
  '&.Mui-selected': {
    color: '#009688',
    fontWeight: theme.typography.fontWeightMedium,
  },
  '&.Mui-focusVisible': {
    backgroundColor: 'rgba(100, 95, 228, 0.32)',
  },
}));

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 2 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function MembersSection({ list = [] }) {
  const dispatch = useDispatch();

  const [open, setOpen] = useState(false);

  const [members, setMembers] = useState(list);

  const redStyle = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: '14px',
    background: '#d72121',
    width: '28px',
    height: '28px',
    borderRadius: '100px',
    color: 'white',
  };
  const greenStyle = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: '14px',
    background: '#11a6a1',
    width: '28px',
    height: '28px',
    borderRadius: '100px',
    color: 'white',
  };

  const [searchText, setSearchText] = useState('');
  const [addMemberError, setAddMemberError] = useState('');
  const [filteredData, setFilteredData] = useState(list);
  const [showDeleteBottomSheet, setShowDeleteBottomSheet] = useState(false);
  const [showPermisionModal, setShowPermisionModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState(null);

  const [selectedMemberToAdd, setSelectedMemberToAdd] = useState([]);
  const [selectedAdmins, setSelectedAdmins] = useState([]);
  const [selectedTexts, setSelectedTexts] = useState([]);
  const [userSelectorOpen, setUserSelectorOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [availableUsers, setAvailableUsers] = useState([]);
  const [organizations, setOrganizations] = useState([]);
  const [admins, setAdmins] = useState([]);
  const [userSearchText, setUserSearchText] = useState('');
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [adminSearchText, setAdminSearchText] = useState('');
  const [filteredAdmins, setFilteredAdmins] = useState([]);

  const [userPermisions, setUserPermisions] = useState([]);

  const [addUserForm, setAddUserForm] = useState({ step: 1 });
  const userSelectorRef = useRef(null);

  // Fetch available users
  useQuery({
    queryKey: ['OrganizationUserKey'],
    queryFn: () => getOrganizationUsers(),
    onSuccess: data => {
      if (data.status === 200) {
        setAvailableUsers(data.data);
      }
    },
  });

  // Fetch organizations and extract admins
  useQuery({
    queryKey: ['OrganizationKey'],
    queryFn: () => getOrganizations(),
    onSuccess: data => {
      if (data.status === 200) {
        setOrganizations(data.data);

        // Extract all admins from organizations
        const allAdmins = [];
        data.data.forEach(org => {
          if (org.admins && org.admins.length > 0) {
            org.admins.forEach(admin => {
              // Add organization info to admin
              const adminWithOrg = {
                ...admin,
                organizationName: org.name,
                organizationId: org.id,
              };
              // Avoid duplicates
              if (!allAdmins.find(a => a.id === admin.id)) {
                allAdmins.push(adminWithOrg);
              }
            });
          }
        });
        setAdmins(allAdmins);
      }
    },
  });

  // Filter users based on search text
  useEffect(() => {
    const searchTerm = userSearchText.toLowerCase();
    const filtered = availableUsers.filter(item => {
      // Basic text search in name and username
      const basicSearch =
        `${item.first_name?.toLowerCase()}${item.last_name?.toLowerCase()}${item.username?.toLowerCase()}`.includes(
          searchTerm,
        );

      // Phone number search with zero handling
      let phoneSearch = false;
      if (item.phone) {
        const phoneStr = item.phone.toString();
        // Direct match
        phoneSearch = phoneStr.includes(searchTerm);

        // If search starts with 0, also try without 0
        if (!phoneSearch && searchTerm.startsWith('0')) {
          const searchWithoutZero = searchTerm.substring(1);
          phoneSearch = phoneStr.includes(searchWithoutZero);
        }

        // If phone doesn't start with 0 but search doesn't, try adding 0
        if (
          !phoneSearch &&
          !searchTerm.startsWith('0') &&
          !phoneStr.startsWith('0')
        ) {
          phoneSearch = phoneStr.includes(searchTerm);
        }
      }

      return basicSearch || phoneSearch;
    });
    setFilteredUsers(filtered);
  }, [userSearchText, availableUsers]);

  // Filter admins based on search text
  useEffect(() => {
    const searchTerm = adminSearchText.toLowerCase();
    const filtered = admins.filter(item => {
      // Basic text search in name and organization
      const basicSearch =
        `${item.first_name?.toLowerCase()}${item.last_name?.toLowerCase()}${item.organizationName?.toLowerCase()}`.includes(
          searchTerm,
        );

      // Phone number search with zero handling
      let phoneSearch = false;
      if (item.phone) {
        const phoneStr = item.phone.toString();
        // Direct match
        phoneSearch = phoneStr.includes(searchTerm);

        // If search starts with 0, also try without 0
        if (!phoneSearch && searchTerm.startsWith('0')) {
          const searchWithoutZero = searchTerm.substring(1);
          phoneSearch = phoneStr.includes(searchWithoutZero);
        }

        // If phone doesn't start with 0 but search doesn't, try adding 0
        if (
          !phoneSearch &&
          !searchTerm.startsWith('0') &&
          !phoneStr.startsWith('0')
        ) {
          phoneSearch = phoneStr.includes(searchTerm);
        }
      }

      return basicSearch || phoneSearch;
    });
    setFilteredAdmins(filtered);
  }, [adminSearchText, admins]);

  // Update selected texts when selected members or admins change
  useEffect(() => {
    if (selectedMemberToAdd.length === 0 && selectedAdmins.length === 0) {
      setSelectedTexts([]);
      return;
    }

    const userTexts = selectedMemberToAdd
      .map(id => {
        const user = availableUsers.find(u => u.id === id);
        return user
          ? user.first_name
            ? `${user.first_name} ${user.last_name}`
            : user.username
          : '';
      })
      .filter(text => text);

    const adminTexts = selectedAdmins
      .map(id => {
        const admin = admins.find(a => a.id === id);
        return admin
          ? `${admin.first_name} ${admin.last_name} (مدیر ${admin.organizationName})`
          : '';
      })
      .filter(text => text);

    setSelectedTexts([...adminTexts, ...userTexts]);
  }, [selectedMemberToAdd, selectedAdmins, availableUsers, admins]);

  useEffect(() => {
    const filtered = members
      .filter(
        item =>
          item?.display_username
            ?.toLowerCase()
            .includes(searchText?.toLowerCase()) ||
          item?.last_name?.toLowerCase().includes(searchText?.toLowerCase()) ||
          item?.first_name?.toLowerCase().includes(searchText?.toLowerCase()),
      )
      .map(member => ({
        ...member,
      }));

    setFilteredData(filtered);
  }, [searchText, members]);
  const handleInputChange = e => {
    setSearchText(e.target.value);
  };
  const handleDelete = member => {
    setShowDeleteBottomSheet(true);
    setSelectedMember(member);
  };
  const handlePermissions = member => {
    setShowPermisionModal(true);
    setUserPermisions(member.groups);
    setSelectedMember(member);
  };

  // User selection handlers
  const handleUserSelectorToggle = () => {
    setUserSelectorOpen(prev => !prev);
  };

  const handleUserSelectorClose = event => {
    setUserSelectorOpen(false);
  };

  const handleUserSearchChange = e => {
    setUserSearchText(e.target.value);
  };

  const handleAdminSearchChange = e => {
    setAdminSearchText(e.target.value);
  };

  const handleUserSelect = (userId, userName) => {
    setSelectedMemberToAdd(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId],
    );
  };

  const handleSelectAllUsers = () => {
    const allUserIds = filteredUsers.map(user => user.id);
    if (selectedMemberToAdd.length === filteredUsers.length) {
      setSelectedMemberToAdd([]);
    } else {
      setSelectedMemberToAdd(allUserIds);
    }
  };

  // Tab handling
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setUserSearchText('');
    setAdminSearchText('');
    // if (newValue === 0) {
    //   // Reset admin selections when switching to members tab
    //   setSelectedAdmins([]);
    // } else {
    //   // Reset member selections when switching to admins tab
    //   setSelectedMemberToAdd([]);
    // }
  };

  // Admin selection handlers
  const handleAdminSelect = (adminId, adminName) => {
    setSelectedAdmins(prev =>
      prev.includes(adminId)
        ? prev.filter(id => id !== adminId)
        : [...prev, adminId],
    );
  };

  const handleSelectAllAdmins = () => {
    const visibleAdminIds = (
      adminSearchText !== '' ? filteredAdmins : admins
    ).map(admin => admin.id);
    const currentVisibleSelected = selectedAdmins.filter(id =>
      visibleAdminIds.includes(id),
    );

    if (currentVisibleSelected.length === visibleAdminIds.length) {
      // Deselect all visible admins
      setSelectedAdmins(prev =>
        prev.filter(id => !visibleAdminIds.includes(id)),
      );
    } else {
      // Select all visible admins
      setSelectedAdmins(prev => [...new Set([...prev, ...visibleAdminIds])]);
    }
  };

  const handleAddMember = async () => {
    try {
      if (selectedMemberToAdd.length === 0 && selectedAdmins.length === 0) {
        setAddMemberError('کاربر/کاربران یا ادمین مورد نظر را انتخاب کنید!');
        return;
      }
      if (userPermisions.length === 0) {
        setAddMemberError('دسترسی مورد نظر را انتخاب کنید!');
        return;
      }

      // const promises = [];

      // // Add individual users
      // if (selectedMemberToAdd.length > 0) {
      //   const userPromises = selectedMemberToAdd.map(userId =>
      //     updateMarfookUser(userId, {
      //       groups: userPermisions.filter(
      //         x =>
      //           x === 'MarfookManager' ||
      //           x === 'MarfookExpert' ||
      //           x === 'MarfookEvaluator' ||
      //           x === 'MarfookInstructionViewer',
      //       ),
      //     }),
      //   );
      //   promises.push(...userPromises);
      // }

      // // Add selected admins
      // if (selectedAdmins.length > 0) {
      //   const adminPromises = selectedAdmins.map(adminId =>

      //   );
      //   promises.push(...adminPromises);
      // }

      // await Promise.all(promises);

      await updateMarfookUserBulk({
        groups: userPermisions.filter(
          x =>
            x === 'MarfookManager' ||
            x === 'MarfookExpert' ||
            x === 'MarfookEvaluator' ||
            x === 'MarfookInstructionViewer',
        ),
        users: [...selectedMemberToAdd, ...selectedAdmins],
      });

      dispatch(
        setSnackbar({
          message: 'کاربران با موفقیت افزوده شدند',
          severity: 'success',
        }),
      );

      // Refresh the members list - you might need to refetch data here
      window.location.reload();

      setUserPermisions([]);
      setSelectedMemberToAdd([]);
      setSelectedAdmins([]);
      setSelectedTexts([]);
      setSelectedMember(null);
      setOpen(false);
    } catch (e) {
      console.error(e);
      dispatch(
        setSnackbar({
          message: 'خطا در افزودن کاربران!',
          severity: 'error',
        }),
      );
    } finally {
      setAddUserForm({ step: 1 });
    }
  };

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setAddUserForm({ step: 1 });
    setSelectedMemberToAdd([]);
    setSelectedAdmins([]);
    setSelectedTexts([]);
    setSelectedMember(null);
    setUserSelectorOpen(false);
    setUserSearchText('');
    setAdminSearchText('');
    setTabValue(0);
    setAddMemberError('');
    setOpen(false);
  };
  const handleClosePermisionModal = () => {
    setAddUserForm({ step: 1 });
    setSelectedMemberToAdd({});
    setSelectedMember(null);
    setShowPermisionModal(false);
  };

  const submitPermisions = async () => {
    try {
      if (selectedMember.id) {
        await updateMarfookUser(selectedMember.id, {
          groups: userPermisions.filter(
            x =>
              x === 'MarfookManager' ||
              x === 'MarfookExpert' ||
              x === 'MarfookEvaluator' ||
              x === 'MarfookInstructionViewer',
          ),
        });
        selectedMember.groups = userPermisions;
        dispatch(
          setSnackbar({
            message: 'دسترسی کاربر ویرایش شد.',
            severity: 'success',
          }),
        );
        // window.location.reload();
      }
    } catch (e) {
      dispatch(
        setSnackbar({
          message: 'خطا در ویرایش سطح دسترسی کاربر!',
          severity: 'error',
        }),
      );
    } finally {
      setUserPermisions([]);
      setShowPermisionModal(false);
    }
  };

  const deleteUser = async () => {
    try {
      if (selectedMember.id) {
        await updateMarfookUser(selectedMember.id, {
          groups: [],
        });
        setMembers(members.filter(member => member.id !== selectedMember.id));
        dispatch(
          setSnackbar({
            message: 'کابر از لیست مرفوک حذف شد',
            severity: 'success',
          }),
        );
        // window.location.reload();
      }
    } catch (e) {
      dispatch(
        setSnackbar({
          message: 'خطا در حذف کاربر!',
          severity: 'error',
        }),
      );
    } finally {
      setShowDeleteBottomSheet(false);
    }
  };

  const colTitles = [
    'عکس',
    'نام عضو',
    'مدیر',
    'کارشناس',
    'ارزیاب',
    'تابلو اعلانات',
    'اقدامات',
  ];

  return (
    <Grid
      sx={{
        py: 2,
        px: 3,
        mb: 3,
        borderRadius: '8px',
        background: 'white',
        boxShadow: '0px 2px 20px 0px #00000012',
        width: '100%',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
          gap: '10px',
          paddingBottom: '10px',
          borderBottom: '1px solid #ccc',
        }}
      >
        <SearchInput
          label="جستجو"
          onChange={handleInputChange}
          sx={{ maxWidth: '400px', zIndex: '10' }}
        />
        <Button
          variant="contained"
          size="large"
          type="submit"
          sx={{ width: '180px' }}
          onClick={() => {
            handleOpen();
          }}
        >
          <PersonAdd sx={{ marginRight: '6px' }} />
          افزودن دسترسی
        </Button>
      </Box>
      <TableContainer>
        <Table>
          <CTableHead>
            <TableRow>
              {colTitles.map(title => (
                <TableCell>{title}</TableCell>
              ))}
            </TableRow>
          </CTableHead>
          <TableBody>
            {filteredData.map(row => (
              <CTableRow key={row.id}>
                <TableCell sx={{ maxWidth: '40px' }}>
                  <img
                    width="40px"
                    height="40px"
                    alt="avatar"
                    src={row.avatar || '/logo.png'}
                    style={{ borderRadius: '100px' }}
                  />
                </TableCell>
                <TableCell width="90%">
                  <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                    {row.first_name
                      ? `${row.first_name} ${row.last_name}`
                      : row.username}
                  </span>
                  <br />
                  <span style={{ fontSize: '14px' }}>
                    {row.display_username ? `@${row.display_username}` : ''}
                  </span>
                </TableCell>
                <TableCell>
                  {row.groups.includes('MarfookManager') ? (
                    <div style={greenStyle}>
                      <Check />
                    </div>
                  ) : (
                    <div style={redStyle}>
                      <Close />
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  {row.groups.includes('MarfookExpert') ? (
                    <div style={greenStyle}>
                      <Check />
                    </div>
                  ) : (
                    <div style={redStyle}>
                      <Close />
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  {row.groups.includes('MarfookEvaluator') ? (
                    <div style={greenStyle}>
                      <Check />
                    </div>
                  ) : (
                    <div style={redStyle}>
                      <Close />
                    </div>
                  )}
                </TableCell>
                <TableCell width="20%">
                  {row.groups.includes('MarfookInstructionViewer') ? (
                    <div style={greenStyle}>
                      <Check />
                    </div>
                  ) : (
                    <div style={redStyle}>
                      <Close />
                    </div>
                  )}
                </TableCell>

                <TableCell>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Tooltip title="تغییر دسترسی" placement="top">
                      <IconButton
                        sx={{ display: 'flex' }}
                        onClick={() => handlePermissions(row)}
                      >
                        <Key />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="حذف از اعضای مرفوک" placement="top">
                      <IconButton
                        sx={{ display: 'flex' }}
                        onClick={() => handleDelete(row)}
                      >
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </CTableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Modal
        open={open}
        // onClose={handleClose}
        aria-labelledby="child-modal-title"
        aria-describedby="child-modal-description"
      >
        <Grid
          container
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            pt: 2,
            px: 4,
            pb: 3,
            borderRadius: '8px',
            background: 'white',
            boxShadow: '0px 2px 20px 0px #00000012',
            width: '50%',
          }}
        >
          <Stack width="100%">
            <Typography
              align="center"
              fontWeight="bold"
              fontSize={18}
              sx={{ pb: 2, borderBottom: '1px solid #ccc' }}
            >
              افزودن دسترسی مرفوک
            </Typography>
            {addUserForm.step === 1 && (
              <>
                <Typography fontSize={16} sx={{ mt: 4 }}>
                  دسترسی کاربر را انتخاب نمایید:
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    alignItems: 'center',
                    mt: 3,
                    pb: 4,
                    justifyContent: 'center',
                    '& > :not(style)': {
                      display: 'flex',
                      m: 1,
                      width: 110,
                      height: 110,
                      cursor: 'pointer',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: 'none',
                      border: '1px solid #ccc',
                      color: '#bbb',
                      flexDirection: 'column',
                      fontSize: '14px',
                    },
                  }}
                >
                  <Paper
                    elevation={2}
                    onClick={() =>
                      setUserPermisions(
                        userPermisions.includes('MarfookManager')
                          ? userPermisions.filter(x => x !== 'MarfookManager')
                          : [...userPermisions, 'MarfookManager'],
                      )
                    }
                    style={{
                      backgroundColor: userPermisions.includes('MarfookManager')
                        ? '#11a6a1'
                        : '',
                      color: userPermisions.includes('MarfookManager')
                        ? '#333'
                        : '#bbb',
                    }}
                  >
                    <VerifiedUser sx={{ mr: '3px' }} />
                    مدیر مرفوک
                  </Paper>
                  <Paper
                    elevation={2}
                    onClick={() =>
                      setUserPermisions(
                        userPermisions.includes('MarfookExpert')
                          ? userPermisions.filter(x => x !== 'MarfookExpert')
                          : [...userPermisions, 'MarfookExpert'],
                      )
                    }
                    style={{
                      backgroundColor: userPermisions.includes('MarfookExpert')
                        ? '#11a6a1'
                        : '',
                      color: userPermisions.includes('MarfookExpert')
                        ? '#333'
                        : '#bbb',
                    }}
                  >
                    <PersonAdd sx={{ mr: '3px' }} />
                    کارشناس مرفوک
                  </Paper>
                  <Paper
                    elevation={2}
                    onClick={() =>
                      setUserPermisions(
                        userPermisions.includes('MarfookEvaluator')
                          ? userPermisions.filter(x => x !== 'MarfookEvaluator')
                          : [...userPermisions, 'MarfookEvaluator'],
                      )
                    }
                    style={{
                      backgroundColor: userPermisions.includes(
                        'MarfookEvaluator',
                      )
                        ? '#11a6a1'
                        : '',
                      color: userPermisions.includes('MarfookEvaluator')
                        ? '#333'
                        : '#bbb',
                    }}
                  >
                    <Quiz sx={{ mr: '3px' }} />
                    ارزیاب مرفوک
                  </Paper>
                  <Paper
                    elevation={2}
                    onClick={() =>
                      setUserPermisions(
                        userPermisions.includes('MarfookInstructionViewer')
                          ? userPermisions.filter(
                              x => x !== 'MarfookInstructionViewer',
                            )
                          : [...userPermisions, 'MarfookInstructionViewer'],
                      )
                    }
                    style={{
                      backgroundColor: userPermisions.includes(
                        'MarfookInstructionViewer',
                      )
                        ? '#11a6a1'
                        : '',
                      color: userPermisions.includes('MarfookInstructionViewer')
                        ? '#333'
                        : '#bbb',
                    }}
                  >
                    <Notifications sx={{ mr: '3px' }} />
                    تابلو اعلانات
                  </Paper>
                </Box>

                <Typography fontSize={16} sx={{ mt: 4 }}>
                  برای انتخاب عضو/اعضا جدید، نام یا شماره شخص مورد نظر را در
                  باکس زیر وارد نمایید:
                </Typography>
                <FormControl fullWidth sx={{ mt: 2, pb: 4 }}>
                  <Box sx={{ position: 'relative' }}>
                    <CustomInputBase
                      ref={userSelectorRef}
                      // value={selectedTexts.join(', ')}
                      onClick={handleUserSelectorToggle}
                      endAdornment={
                        <IconButton onClick={handleUserSelectorToggle}>
                          <ArrowDropDown />
                        </IconButton>
                      }
                      placeholder="انتخاب کاربران"
                      readOnly
                    />
                    <Popper
                      open={userSelectorOpen}
                      anchorEl={userSelectorRef.current}
                      placement="top-start"
                      style={{
                        width: userSelectorRef.current
                          ? userSelectorRef.current.clientWidth
                          : '100%',
                        zIndex: 9999,
                      }}
                    >
                      <ClickAwayListener onClickAway={handleUserSelectorClose}>
                        <Box
                          sx={{
                            border: 'none',
                            bgcolor: 'background.paper',
                            width: '100%',
                            borderRadius: '8px',
                            boxShadow: '0px 4px 20px 0px #0000001A',
                          }}
                        >
                          <CustomTabs
                            value={tabValue}
                            onChange={handleTabChange}
                            variant="fullWidth"
                          >
                            <CustomTab label="اعضا" />
                            <CustomTab label="ادمین‌ها" />
                          </CustomTabs>

                          <TabPanel
                            value={tabValue}
                            index={0}
                            style={{
                              minHeight: '300px',
                              maxHeight: '300px',
                              overflowY: 'scroll',
                            }}
                          >
                            <SearchInput
                              label="جستجو"
                              onChange={handleUserSearchChange}
                              sx={{
                                position: 'sticky',
                                top: '15px',
                                zIndex: '10',
                              }}
                            />
                            {userSearchText !== '' ? (
                              filteredUsers.length > 0 ? (
                                <>
                                  <MenuItem onClick={handleSelectAllUsers}>
                                    <Checkbox
                                      checked={
                                        selectedMemberToAdd.length ===
                                        filteredUsers.length
                                      }
                                    />
                                    <ListItemText primary="انتخاب همه" />
                                  </MenuItem>
                                  {filteredUsers.slice(0, 10).map(user => (
                                    <MenuItem
                                      key={user.id}
                                      onClick={() =>
                                        handleUserSelect(
                                          user.id,
                                          user.first_name
                                            ? `${user.first_name} ${user.last_name}`
                                            : user.username,
                                        )
                                      }
                                    >
                                      <Checkbox
                                        checked={selectedMemberToAdd.includes(
                                          user.id,
                                        )}
                                      />
                                      {user.avatar && (
                                        <Box sx={{ maxWidth: '40px', mr: 2 }}>
                                          <img
                                            alt="avatar"
                                            width="40px"
                                            height="40px"
                                            src={user.avatar}
                                            style={{ borderRadius: '100px' }}
                                          />
                                        </Box>
                                      )}
                                      <ListItemText
                                        primary={
                                          user.first_name
                                            ? `${user.first_name} ${user.last_name}`
                                            : user.username
                                        }
                                      />
                                    </MenuItem>
                                  ))}
                                </>
                              ) : (
                                <Box
                                  sx={{
                                    width: '100%',
                                    textAlign: 'center',
                                    py: 6,
                                  }}
                                >
                                  موردی یافت نشد
                                </Box>
                              )
                            ) : (
                              <Box
                                sx={{
                                  width: '100%',
                                  textAlign: 'center',
                                  py: 6,
                                }}
                              >
                                نام یا شماره شخص مورد نظر را جستجو کنید
                              </Box>
                            )}
                          </TabPanel>

                          <TabPanel
                            value={tabValue}
                            index={1}
                            style={{
                              minHeight: '300px',
                              maxHeight: '300px',
                              overflowY: 'scroll',
                            }}
                          >
                            <SearchInput
                              label="جستجو"
                              onChange={handleAdminSearchChange}
                              sx={{
                                position: 'sticky',
                                top: '15px',
                                zIndex: '10',
                              }}
                            />
                            {adminSearchText !== '' ? (
                              filteredAdmins.length > 0 ? (
                                <>
                                  <MenuItem onClick={handleSelectAllAdmins}>
                                    <Checkbox
                                      checked={
                                        selectedAdmins.filter(id =>
                                          filteredAdmins.some(
                                            admin => admin.id === id,
                                          ),
                                        ).length === filteredAdmins.length
                                      }
                                    />
                                    <ListItemText primary="انتخاب همه" />
                                  </MenuItem>
                                  {filteredAdmins.slice(0, 10).map(admin => (
                                    <MenuItem
                                      key={admin.id}
                                      onClick={() =>
                                        handleAdminSelect(
                                          admin.id,
                                          `${admin.first_name} ${admin.last_name}`,
                                        )
                                      }
                                    >
                                      <Checkbox
                                        checked={selectedAdmins.includes(
                                          admin.id,
                                        )}
                                      />
                                      {admin.avatar && (
                                        <Box sx={{ maxWidth: '40px', mr: 2 }}>
                                          <img
                                            alt="avatar"
                                            width="40px"
                                            height="40px"
                                            src={admin.avatar}
                                            style={{ borderRadius: '100px' }}
                                          />
                                        </Box>
                                      )}
                                      <ListItemText
                                        primary={`${admin.first_name} ${admin.last_name} (مدیر ${admin.organizationName})`}
                                      />
                                    </MenuItem>
                                  ))}
                                </>
                              ) : (
                                <Box
                                  sx={{
                                    width: '100%',
                                    textAlign: 'center',
                                    py: 6,
                                  }}
                                >
                                  موردی یافت نشد
                                </Box>
                              )
                            ) : admins.length > 0 ? (
                              <>
                                <MenuItem onClick={handleSelectAllAdmins}>
                                  <Checkbox
                                    checked={
                                      selectedAdmins.length === admins.length
                                    }
                                  />
                                  <ListItemText primary="انتخاب همه" />
                                </MenuItem>
                                {admins.map(admin => (
                                  <MenuItem
                                    key={admin.id}
                                    onClick={() =>
                                      handleAdminSelect(
                                        admin.id,
                                        `${admin.first_name} ${admin.last_name}`,
                                      )
                                    }
                                  >
                                    <Checkbox
                                      checked={selectedAdmins.includes(
                                        admin.id,
                                      )}
                                    />
                                    {admin.avatar && (
                                      <Box sx={{ maxWidth: '40px', mr: 2 }}>
                                        <img
                                          alt="avatar"
                                          width="40px"
                                          height="40px"
                                          src={admin.avatar}
                                          style={{ borderRadius: '100px' }}
                                        />
                                      </Box>
                                    )}
                                    <ListItemText
                                      primary={`${admin.first_name} ${admin.last_name} (مدیر ${admin.organizationName})`}
                                    />
                                  </MenuItem>
                                ))}
                              </>
                            ) : (
                              <Box
                                sx={{
                                  width: '100%',
                                  textAlign: 'center',
                                  py: 6,
                                }}
                              >
                                موردی یافت نشد
                              </Box>
                            )}
                          </TabPanel>
                        </Box>
                      </ClickAwayListener>
                    </Popper>
                  </Box>
                </FormControl>

                {/* Selected Items Table */}
                {(selectedMemberToAdd.length > 0 ||
                  selectedAdmins.length > 0) && (
                  <Box sx={{ mt: 3, mb: 3 }}>
                    <Typography
                      fontSize={16}
                      sx={{ mb: 2, fontWeight: 'bold' }}
                    >
                      موارد انتخاب شده:
                    </Typography>
                    <TableContainer component={Paper} sx={{ maxHeight: 200 }}>
                      <Table size="small">
                        <CTableHead>
                          <TableRow>
                            <TableCell>عکس</TableCell>
                            <TableCell>نام</TableCell>
                            <TableCell>نوع</TableCell>
                            <TableCell>حذف</TableCell>
                          </TableRow>
                        </CTableHead>
                        <TableBody>
                          {/* Admins */}
                          {selectedAdmins.map(adminId => {
                            const admin = admins.find(a => a.id === adminId);
                            if (!admin) return null;
                            return (
                              <CTableRow key={`admin-${admin.id}`}>
                                <TableCell sx={{ maxWidth: '40px' }}>
                                  <img
                                    width="30px"
                                    height="30px"
                                    alt="avatar"
                                    src={admin.avatar || '/logo.png'}
                                    style={{ borderRadius: '100px' }}
                                  />
                                </TableCell>
                                <TableCell>
                                  {`${admin.first_name} ${admin.last_name} (مدیر ${admin.organizationName})`}
                                </TableCell>
                                <TableCell>ادمین</TableCell>
                                <TableCell>
                                  <IconButton
                                    size="small"
                                    onClick={() => handleAdminSelect(admin.id)}
                                    sx={{ color: 'red' }}
                                  >
                                    <Delete fontSize="small" />
                                  </IconButton>
                                </TableCell>
                              </CTableRow>
                            );
                          })}
                          {/* Users */}
                          {selectedMemberToAdd.map(userId => {
                            const user = availableUsers.find(
                              u => u.id === userId,
                            );
                            if (!user) return null;
                            return (
                              <CTableRow key={`user-${user.id}`}>
                                <TableCell sx={{ maxWidth: '40px' }}>
                                  <img
                                    width="30px"
                                    height="30px"
                                    alt="avatar"
                                    src={user.avatar || '/logo.png'}
                                    style={{ borderRadius: '100px' }}
                                  />
                                </TableCell>
                                <TableCell>
                                  {user.first_name
                                    ? `${user.first_name} ${user.last_name}`
                                    : user.username}
                                </TableCell>
                                <TableCell>کاربر</TableCell>
                                <TableCell>
                                  <IconButton
                                    size="small"
                                    onClick={() => handleUserSelect(user.id)}
                                    sx={{ color: 'red' }}
                                  >
                                    <Delete fontSize="small" />
                                  </IconButton>
                                </TableCell>
                              </CTableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Box>
                )}

                <Typography
                  align="center"
                  fontSize={14}
                  sx={{ mb: '45px', mt: 5, color: 'red' }}
                >
                  {addMemberError}
                </Typography>
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: '0',
                    left: '0',
                    width: '100%',
                    px: 3,
                    py: 2,
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '10px',
                  }}
                >
                  <Button
                    variant="contained"
                    size="large"
                    type="submit"
                    sx={{ mt: 2, width: '100%' }}
                    onClick={handleAddMember}
                  >
                    تایید
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    type="button"
                    sx={{ mt: 2, width: '50%' }}
                    onClick={handleClose}
                  >
                    انصراف
                  </Button>
                </Box>
              </>
            )}
          </Stack>
        </Grid>
      </Modal>

      <Modal
        open={showPermisionModal}
        onClose={handleClosePermisionModal}
        aria-labelledby="child-modal-title"
        aria-describedby="child-modal-description"
      >
        <Grid
          container
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            pt: 2,
            px: 4,
            pb: 3,
            borderRadius: '8px',
            background: 'white',
            boxShadow: '0px 2px 20px 0px #00000012',
            width: '50%',
          }}
        >
          <Stack width="100%">
            <Typography
              align="center"
              fontWeight="bold"
              fontSize={18}
              sx={{ pb: 2, borderBottom: '1px solid #ccc' }}
            >
              ویرایش دسترسی کاربر مرفوک{' '}
            </Typography>
            <Typography fontSize={16} sx={{ mt: 4 }}>
              دسترسی کاربر را انتخاب نمایید:
            </Typography>
            <Box
              sx={{
                display: 'flex',
                flexWrap: 'wrap',
                alignItems: 'center',
                mt: 3,
                pb: 4,
                justifyContent: 'center',
                '& > :not(style)': {
                  display: 'flex',
                  m: 1,
                  width: 110,
                  height: 110,
                  cursor: 'pointer',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: 'none',
                  border: '1px solid #ccc',
                  color: '#bbb',
                  flexDirection: 'column',
                  fontSize: '14px',
                },
              }}
            >
              <Paper
                elevation={2}
                onClick={() =>
                  setUserPermisions(
                    userPermisions.includes('MarfookManager')
                      ? userPermisions.filter(x => x !== 'MarfookManager')
                      : [...userPermisions, 'MarfookManager'],
                  )
                }
                style={{
                  backgroundColor: userPermisions.includes('MarfookManager')
                    ? '#11a6a1'
                    : '',
                  color: userPermisions.includes('MarfookManager')
                    ? '#333'
                    : '#bbb',
                }}
              >
                <VerifiedUser sx={{ mr: '3px' }} />
                مدیر مرفوک
              </Paper>
              <Paper
                elevation={2}
                onClick={() =>
                  setUserPermisions(
                    userPermisions.includes('MarfookExpert')
                      ? userPermisions.filter(x => x !== 'MarfookExpert')
                      : [...userPermisions, 'MarfookExpert'],
                  )
                }
                style={{
                  backgroundColor: userPermisions.includes('MarfookExpert')
                    ? '#11a6a1'
                    : '',
                  color: userPermisions.includes('MarfookExpert')
                    ? '#333'
                    : '#bbb',
                }}
              >
                <PersonAdd sx={{ mr: '3px' }} />
                کارشناس مرفوک
              </Paper>
              <Paper
                elevation={2}
                onClick={() =>
                  setUserPermisions(
                    userPermisions.includes('MarfookEvaluator')
                      ? userPermisions.filter(x => x !== 'MarfookEvaluator')
                      : [...userPermisions, 'MarfookEvaluator'],
                  )
                }
                style={{
                  backgroundColor: userPermisions.includes('MarfookEvaluator')
                    ? '#11a6a1'
                    : '',
                  color: userPermisions.includes('MarfookEvaluator')
                    ? '#333'
                    : '#bbb',
                }}
              >
                <Quiz sx={{ mr: '3px' }} />
                ارزیاب مرفوک
              </Paper>
              <Paper
                elevation={2}
                onClick={() =>
                  setUserPermisions(
                    userPermisions.includes('MarfookInstructionViewer')
                      ? userPermisions.filter(
                          x => x !== 'MarfookInstructionViewer',
                        )
                      : [...userPermisions, 'MarfookInstructionViewer'],
                  )
                }
                style={{
                  backgroundColor: userPermisions.includes(
                    'MarfookInstructionViewer',
                  )
                    ? '#11a6a1'
                    : '',
                  color: userPermisions.includes('MarfookInstructionViewer')
                    ? '#333'
                    : '#bbb',
                }}
              >
                <Notifications sx={{ mr: '3px' }} />
                تابلو اعلانات
              </Paper>
            </Box>
            <Button
              onClick={() => submitPermisions()}
              variant="contained"
              sx={{ width: '50%', margin: '0 auto' }}
            >
              ثبت دسترسی
            </Button>
          </Stack>
        </Grid>
      </Modal>

      <BottomSheet
        title="حذف عضو"
        hideBottomSheet={() => setShowDeleteBottomSheet(false)}
        show={showDeleteBottomSheet}
      >
        <BottomSheetMessage>
          آیا می‌خواهید این عضو را از اعضای مرفوک حذف کنید؟
        </BottomSheetMessage>

        <Stack direction="row">
          <BottomSheetPrimaryButton onClick={() => deleteUser()}>
            بله
          </BottomSheetPrimaryButton>
          <BottomSheetSecondaryButton
            onClick={() => setShowDeleteBottomSheet(false)}
          >
            خیر
          </BottomSheetSecondaryButton>
        </Stack>
      </BottomSheet>
    </Grid>
  );
}

export default MembersSection;
