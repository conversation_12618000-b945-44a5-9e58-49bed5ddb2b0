import { Button, Paper, Typography } from '@mui/material';
import { Box } from '@mui/system';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useEffect, useState } from 'react';
import ButtonGroup from '@mui/material/ButtonGroup';
import CardTitle from './CardTitle';

export default function ContentCategoryChart() {
  const [chartOptions, setChartOptions] = useState({});

  // Mock data
  const mockData = {
    xAxisCategories: [
      '۱۴۰۳/۱۰/۰۱',
      '۱۴۰۳/۱۰/۰۲',
      '۱۴۰۳/۱۰/۰۳',
      '۱۴۰۳/۱۰/۰۴',
      '۱۴۰۳/۱۰/۰۵',
      '۱۴۰۳/۱۰/۰۶',
      '۱۴۰۱/۰۱/۰۷',
      '۱۴۰۱/۰۱/۰۸',
      '۱۴۰۱/۰۱/۰۹',
      '۱۴۰۱/۰۱/۱۰',
    ],
    seriesData: [
      {
        name: 'استوری (تصویری)',
        data: [5, 10, 15, 10, 20, 25, 30, 35, 40, 45],
        stack: 'محتوای تصویری',
        color: '#1C60B0',
      },
      {
        name: 'فتوتیتر (تصویری)',
        data: [10, 5, 8, 15, 10, 20, 25, 30, 35, 40],
        stack: 'محتوای تصویری',
        color: '#1E88E5',
      },
      {
        name: 'تصویرسازی (تصویری)',
        data: [7, 12, 10, 18, 14, 10, 20, 25, 30, 35],
        stack: 'محتوای تصویری',
        color: '#64B5F6',
      },
      {
        name: 'استوری (ویدیویی)',
        data: [6, 9, 12, 15, 20, 25, 30, 35, 40, 45],
        stack: 'محتوای ویدیویی',
        color: '#E91E63',
      },
      {
        name: 'موشن (ویدیویی)',
        data: [8, 7, 10, 12, 15, 20, 25, 30, 35, 40],
        stack: 'محتوای ویدیویی',
        color: '#F06292',
      },
      {
        name: 'ویدیو (ویدیویی)',
        data: [12, 10, 8, 15, 10, 25, 30, 35, 40, 45],
        stack: 'محتوای ویدیویی',
        color: '#F8BBD0',
      },
      {
        name: 'نماهنگ (ویدیویی)',
        data: [4, 6, 8, 10, 12, 15, 18, 20, 22, 24],
        stack: 'محتوای ویدیویی',
        color: '#C2185B',
      },
      {
        name: 'کلیپ (ویدیویی)',
        data: [5, 8, 10, 15, 18, 20, 25, 30, 35, 40],
        stack: 'محتوای ویدیویی',
        color: '#AD1457',
      },
      {
        name: 'پادکست (ویدیویی)',
        data: [3, 5, 7, 10, 12, 15, 18, 20, 22, 24],
        stack: 'محتوای ویدیویی',
        color: '#880E4F',
      },
      {
        name: 'پادکست (صوتی)',
        data: [7, 10, 12, 15, 20, 25, 30, 35, 40, 45],
        stack: 'محتوای صوتی',
        color: '#FFC107',
      },
    ],
  };

  useEffect(() => {
    setChartOptions({
      chart: {
        type: 'column',
        height: '400px',
        backgroundColor: '#fff',
      },
      title: {
        text: '',
      },
      legend: {
        enabled: true,
      },
      credits: {
        enabled: false,
      },
      xAxis: {
        categories: mockData.xAxisCategories,
        labels: {
          style: {
            fontFamily: 'IRANSansX, serif',
            fontSize: '12px',
          },
        },
      },
      yAxis: {
        min: 0,
        title: {
          text: 'تعداد',
          style: {
            fontFamily: 'IRANSansX, serif',
            fontSize: '14px',
          },
        },
      },
      tooltip: {
        shared: true,
        useHTML: true,
        style: {
          fontFamily: 'IRANSansX, serif',
          fontSize: '12px',
        },
        formatter: function () {
          return `<div style="text-align: right; font-family: 'IRANSansX', sans-serif; font-size: 12px;">
            <b>${this.x}</b><br/>
            ${this.points
              .map(
                point =>
                  ` ${point.series.name}: <b>${point.y}</b> <span style="color:${point.color}">●</span>`,
              )
              .join('<br/>')}
          </div>`;
        },
      },
      plotOptions: {
        column: {
          stacking: 'normal',
          borderWidth: 0,
        },
        series: {
          groupPadding: 0.1,
          borderWidth: 0,
        },
      },
      series: mockData.seriesData,
    });
  }, []);

  return (
    <Paper
      display="flex"
      justifyContent="center"
      alignItems="center"
      textAlign="center"
      flexDirection="column"
      elevation={2}
      sx={{ flexGrow: 1, width: '100%', padding: '20px' }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <CardTitle title="توزیع محتوای تولید شده" />
        <ButtonGroup variant="contained" aria-label="Basic button group">
          <Button size="small">روزانه</Button>
          <Button size="small">هفتگی</Button>
          <Button size="small">ماهانه</Button>
        </ButtonGroup>
      </Box>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} key={1} />
    </Paper>
  );
}
