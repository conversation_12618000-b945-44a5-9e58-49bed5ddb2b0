import { IconButton } from '@mui/material';
import LockOpenOutlinedIcon from '@mui/icons-material/LockOpenOutlined';
import { useState } from 'react';
import UnBlockUserBottomSheet from '../UnBlockUserBottomSheet/UnBlockUserBottomSheet';

export default function UnBlockUserButton({ chatId, userId }) {
  const [showBS, setShowBS] = useState(false);

  return (
    <>
      <IconButton onClick={() => setShowBS(true)}><LockOpenOutlinedIcon /></IconButton>
      <UnBlockUserBottomSheet
        show={showBS}
        hideBottomSheet={() => setShowBS(false)}
        chatId={chatId}
        userId={userId}
      />
    </>
  );
}
