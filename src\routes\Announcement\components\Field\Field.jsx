import {
  CircularProgress,
  IconButton,
  TextField,
} from '@mui/material';
import { useCallback, useRef, useState } from 'react';
import { useMutation } from 'react-query';
import { updateUserProfile } from 'apis/auth';
import IconSax from 'components/IconSax/IconSax';

export default function Field({
  userId,
  editable = true,
  title,
  name,
  value,
  sx,
  multiline = false,
}) {
  const [editMode, setEditMode] = useState(false);
  const initialValue = value;
  const inputRef = useRef();
  const saveRef = useRef();

  const mutation = useMutation(
    (event) => {
      event.preventDefault();

      const formData = new FormData();
      formData.append(name, inputRef.current.value);

      return updateUserProfile(userId, formData, true);
    },
    {
      onSettled: () => setEditMode(false),
      onError: () => {},
    },
  );

  const onFocus = useCallback(() => {
    setEditMode(true);
  }, []);

  const onBlur = useCallback((event) => {
    if (event.relatedTarget !== saveRef.current) {
      inputRef.current.value = initialValue;
      setEditMode(false);
    }
  }, []);

  return (
    <TextField
      label={title}
      defaultValue={initialValue}
      disabled={mutation.isLoading || !editable}
      multiline={multiline}
      variant="standard"
      sx={{ ...sx }}
      InputProps={{
        inputRef,
        onFocus,
        onBlur,

        endAdornment: (
          <IconButton
            onClick={mutation.mutate}
            ref={saveRef}
            type="submit"
            sx={{
              visibility:
                        editMode || mutation.isLoading ? 'visible' : 'hidden',
            }}
          >
            {mutation.isLoading ? (
              <CircularProgress size={20} />
            ) : (
              <IconSax
                name="tick-circle"
                sx={{ width: 20, height: 20, color: 'green' }}
              />
            )}
          </IconButton>
        ),
      }}
    />
  );
}
