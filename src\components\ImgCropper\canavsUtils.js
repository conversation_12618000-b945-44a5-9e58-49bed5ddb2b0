export const createImage = (url) => new Promise((resolve, reject) => {
  const image = new Image();
  image.addEventListener('load', () => resolve(image));
  image.addEventListener('error', (error) => reject(error));
  image.setAttribute('crossOrigin', 'anonymous'); // needed to avoid cross-origin issues on CodeSandbox
  image.src = url;
});

function createCanvas(width, height) {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  return canvas;
}

export function resizeCanvas(srcCanvas, dimensions) {
  const dstCanvas = createCanvas(dimensions.width, dimensions.height);
  const dstCtx = dstCanvas.getContext('2d');

  dstCtx.drawImage(srcCanvas, 0, 0, dimensions.width, dimensions.height);

  return dstCanvas;
}

export async function cropImage(imageURL, pixelCrop) {
  const image = await createImage(imageURL);

  const canvas = createCanvas(image.width, image.height);
  const ctx = canvas.getContext('2d');

  ctx.drawImage(image, 0, 0);
  const data = ctx.getImageData(
    pixelCrop.x,
    pixelCrop.y,
    pixelCrop.width,
    pixelCrop.height,
  );

  canvas.width = pixelCrop.width;
  canvas.height = pixelCrop.height;

  ctx.putImageData(data, 0, 0);

  return canvas;
}

export async function canvasToBlob(canvas) {
  return new Promise((resolve) => {
    canvas.toBlob((file) => {
      resolve(file);
    });
  });
}
