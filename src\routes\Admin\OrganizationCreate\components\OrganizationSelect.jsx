import { useState, useRef, useEffect } from 'react';
import {
  ListItemText,
  <PERSON>per,
  Tabs,
  Tab,
  Box,
  ClickAwayListener,
  IconButton,
  InputBase,
  MenuItem,
} from '@mui/material';
import { ArrowDropDown } from '@mui/icons-material';
import { styled } from '@mui/system';
import { useQuery } from 'react-query';
import SearchInput from '../../AnnouncementView/components/ViewersSection/components/SearchInput';
import { getOrganizations } from '../../../../apis/organization';

const CustomInputBase = styled(InputBase)(() => ({
  border: '1px solid rgba(0,0,0,0.25)',
  borderRadius: 6,
  padding: '6px 8px',
  width: '100%',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
}));
styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  '& .MuiTabs-indicator': {
    backgroundColor: '#009688',
  },
}));
styled(Tab)(({ theme }) => ({
  minWidth: 72,
  textTransform: 'none',
  fontWeight: theme.typography.fontWeightRegular,
  marginRight: 0,
  color: 'rgba(0, 0, 0, 0.85)',
  '&.Mui-selected': {
    color: '#009688',
    fontWeight: theme.typography.fontWeightMedium,
  },
  '&.Mui-focusVisible': {
    backgroundColor: 'rgba(100, 95, 228, 0.32)',
  },
}));

function OrganizationSelect({
  selectedOrganization = '', setSelectedOrganization,
}) {
  const [open, setOpen] = useState(false);
  const anchorRef = useRef(null);

  const [organizations, setOrganizations] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [filteredData, setFilteredData] = useState([]);

  useEffect(() => {
    const filtered = organizations.filter(item => (`${item.name?.toLowerCase()}`).includes(searchText.toLowerCase()));
    setFilteredData(filtered);
  }, [searchText, organizations]);

  useQuery({
    queryKey: ['OrganizationKey'],
    queryFn: () => getOrganizations(),
    onSuccess: (data) => {
      if (data.status === 200) {
        setOrganizations(data.data);
      }
    },
  });

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  const handleChange = (event, value) => {
    setSelectedOrganization(value);
    handleClose(event);
  };

  const handleSearchChange = (e) => {
    setSearchText(e.target.value);
  };

  return (
    <>
      <CustomInputBase
        ref={anchorRef}
        value={selectedOrganization?.value}
        onClick={handleToggle}
        endAdornment={(
          <IconButton onClick={handleToggle}>
            <ArrowDropDown />
          </IconButton>
        )}
        placeholder="انتخاب سازمان"
        readOnly
      />
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        placement="bottom-start"
        style={{ width: anchorRef.current ? anchorRef.current.clientWidth : undefined }}
      >
        <ClickAwayListener onClickAway={handleClose}>
          <Box sx={{
            border: 'none',
            bgcolor: 'background.paper',
            width: '100%',
            borderRadius: '8px',
            boxShadow: '0px 4px 20px 0px #0000001A',
            // p: 1,
          }}
          >

            <Box style={{ maxHeight: '250px', overflowY: 'scroll', padding: '10px' }}>
              <SearchInput label="جستجو" onChange={handleSearchChange} sx={{ position: 'sticky', top: '0px', zIndex: '10' }} />
              {filteredData.length > 0 ? (
                <Box style={{ marginTop: '10px' }}>
                  {filteredData.map((option) => (
                    <MenuItem
                      value={option.id}
                      onClick={(event) => {
                        handleChange(event, { id: option.id, value: option.name });
                      }}
                    >
                      <ListItemText primary={option.name} />
                    </MenuItem>
                  ))}
                </Box>
              ) : <Box sx={{ width: '100%', textAlign: 'center', py: 6 }}>موردی یافت نشد</Box>}

            </Box>
          </Box>
        </ClickAwayListener>
      </Popper>
    </>
  );
}

export default OrganizationSelect;
