import { Stack } from '@mui/material';
import CustomAppBar from 'components/CustomAppBar/CustomAppBar';
import BackButton from 'components/CustomAppBar/components/BackButton/BackButton';
import SubTitle from 'components/CustomAppBar/components/SubTitle/SubTitle';
import ContactSearchBox from 'components/ContactSearchBox/ContactSearchBox';
// import SearchBox from '../SearchBox/SearchBox';

export default function MyAppBar({ onSearchChange }) {
  return (
    <CustomAppBar>
      <Stack sx={{ mb: 2, width: '100%' }}>
        <Stack direction="row" sx={{ mt: 1, mb: 1 }}>
          <BackButton />
          <SubTitle text="پیام جدید" />
        </Stack>

        <ContactSearchBox onSearchChange={onSearchChange} />
      </Stack>
    </CustomAppBar>

  );
}
