import { Stack, Typography, styled } from '@mui/material';
import dayjs from 'dayjs';
import DeliverStatus from '../DeliverStatus/DeliverStatus';

const MessageTime = styled(Typography)({
  fontSize: '10px',
  fontWeight: 400,
  color: '#898989',
});

export default function MessageMeta({ sent, deliveryStatus, createdAt }) {
  const yesterday = dayjs().subtract(1, 'day').startOf('day');
  const isBeforeYesterday = dayjs(createdAt).isBefore(dayjs(yesterday));
  let messageTime = '';
  if (isBeforeYesterday) messageTime = dayjs(createdAt).format('HH:mm  YYYY-MM-DD');
  else messageTime = dayjs(createdAt).fromNow();
  return (
    <Stack
      direction={sent ? 'row' : 'row-reverse'}
      sx={{ pl: 2, pr: 2, pb: 1 }}
      spacing={2}
      justifyContent="space-between"
    >
      {sent && <DeliverStatus status={deliveryStatus} />}

      <MessageTime>{messageTime}</MessageTime>
    </Stack>
  );
}
