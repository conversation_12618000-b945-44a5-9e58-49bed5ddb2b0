import { Stack } from '@mui/material';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';

export default function DeleteBottomSheet({
  show,
  hideBottomSheet,
  onPrimary,
}) {
  return (
    <BottomSheet
      title="حذف محتوا"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        آیا می‌خواهید این محتوا را حذف کنید؟
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            onPrimary();
            hideBottomSheet();
          }}
        >
          بله
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={hideBottomSheet}>
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
