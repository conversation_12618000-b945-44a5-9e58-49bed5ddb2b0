import { Button, Paper } from '@mui/material';
import { Box } from '@mui/system';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useEffect, useState } from 'react';
import ButtonGroup from '@mui/material/ButtonGroup';
import CardTitle from './CardTitle';

export default function ContentProcessChart() {
  const [chartOptions, setChartOptions] = useState({});

  // Mock Data
  const mockData = {
    xAxisCategories: [
      '۱۴۰۳/۱۰/۰۱',
      '۱۴۰۳/۱۰/۰۲',
      '۱۴۰۳/۱۰/۰۳',
      '۱۴۰۳/۱۰/۰۴',
      '۱۴۰۳/۱۰/۰۵',
      '۱۴۰۳/۱۰/۰۶',
    ],
    seriesData: {
      name: 'محتوا',
      data: [15, 25, 35, 20, 30, 40],
      color: '#1C60B0',
    },
  };

  useEffect(() => {
    setChartOptions({
      chart: {
        type: 'line',
        height: '300px',
        backgroundColor: '#fff',
        style: {
          borderRadius: '8px',
        },
      },
      title: {
        text: '',
      },
      legend: {
        enabled: false,
      },
      credits: {
        enabled: false,
      },
      xAxis: {
        categories: mockData.xAxisCategories,
        labels: {
          style: {
            fontFamily: 'IranSansX, serif',
            fontSize: '12px',
          },
        },
      },
      yAxis: {
        min: 0,
        title: {
          text: 'تعداد',
          style: {
            fontFamily: 'IranSansX, serif',
            fontSize: '14px',
          },
        },
      },
      tooltip: {
        shared: true,
        useHTML: true,
        style: {
          fontFamily: 'IranSansX, serif',
          fontSize: '12px',
        },
        formatter: function () {
          return `<div style="text-align: right; font-family: 'IranSansX'; font-size: 12px;">
            <b>${this.x}</b><br/>
            <span style="color:${this.color}">●</span> ${this.series.name}: <b>${this.y}</b>
          </div>`;
        },
      },
      plotOptions: {
        line: {
          marker: {
            enabled: true,
          },
        },
        series: {
          enableMouseTracking: true,
        },
      },
      series: [mockData.seriesData],
    });
  }, []);

  return (
    <Paper
      display="flex"
      justifyContent="center"
      alignItems="center"
      textAlign="center"
      flexDirection="column"
      elevation={2}
      sx={{ flexGrow: 1, width: '100%', padding: '20px' }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <CardTitle title="روند انتشار محتوا" />
        <ButtonGroup variant="contained" aria-label="Basic button group">
          <Button size="small">روزانه</Button>
          <Button size="small">هفتگی</Button>
          <Button size="small">ماهانه</Button>
        </ButtonGroup>
      </Box>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} key={1} />
    </Paper>
  );
}
