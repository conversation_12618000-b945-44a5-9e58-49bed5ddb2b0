import { Button, Stack, Typography, styled } from '@mui/material';
import { createIntentUrl, getToken } from 'utils';
import Contact from '../Contact/Contact';

const Title = styled(Typography)(() => ({
  fontSize: '14px',
  fontWeight: 400,
  color: '#2C2C2E',
  marginTop: 'auto',
  marginBottom: 'auto',
}));

const SyncButton = styled(Button)(() => ({
  fontSize: '14px',
}));

function isContactSelected(selectedContacts, contact) {
  return selectedContacts.find(c => c.id === contact.id);
}

export default function Contacts({
  sx,
  contacts = [],
  selectedContacts = [],
  onContactClick,
  selectable,
  searchValue = '',
}) {
  const filteredContacts = contacts?.filter(contact =>
    `${contact.user_first_name} ${contact.user_last_name}`.includes(
      searchValue,
    ),
  );

  const intentURL = createIntentUrl('sync_contact', 'sync_contact', {
    authorizationToken: getToken(),
  });

  return (
    <Stack sx={{ ...sx, height: '100%' }} spacing={2}>
      <Stack direction="row" justifyContent="space-between">
        <Title align="right">مرتب شده بر اساس آخرین زمان بازدید</Title>
        <SyncButton href={intentURL}>همگام سازی</SyncButton>
      </Stack>

      {filteredContacts.map(contact => (
        <Contact
          key={contact.id}
          contact={contact}
          onClick={onContactClick}
          selected={isContactSelected(selectedContacts, contact)}
          selectable={selectable}
        />
      ))}
    </Stack>
  );
}
