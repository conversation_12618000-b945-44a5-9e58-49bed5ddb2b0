import { Collapse, Grid, Stack } from '@mui/material';
import { useState } from 'react';
import { Form } from 'react-router-dom';
import { SEARCH_PARAMS } from './searchConfig';
import SearchInput from './components/SearchInput/SearchInput';
import DropBottom from './components/DropBottom/DropBottom';
import AdvancedSearch from './components/AdvancedSearch/AdvancedSearch';
import Order from './components/Order/Order';
import ViewMode from './components/ViewMode/ViewMode';

export default function SearchBoxMarfook({ sx, searchConfig, setFilter }) {
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [showOrder, setShowOrder] = useState(false);
  const [showViewMode, setShowViewMode] = useState(false);

  return (
    <Form onSubmit={() => setShowAdvancedSearch(false)}>
      <Grid
        container
        justifyContent="center"
        alignItems="center"
        columnSpacing={3}
        sx={sx}
      >
        <Grid item xs={12} lg={8}>
          <SearchInput
            defaultValue={searchConfig.search}
            filter={searchConfig}
            setFilter={setFilter}
          />
        </Grid>

        <Grid item xs={12} lg={6}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{ height: '100%' }}
          >
            <DropBottom
              label="جست‌وجو پیشرفته"
              onClick={() =>
                setShowAdvancedSearch(prev => {
                  setShowOrder(false);
                  setShowViewMode(false);
                  return !prev;
                })
              }
              expanded={showAdvancedSearch}
            />
            <DropBottom
              label="ترتیب"
              onClick={() =>
                setShowOrder(prev => {
                  setShowAdvancedSearch(false);
                  setShowViewMode(false);
                  return !prev;
                })
              }
              expanded={showOrder}
            />
            {/* <DropBottom
              label="نمایش"
              onClick={() =>
                setShowViewMode(prev => {
                  setShowAdvancedSearch(false);
                  setShowOrder(false);
                  return !prev;
                })
              }
              expanded={showViewMode}
            /> */}
          </Stack>
        </Grid>

        <Grid xs={12} lg={8} sx={{ pl: 3 }}>
          <Collapse in={showAdvancedSearch}>
            <AdvancedSearch searchConfig={searchConfig} setFilter={setFilter} />
          </Collapse>

          <Collapse in={showOrder}>
            <Order searchConfig={searchConfig} setFilter={setFilter} />
          </Collapse>
        </Grid>
      </Grid>
    </Form>
  );
}
