import { Box } from '@mui/material';
import { useCallback } from 'react';
import CTable from '../../../components/CTable/CTable';
import { getGroupMsgs } from '../../../apis/groupMsg';

export default function GroupMsgList() {
  const colTitles = [
    'متن پیام',
    'تاریخ ارسال',
    'تعداد بازدید',
    'روش ارسال',
    // 'اقدامات',
  ];

  const normalizer = useCallback(data => {
    const modifiedData = [];
    for (const i in data.results) {
      const item = data?.results?.[i] || {};
      modifiedData.push({
        id: item.id,
        cols: [
          { type: 'text', value: item.body },
          { type: 'date', value: item.created_at },
          { type: 'text', value: '---' },
          {
            type: 'text',
            value:
              item.use_sms && item.use_chat
                ? 'پیامک/پیام‌رسان'
                : item.use_sms
                  ? 'پیامک'
                  : 'پیام‌رسان',
          },
          // { type: 'action', actions: item.use_chat ? ['view'] : [] },
        ],
      });
    }
    return modifiedData;
  });

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        overflowY: 'scroll',
        mt: 2,
      }}
    >
      <CTable
        normalizer={normalizer}
        fetchData={getGroupMsgs}
        colTitles={colTitles}
        basePath="admin-panel/group-msg"
      />
    </Box>
  );
}
