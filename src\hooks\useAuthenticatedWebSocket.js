import { WS_URL } from 'constants';
import { useSelector } from 'react-redux';
import { useWebSocket } from 'react-use-websocket/dist/lib/use-websocket';
import { selectMe } from 'store/auth';
import { getToken } from 'utils';

export default function useAuthenticatedWebsocket() {
  // Let's depend this hook on me.
  useSelector(selectMe);

  const token = getToken();
  const wsUrl = `${WS_URL}?token=${token}`;
  return useWebSocket(wsUrl, { share: true, shouldReconnect: () => true });
}
