import { FormControlLabel, Radio, RadioGroup } from '@mui/material';
import { SEARCH_PARAMS } from 'components/SearchBox/searchConfig';
import { CONTENT_VIEW_MODES } from 'constants';

export default function ViewMode({ viewMode }) {
  return (
    <RadioGroup
      value={viewMode}
      sx={{ mt: 2 }}
      name={SEARCH_PARAMS.VIEW_MODE}
      row
    >
      {CONTENT_VIEW_MODES.map((view) => (
        <FormControlLabel
          key={view.key}
          control={(
            <Radio
              onClick={() => localStorage.setItem(SEARCH_PARAMS.VIEW_MODE, view.key)}
              inputProps={{ type: 'submit' }}
            />
)}
          label={view.value}
          value={view.key}
          sx={{
            mt: 1,
            mb: 1,
          }}
        />
      ))}
    </RadioGroup>
  );
}
