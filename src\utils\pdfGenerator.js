import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

// Helper function to format date to Jalali
const formatToJalali = (date) => {
  if (!date) return '';
  try {
    return new Date(date).toLocaleDateString('fa-IR');
  } catch (error) {
    return date;
  }
};

export const generateInstructionPDF = async (instruction) => {
  // Create a temporary div to render the instruction content
  const tempDiv = document.createElement('div');
  tempDiv.style.position = 'absolute';
  tempDiv.style.left = '-9999px';
  tempDiv.style.top = '-9999px';
  tempDiv.style.width = '800px';
  tempDiv.style.padding = '40px';
  tempDiv.style.backgroundColor = 'white';
  tempDiv.style.fontFamily = 'Arial, sans-serif';
  tempDiv.style.fontSize = '14px';
  tempDiv.style.lineHeight = '1.6';
  tempDiv.style.color = '#333';

  // Build the HTML content
  let htmlContent = `
    <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #11a6a1; padding-bottom: 20px;">
      <h1 style="color: #11a6a1; margin: 0; font-size: 24px;">دستورالعمل مرفوک</h1>
      <p style="margin: 10px 0 0 0; color: #666; font-size: 12px;">تاریخ تولید: ${new Date().toLocaleDateString('fa-IR')}</p>
    </div>

    <div style="margin-bottom: 25px;">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
        <span style="background: #11a6a1; color: white; padding: 5px 15px; border-radius: 15px; font-size: 12px;">شماره: ${instruction.code}</span>
      </div>
      <h2 style="color: #333; margin: 0 0 15px 0; font-size: 20px; font-weight: bold;">${instruction.title}</h2>
    </div>
  `;

  if (instruction.description) {
    htmlContent += `
      <div style="margin-bottom: 25px;">
        <h3 style="color: #11a6a1; margin: 0 0 10px 0; font-size: 16px; border-bottom: 1px solid #ddd; padding-bottom: 5px;">توضیحات</h3>
        <p style="margin: 0; text-align: justify;">${instruction.description}</p>
      </div>
    `;
  }

  // Add dates and info
  htmlContent += `
    <div style="margin-bottom: 25px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
      <h3 style="color: #11a6a1; margin: 0 0 15px 0; font-size: 16px;">اطلاعات دستورالعمل</h3>
      <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
        <div>
          <strong>تاریخ ثبت:</strong><br>
          <span>${formatToJalali(instruction.created_at)}</span>
        </div>
        <div>
          <strong>تاریخ انقضا:</strong><br>
          <span>${formatToJalali(instruction.expiration_date)}</span>
        </div>
        <div>
          <strong>تعداد محورها:</strong><br>
          <span>${instruction.subject?.length || 0} محور</span>
        </div>
      </div>
    </div>
  `;

  // Add hashtags if available
  if (instruction.hashtags && instruction.hashtags.length > 0) {
    htmlContent += `
      <div style="margin-bottom: 25px;">
        <h3 style="color: #11a6a1; margin: 0 0 10px 0; font-size: 16px; border-bottom: 1px solid #ddd; padding-bottom: 5px;">هشتگ‌ها</h3>
        <div style="display: flex; flex-wrap: wrap; gap: 8px;">
    `;
    
    instruction.hashtags.forEach(hashtag => {
      htmlContent += `<span style="background: #e3f2fd; color: #1976d2; padding: 4px 12px; border-radius: 12px; font-size: 12px; border: 1px solid #1976d2;">${hashtag}</span>`;
    });
    
    htmlContent += `
        </div>
      </div>
    `;
  }

  // Add subjects/axes
  if (instruction.subject && instruction.subject.length > 0) {
    htmlContent += `
      <div style="margin-bottom: 25px;">
        <h3 style="color: #11a6a1; margin: 0 0 15px 0; font-size: 16px; border-bottom: 1px solid #ddd; padding-bottom: 5px;">محورها</h3>
    `;

    instruction.subject.forEach((subj, index) => {
      htmlContent += `
        <div style="margin-bottom: 15px; background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #11a6a1;">
          <h4 style="margin: 0 0 10px 0; color: #333; font-size: 14px; font-weight: bold;">${index + 1}. ${subj.title}</h4>
      `;

      if (subj.description) {
        htmlContent += `<p style="margin: 0 0 10px 0; color: #666; font-size: 13px; text-align: justify;">${subj.description}</p>`;
      }

      if (subj.hashtags && subj.hashtags.length > 0) {
        htmlContent += `<div style="display: flex; flex-wrap: wrap; gap: 6px;">`;
        subj.hashtags.forEach(hashtag => {
          htmlContent += `<span style="background: #e3f2fd; color: #1976d2; padding: 2px 8px; border-radius: 10px; font-size: 11px; border: 1px solid #1976d2;">${hashtag}</span>`;
        });
        htmlContent += `</div>`;
      }

      htmlContent += `</div>`;
    });

    htmlContent += `</div>`;
  }

  tempDiv.innerHTML = htmlContent;
  document.body.appendChild(tempDiv);

  try {
    // Generate canvas from the HTML content
    const canvas = await html2canvas(tempDiv, {
      backgroundColor: '#ffffff',
      useCORS: true,
      scale: 2,
      width: 800,
      height: tempDiv.scrollHeight,
    });

    // Create PDF
    const pdf = new jsPDF('p', 'mm', 'a4');
    const imgData = canvas.toDataURL('image/png');
    
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();
    const imgWidth = pdfWidth - 20; // 10mm margin on each side
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    
    let heightLeft = imgHeight;
    let position = 10; // 10mm top margin

    // Add first page
    pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
    heightLeft -= (pdfHeight - 20); // Subtract margins

    // Add additional pages if needed
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight + 10;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
      heightLeft -= (pdfHeight - 20);
    }

    // Save the PDF
    pdf.save(`دستورالعمل-${instruction.code}.pdf`);

    return true;
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  } finally {
    // Clean up
    document.body.removeChild(tempDiv);
  }
};
