import Slider from 'react-slick';

import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

import { Box, Stack, styled } from '@mui/material';
import { useRef } from 'react';
import { useQuery } from 'react-query';
import MyLink from 'components/MyLink/MyLink';
import { getNotifications } from '../../apis/notification';

const MyImg = styled('img')(({ theme }) => ({
  borderRadius: 2 * theme.shape.borderRadius,
  maxHeight: '320px',
  width: '100%',
}));

const DOT_SIZE = 10;

function Circle({ disabled, onClick }) {
  return (
    <Box
      onClick={onClick}
      sx={theme => ({
        borderRadius: '50%',
        backgroundColor: disabled
          ? theme.palette.grey['300']
          : theme.palette.primary.main,
        width: DOT_SIZE,
        height: DOT_SIZE,
      })}
    />
  );
}

function calcNavigatorWidth(dotsLen) {
  // pr + dotsWidth + dotsSpacing + pl
  return 8 + dotsLen * DOT_SIZE + (dotsLen - 1) * 8 + 8;
}

function Navigator({ dots, slider }) {
  return (
    <Stack
      sx={{
        height: '14px',
        width: calcNavigatorWidth(dots.length),

        position: 'relative',
        bottom: '32px',
        pr: 1,
        pl: 1,
        margin: 'auto',

        backgroundColor: 'white',
        borderRadius: '8px',

        justifyContent: 'space-evenly',
      }}
      direction="row"
      alignItems="center"
    >
      {dots.map((dot, index) => (
        <Circle
          disabled={!dot.props.className}
          key={dot.key}
          onClick={() => {
            slider.current.slickGoTo(index, true);
          }}
        />
      ))}
    </Stack>
  );
}

export default function ImageSlider({ sx }) {
  const slider = useRef(null);

  const setting = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    appendDots: dots => <Navigator dots={dots} slider={slider} />,
    initialSlide: 0,
    arrows: false,
  };

  const { isLoading, data } = useQuery('notifications', getNotifications);
  const images = isLoading ? [] : data?.data?.results || [];

  return (
    <Box sx={sx}>
      <Slider {...setting} ref={slider}>
        {[...images].reverse().map(img => (
          <MyLink
            to={img.url}
            key={img.banner}
            sx={{ cursor: img.url ? 'pointer' : 'auto' }}
          >
            <Box key={img.banner}>
              <MyImg src={img.banner} />
            </Box>
          </MyLink>
        ))}
      </Slider>
    </Box>
  );
}
