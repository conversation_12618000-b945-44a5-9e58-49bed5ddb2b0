import { Paper } from '@mui/material';
import { Box } from '@mui/system';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useEffect, useState } from 'react';
import CardTitle from './CardTitle';

export default function ContentCategoryDonutChart() {
  const [chartOptions, setChartOptions] = useState({});

  // Mock Data for Donut Chart
  const mockData = [
    { name: 'سیاسی', y: 15, color: '#1C60B0' },
    { name: 'مقاومت', y: 10, color: '#38CAB3' },
    { name: 'فرهنگی', y: 20, color: '#E052B8' },
    { name: 'انقلاب اسلامی', y: 10, color: '#FFC107' },
    { name: 'اجتماعی', y: 15, color: '#FF5722' },
    { name: 'طنز و سرگرمی', y: 10, color: '#64B5F6' },
    { name: 'آموزشی', y: 5, color: '#C2185B' },
    { name: 'فناوری', y: 10, color: '#F06292' },
    { name: 'مناسبت', y: 5, color: '#AD1457' },
  ];

  useEffect(() => {
    setChartOptions({
      chart: {
        type: 'pie',
        height: '400px',
        backgroundColor: '#fff',
      },
      title: {
        text: '',
      },
      legend: {
        align: 'right',
        verticalAlign: 'middle',
        layout: 'vertical',
        itemStyle: {
          fontFamily: 'IranSansX, serif',
          fontSize: '16px',
        },
      },
      credits: {
        enabled: false,
      },
      tooltip: {
        useHTML: true,
        style: {
          fontFamily: 'IranSansX, serif',
          fontSize: '16px',
        },
        formatter: function () {
          return `<div style="text-align: right; font-family: 'IranSansX'; font-size: 12px;">
            <b>٪${this.point.name}</b>: <b>${this.point.y}</b>
          </div>`;
        },
      },
      plotOptions: {
        pie: {
          innerSize: '50%', // Makes the pie chart a donut
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: false,
          },
          showInLegend: true,
        },
      },
      series: [
        {
          name: 'دسته‌بندی محتوا',
          colorByPoint: true,
          data: mockData,
        },
      ],
    });
  }, []);

  return (
    <Paper
      display="flex"
      justifyContent="space-between"
      alignItems="center"
      flexDirection="row"
      elevation={2}
      sx={{ flexGrow: 1, width: '100%', padding: '20px' }}
    >
      <Box sx={{ flex: 1 }}>
        <CardTitle title="دسته‌بندی موضوعی محتوا" />
        <HighchartsReact highcharts={Highcharts} options={chartOptions} />
      </Box>
    </Paper>
  );
}
