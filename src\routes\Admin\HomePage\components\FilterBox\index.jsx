import {
  But<PERSON>,
  Card,
  CircularProgress,
  Stack,
  Typography,
} from '@mui/material';
import { Box } from '@mui/system';
import CardTitle from '../CardTitle';
import OrganFilter from './OrganFilter';
import DateFilter from './DateFilter';

export default function FilterBox({ loading = false }) {
  return (
    <Card
      display="flex"
      justifyContent="center"
      alignItems="center"
      textAlign="center"
      flexDirection="column"
      elevation={2}
      sx={{ flexGrow: 1, width: '100%', p: 2 }}
    >
      <CardTitle title="فیلتر‌ها" />
      <Stack display="flex" justifyContent="center" direction="row" gap={2}>
        <OrganFilter />
        <DateFilter />
      </Stack>
      <Box sx={{ pt: 2, textAlign: 'right' }}>
        <Button
          variant="contained"
          size="small"
          type="submit"
          sx={{ width: '300px' }}
        >
          {!loading ? (
            <Typography>اعمال فیلتر</Typography>
          ) : (
            <CircularProgress sx={{ color: 'white' }} size={24} />
          )}
        </Button>
      </Box>
    </Card>
  );
}
