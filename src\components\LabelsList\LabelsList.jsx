import { Grid, Typography } from '@mui/material';
import MyLink from 'components/MyLink/MyLink';
import { PATHS } from 'constants';

export default function LabelsList({ labels }) {
  return (
    <Grid container gap={1}>
      {labels.map((label) => (
        <MyLink
          to={`${PATHS.search}?search=${label}&labels=on`}
          sx={{ backgroundColor: '#E7EAF4', borderRadius: '24px' }}
        >
          <Typography
            sx={{
              fontSize: '14px', cursor: 'pointer', p: 1, pr: 2, pl: 2,
            }}
            key={label}
          >
            {`#   ${label}`}
          </Typography>
        </MyLink>
      ))}
    </Grid>
  );
}
