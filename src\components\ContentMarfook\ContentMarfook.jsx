import { PATHS } from 'constants';
import TileContent from './components/TileContent/TileContent';

export default function ContentMarfook({
  withTypeIcon,
  content,
  setOpen,
  setSelectedContent,
}) {
  // const contentLink = PATHS.content.replace(':contentId', content.id);

  return (
    <TileContent
      content={content}
      withTypeIcon={withTypeIcon}
      setOpen={setOpen}
      setSelectedContent={setSelectedContent}
      // link={contentLink}
    />
  );
}
