import { Stack } from '@mui/material';
import {
  createAudioMessage,
  createFileMessage, createTextMessage,
} from 'dtos/messenger';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import EmojiPicker, { EmojiStyle } from 'emoji-picker-react';
import {
  progressMessageUpload, selectSelectedMessage, unsetSelectedMessage,
} from 'store/messenger';
import uploadFile from 'apis/storage';
import { isAudio, isImage, isVideo } from 'utils';
import useSendMessage from 'hooks/useSendMessage';
import { SELECT_MESSAGE_REASONS } from 'constants';
import useEditMessage from 'hooks/useEditMessage';
import { useQuery } from 'react-query';
import { GET_ME_URL, getMe } from 'apis/auth';
import SelectFileButton from './components/SelectFileButton/SelectFileButton';
import SelectEmojiButton from './components/SelectEmojiButton/SelectEmojiButton';
import SendMessageButton from './components/SendMessageButton/SendMessageButton';
import SendImagePreview from './components/MediaCaptionModal/MediaCaptionModal';
import SelectedMessagePreview from './components/SelectedMessagePreview/SelectedMessagePreview';
import MyInputBase from './components/MyInputBase/MyInputBase';
import MyCropper from './components/MyCropper/MyCropper';

function genInputBoxDefaultValue(selectedMessage) {
  if (selectedMessage && selectSelectedMessage.reason === SELECT_MESSAGE_REASONS.EDIT) {
    return selectedMessage.message.text;
  }
  return '';
}

function isEditing(selectedMessage) {
  return selectedMessage?.reason === SELECT_MESSAGE_REASONS.EDIT;
}

export default function ChatInputBox({ chatId, addMessage }) {
  const selectedMessage = useSelector(selectSelectedMessage(chatId));
  const inputRef = useRef(null);

  const [messageText, setMessageText] = useState(genInputBoxDefaultValue(selectedMessage));
  const [selectedMedia, setSelectedMedia] = useState(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  const { data: meData } = useQuery(GET_ME_URL, getMe);
  const me = meData.data;

  const dispatch = useDispatch();
  const sendMessageToServer = useSendMessage();

  useEffect(() => {
    if (selectedMessage?.reason === SELECT_MESSAGE_REASONS.EDIT) {
      setMessageText(selectedMessage.message.text);
    }
  }, [selectedMessage]);

  const sendTextMessage = () => {
    const message = createTextMessage(
      me,
      chatId,
      messageText,
      selectedMessage?.message,
    );
    addMessage(message);
    sendMessageToServer(message);
    dispatch(unsetSelectedMessage({ chatId }));
  };

  const editMessage = useEditMessage();
  const onSendMessageClick = () => {
    if (isEditing(selectedMessage)) {
      editMessage(selectedMessage.message.deliveryToken, messageText);
      dispatch(unsetSelectedMessage({ chatId }));
    } else {
      sendTextMessage();
    }

    setMessageText('');
    inputRef.current.focus();
  };

  const sendFileMessage = async (file) => {
    const message = createFileMessage(me, chatId, file, selectedMessage?.message);
    addMessage(message);
    const uploadedFile = await uploadFile(file, (progress) => {
      dispatch(progressMessageUpload({
        deliveryToken: message.deliveryToken,
        uploadProgress: progress,
      }));
    });
    sendMessageToServer(message, uploadedFile.data.id);
    dispatch(unsetSelectedMessage({ chatId }));
  };

  const sendAudioMessage = async (file) => {
    const message = createAudioMessage(me, chatId, file);
    addMessage(message);
    const uploadedFile = await uploadFile(file, (progress) => {
      dispatch(progressMessageUpload({
        deliveryToken: message.deliveryToken,
        uploadProgress: progress,
      }));
    });
    sendMessageToServer(message, uploadedFile.data.id);
  };

  const [toBeCroppedImage, setToBeCroppedImage] = useState(null);
  const onSelectFiles = async (files) => {
    if (isImage(files[0].type)) {
      setToBeCroppedImage(files[0]);
    } else if (isVideo(files[0].type)) {
      setSelectedMedia(files[0]);
    } else if (isAudio(files[0].type)) {
      sendAudioMessage(files[0]);
    } else {
      sendFileMessage(files[0]);
    }
  };

  const closeModal = () => { setSelectedMedia(null); };

  const insertEmoji = (emoji) => {
    setMessageText((prev) => prev + emoji.emoji);
  };

  return (
    <Stack direction="column" sx={{ borderTop: '1px solid #ccc' }}>
      <SelectedMessagePreview selectedMessage={selectedMessage} />

      <Stack
        sx={{
          background: 'white',
          left: 0,
          right: 0,
          bottom: 0,
          p: 1,
          pl: 2,
        }}
        direction="row"
        justifyContent="space-between"
        alignItems="center"
      >
        {messageText.trim()
          ? <SendMessageButton onSendMessageClick={onSendMessageClick} disabled={!messageText} />
          : <SelectFileButton onSelectFiles={onSelectFiles} />}

        <MyInputBase
          value={messageText}
          setValue={setMessageText}
          ref={inputRef}
          onFocus={() => setShowEmojiPicker(false)}
        />

        <SelectEmojiButton onClick={() => setShowEmojiPicker(!showEmojiPicker)} />

      </Stack>

      {selectedMedia && (
        <SendImagePreview
          media={selectedMedia}
          closeModal={closeModal}
          me={me}
          chatId={chatId}
          addMessage={addMessage}
        />
      )}

      {toBeCroppedImage && (
        <MyCropper
          src={URL.createObjectURL(toBeCroppedImage)}
          setCroppedImage={(img) => { setSelectedMedia(img); }}
          hideCropper={() => setToBeCroppedImage(null)}
        />
      )}

      {showEmojiPicker && (
        <EmojiPicker
          onEmojiClick={insertEmoji}
          autoFocusSearch={false}
          emojiStyle={EmojiStyle.NATIVE}
          width="100"
          height={300}
          previewConfig={{
            showPreview: false,
          }}
          skinTonesDisabled
          searchDisabled
        />
      )}
    </Stack>
  );
}
