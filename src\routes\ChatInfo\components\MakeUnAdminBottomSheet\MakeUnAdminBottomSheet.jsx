import { Stack } from '@mui/material';
import { removeAdmin } from 'apis/messenger';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { setSnackbar } from 'store/layout';

export default function MakeUnAdminBottomSheet({
  show,
  hideBottomSheet,
  userId,
  chatId,
}) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  function onRemoveAdmin() {
    removeAdmin({ chatId, admin: userId }).then(() => {
      dispatch(setSnackbar({
        message: 'کاربر با موفقیت از لیست مدیران حذف شد.',
        severity: 'success',
      }));

      navigate(-1);
    });
  }

  return (
    <BottomSheet
      title="حذف کاربر از لیست مدیران"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        آیا تمایل به حذف کاربر از لیست مدیران دارید؟
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            onRemoveAdmin();
            hideBottomSheet();
          }}
        >
          بله
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={hideBottomSheet}>
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
