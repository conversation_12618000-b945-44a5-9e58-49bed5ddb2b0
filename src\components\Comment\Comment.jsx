import {
  Avatar, Paper, Stack, Typography,
} from '@mui/material';

export default function Comment() {
  return (
    <Stack direction="row">
      <Avatar>AF</Avatar>
      <Paper
        sx={(theme) => ({
          padding: theme.spacing(2),
          paddingTop: theme.spacing(1),
          paddingBottom: theme.spacing(1),
          marginLeft: theme.spacing(2),
          border: `1px solid ${theme.palette.grey.A200}`,
        })}
        elevation={0}
        variant="body2"
      >
        <Typography variant="body2">
          طراحی شما بسیار بسیار زیبا بود
        </Typography>
      </Paper>
    </Stack>
  );
}
