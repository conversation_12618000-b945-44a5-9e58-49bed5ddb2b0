import { Button, Paper } from '@mui/material';
import { Box } from '@mui/system';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useEffect, useState } from 'react';
import ButtonGroup from '@mui/material/ButtonGroup';
import CardTitle from './CardTitle';

export default function EngagementChart() {
  const [chartOptions, setChartOptions] = useState({});

  // Mock Data with Overlapping Lines
  const mockData = {
    xAxisCategories: [
      '۱۴۰۱/۰۱/۰۱',
      '۱۴۰۱/۰۱/۰۲',
      '۱۴۰۱/۰۱/۰۳',
      '۱۴۰۱/۰۱/۰۴',
      '۱۴۰۱/۰۱/۰۵',
      '۱۴۰۱/۰۱/۰۶',
    ],
    seriesData: [
      {
        name: 'تعداد لایک',
        data: [50, 100, 150, 120, 200, 250],
        color: '#1C60B0',
      },
      {
        name: 'تعداد بازدید',
        data: [100, 80, 180, 140, 240, 220],
        color: '#38CAB3',
      },
      {
        name: 'تعداد دانلود',
        data: [150, 130, 100, 200, 170, 180],
        color: '#E052B8',
      },
      {
        name: 'تعداد اشتراک گذاری',
        data: [60, 120, 80, 160, 140, 200],
        color: '#FFC107',
      },
      {
        name: 'مجموع حمایت از پست',
        data: [200, 150, 250, 220, 300, 260],
        color: '#FF5722',
      },
    ],
  };

  useEffect(() => {
    setChartOptions({
      chart: {
        type: 'spline', // Spline for smooth curves
        height: '400px',
        backgroundColor: '#fff',
        style: {
          borderRadius: '8px',
        },
      },
      title: {
        text: '',
      },
      legend: {
        enabled: true,
        itemStyle: {
          fontFamily: 'IranSansX, serif',
          fontSize: '12px',
        },
      },
      credits: {
        enabled: false,
      },
      xAxis: {
        categories: mockData.xAxisCategories,
        labels: {
          style: {
            fontFamily: 'IranSansX, serif',
            fontSize: '12px',
          },
        },
      },
      yAxis: {
        min: 0,
        title: {
          text: 'تعداد',
          style: {
            fontFamily: 'IranSansX, serif',
            fontSize: '14px',
          },
        },
      },
      tooltip: {
        shared: true,
        useHTML: true,
        style: {
          fontFamily: 'IranSansX, serif',
          fontSize: '12px',
        },
        formatter: function () {
          return `<div style="text-align: right; font-family: 'IranSansX'; font-size: 12px;">
            <b>${this.x}</b><br/>
            ${this.points
              .map(
                point =>
                  `<span style="color:${point.color}">●</span> ${point.series.name}: <b>${point.y}</b>`,
              )
              .join('<br/>')}
          </div>`;
        },
      },
      plotOptions: {
        spline: {
          marker: {
            enabled: true,
            radius: 4,
          },
        },
        series: {
          enableMouseTracking: true,
        },
      },
      series: mockData.seriesData,
    });
  }, []);

  return (
    <Paper
      display="flex"
      justifyContent="center"
      alignItems="center"
      textAlign="center"
      flexDirection="column"
      elevation={2}
      sx={{ flexGrow: 1, width: '100%', padding: '20px' }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <CardTitle title="نمودار تعاملات پست‌ها" />
        <ButtonGroup variant="contained" aria-label="Basic button group">
          <Button size="small">روزانه</Button>
          <Button size="small">هفتگی</Button>
          <Button size="small">ماهانه</Button>
        </ButtonGroup>
      </Box>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} key={1} />
    </Paper>
  );
}
