import { Typography } from '@mui/material';
import { buildFullName } from 'utils';

export default function ForwardedFrom({ forwardedFrom, onClick }) {
  const forwardedFromName = (forwardedFrom.firstName || forwardedFrom.lastName)
    ? buildFullName(forwardedFrom.firstName, forwardedFrom.lastName)
    : forwardedFrom.displayUsername;

  return (
    <Typography sx={{ fontSize: '12px', p: 1, fontStyle: 'italic' }}>
      باز ارسال از
      {' '}
      <Typography
        sx={{
          cursor: 'pointer',
          fontSize: 'inherit',
          fontStyle: 'inherit',
          color: '#3390ec',
        }}
        component="span"
        onClick={onClick}
      >
        {forwardedFromName}
      </Typography>
    </Typography>
  );
}
