import { Box } from '@mui/material';
import { useCallback, useState } from 'react';
import CTable from '../../../components/CTable/CTable';
import {
  getAnnouncements,
  deleteAnnouncement,
} from '../../../apis/announcement';

export default function AnnouncementList() {
  const colTitles = [
    'شماره اعلان',
    'عنوان اعلان',
    'تاریخ ارسال',
    'آخرین تغییر',
    'تعداد بازدید',
    'وضعیت',
    'اقدامات',
  ];

  const normalizer = useCallback(data => {
    const modifiedData = [];
    for (const i in data.results) {
      if (!data.results[i].deleted) {
        modifiedData.push({
          id: data.results[i].id,
          cols: [
            { type: 'text', value: data.results[i].id },
            { type: 'text', value: data.results[i].title },
            { type: 'date', value: data.results[i].created_at },
            { type: 'date', value: data.results[i].updated_at },
            { type: 'text', value: '---' },
            {
              type: 'status',
              value: data.results[i].published ? 'منتشر شده' : 'منتشر نشده',
              color: data.results[i].published ? 'green' : 'red',
            },
            { type: 'action', actions: ['view', 'delete'] },
          ],
        });
      }
    }
    return modifiedData;
  });

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        overflowY: 'scroll',
        mt: 2,
      }}
    >
      <CTable
        normalizer={normalizer}
        fetchData={getAnnouncements}
        deleteData={deleteAnnouncement}
        colTitles={colTitles}
        basePath="admin-panel/announcement"
      />
    </Box>
  );
}
