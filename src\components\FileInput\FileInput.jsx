/* eslint-disable no-param-reassign */
/* eslint-disable react/destructuring-assignment */
import { styled } from '@mui/material';
import { forwardRef, useEffect } from 'react';

const HiddenInput = styled('input')(() => ({
  display: 'none',
}));

function FileInput({ onSelectFiles, accept = 'image/*' }, ref) {
  useEffect(() => {
    ref.current.addEventListener('change', (e) => {
      onSelectFiles(e.target.files);
      ref.current.value = null;
    });
  }, []);

  return <HiddenInput ref={ref} type="file" accept={accept} />;
}

export default forwardRef(FileInput);
