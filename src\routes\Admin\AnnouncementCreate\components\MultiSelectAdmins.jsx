import { useState } from 'react';
import { useQuery } from 'react-query';
import { getOrganizations } from '../../../../apis/organization';
import MultiSelectDropdown from '../../../../components/UI/MultiSelectDrowDown';

function MultiSelectAdmins({ selectedAdmins = [], setSelectedAdmins }) {
  const [options, setOptions] = useState([]);
  let admins = [];

  const removeDuplicates = arr => {
    const seenIds = [];
    return arr.filter(item => {
      if (!seenIds.includes(item.id)) {
        seenIds.push(item.id);
        return true;
      }
      return false;
    });
  };

  const transformData = data => {
    data.map(item => {
      admins.push(
        ...item.admins?.map(x => ({
          id: x.id,
          first_name: x.first_name,
          last_name: x.last_name,
          avatar: x.avatar || '',
          organization: item.name,
        })),
      );
    });
    admins = removeDuplicates(admins);
    return admins.map(item => ({
      id: item.id,
      label: `${item.first_name} ${item.last_name} (مدیر ${item.organization})`,
      avatar: item.avatar || '/logo.png',
    }));
  };

  const { isLoading } = useQuery({
    queryKey: ['OrganizationKey'],
    queryFn: () => getOrganizations(),
    onSuccess: data => {
      if (data.status === 200) {
        setOptions(transformData(data.data));
      }
    },
  });

  return (
    <MultiSelectDropdown
      defaultOptions={selectedAdmins}
      handleSelect={setSelectedAdmins}
      options={options}
      placeholder="انتخاب مدیر/مدیر ها"
      loading={isLoading}
    />
  );
}

export default MultiSelectAdmins;
