import Field from '../Field/Field';
import Subtitle from '../Subtitle/Subtitle';
import Container from '../Container/Container';

export default function UserInfo({
  sx,
  userId,
  firstName,
  lastName,
  displayUsername,
  username,
  bio,
}) {
  return (
    <Container spacing={3} sx={sx}>
      <Subtitle value="اطلاعات کاربری" />
      <Field
        userId={userId}
        title="نام"
        name="first_name"
        value={firstName}
      />
      <Field
        userId={userId}
        title="نام خانوادگی"
        name="last_name"
        value={lastName}
      />
      <Field
        userId={userId}
        title="نام کاربری"
        name="display_username"
        value={displayUsername}
      />
      <Field
        userId={userId}
        title="بیوگرافی"
        name="bio"
        value={bio}
        multiline
      />
      <Field
        userId={userId}
        title="موبایل"
        name="username"
        value={`0${username}`}
        editable={false}
      />
    </Container>
  );
}
