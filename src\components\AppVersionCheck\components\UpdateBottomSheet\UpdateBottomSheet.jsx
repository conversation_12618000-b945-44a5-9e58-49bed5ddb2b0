import { Stack } from '@mui/material';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import useDownloader from 'react-use-downloader';

export default function UpdateAvailableBottomSheet({
  show,
  hideBottomSheet,
  downloadLink,
}) {
  const { download, isInProgress, percentage } = useDownloader();

  return (
    <BottomSheet
      title="به روز رسانی جدید"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        نسخه‌ی اپلیکیشن جدید در دسترس است. لطفا برای کارایی بهتر برنامه را به
        روزرسانی کنید.
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            download(downloadLink, 'tam-app.apk');
          }}
        >
          دانلود
          {isInProgress ? ` (${percentage} %)` : ''}
        </BottomSheetPrimaryButton>

        <BottomSheetSecondaryButton onClick={hideBottomSheet}>الان نه</BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
