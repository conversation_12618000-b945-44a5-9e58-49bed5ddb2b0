import {
  Box,
  Fade, IconButton, SvgIcon, Typography,
} from '@mui/material';
import { useState } from 'react';
import { addInteraction, deleteInteraction } from 'apis/content';
import { useQueryClient } from 'react-query';
import { setPathParam } from 'utils';
import { PATHS, LIKE, FAVORITE } from 'constants';
import { useSelector } from 'react-redux';
import { selectMe } from 'store/auth';
import { useNavigate } from 'react-router-dom';
import LoginBottomSheet from '../LoginBottomSheet/LoginBottomSheet';
import { DISLIKE } from 'constants';

export default function InteractionButton({
  text,
  icon,
  filledIcon,
  type,
  interaction,
  contentId,
  authRequired,
}) {
  const [state, setState] = useState(!!interaction);
  const [loading, setLoading] = useState(false);
  const [textCopy, setTextCopy] = useState(text);
  const color = state ? 'red' : '#64676A';
  const queryClient = useQueryClient();

  const me = useSelector(selectMe);
  const [showLoginBS, setShowLoginBS] = useState(false);

  async function toggleStateAtServer() {
    if (state) {
      await deleteInteraction({ contentId, interaction: type });
    } else {
      await addInteraction({ contentId, interaction: type });
    }
  }

  async function toggleState() {
    if (authRequired && !me) {
      setShowLoginBS(true);
    } else if (!loading) {
      setLoading(true);

      if (!state) setTextCopy(Number(textCopy) + 1);
      else setTextCopy(Number(textCopy) - 1);
      setState((prev) => !prev);
      await toggleStateAtServer();
      queryClient.invalidateQueries({
        queryKey: setPathParam(PATHS.content, 'contentId', contentId),
      });

      setLoading(false);
    }
  }

  const genBottomSheetMsg = () => {
    let msg = '';
    if (type === LIKE) {
      msg = 'برای لایک کردن محتوا، لطفا وارد شوید.';
    } else if (type === DISLIKE) {
      msg = 'برای دیسلایک کردن محتوا لطفا وارد شوید.';
    } else if (type === FAVORITE) {
      msg = 'برای نشان کردن محتوا لطفا وارد شوید.';
    }
    return msg;
  };

  const startIcon = state ? (
    <Fade in>
      <SvgIcon component={filledIcon} sx={{ fill: 'none' }} />
    </Fade>
  ) : (
    <SvgIcon component={icon} sx={{ fill: 'none' }} />
  );

  const navigate = useNavigate();
  const onInteractionsClick = () => {
    let interactionsPath = setPathParam(PATHS.contentInteractions, 'contentId', contentId);
    interactionsPath = setPathParam(interactionsPath, 'interactionType', type);
    navigate(interactionsPath);
  };

  return (
    <>
      <Box>
        <IconButton
          onClick={toggleState}
          sx={{ color }}
          disabled={loading}
          edge="end"
        >
          {startIcon}
        </IconButton>

        <IconButton onClick={onInteractionsClick}>
          <Typography>{textCopy}</Typography>
        </IconButton>
      </Box>

      <LoginBottomSheet
        show={showLoginBS}
        hideBottomSheet={() => setShowLoginBS(false)}
        message={genBottomSheetMsg()}
      />
    </>
  );
}
