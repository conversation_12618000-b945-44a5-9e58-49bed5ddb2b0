import { useCallback, useState } from 'react';
import useListenOnChatMessages from './useListenOnChatMessages';
import useGetChatMessages from './useGetChatMessages';

function genAddMessageFunction(setMessages) {
  return (message) => setMessages((prevState) => {
    const newState = new Map(prevState);
    const { deliveryToken } = message;
    newState.set(deliveryToken, message);
    return newState;
  });
}

function genUpdateMessageDeliveryStatusFunction(setMessages) {
  return (message) => setMessages((prevState) => {
    const { deliveryStatus, deliveryToken } = message;
    const newState = new Map(prevState);
    if (prevState.has(deliveryToken)) {
      newState.set(deliveryToken, { ...prevState.get(deliveryToken), deliveryStatus });
    }
    return newState;
  });
}

function genDeleteMessageFunction(setMessages) {
  return (message) => setMessages((prevState) => {
    const { deliveryToken } = message;
    if (prevState.delete(deliveryToken)) {
      return new Map(prevState);
    }
    return prevState;
  });
}

function genConcatMessagesFunction(setMessages) {
  return (messages) => setMessages((prevState) => {
    const newMessages = new Map();
    messages.toReversed().forEach((message) => {
      if (!prevState.has(message.deliveryToken)) {
        newMessages.set(message.deliveryToken, message);
      }
    });
    return new Map([...newMessages, ...prevState]);
  });
}

export default function useChatMessages(chatId) {
  const [messages, setMessages] = useState(new Map()); // {deliveryToken: {message}}

  const addMessage = useCallback(
    genAddMessageFunction(setMessages),
    [setMessages],
  );
  const updateMessageDeliveryStatus = useCallback(
    genUpdateMessageDeliveryStatusFunction(setMessages),
    [setMessages],
  );
  const deleteMessage = useCallback(
    genDeleteMessageFunction(setMessages),
    [setMessages],
  );
  const concatMessages = useCallback(
    genConcatMessagesFunction(setMessages),
    [setMessages],
  );

  useListenOnChatMessages(chatId, addMessage, updateMessageDeliveryStatus, deleteMessage);
  const { fetchNextPage, hastNextPage } = useGetChatMessages(chatId, concatMessages);

  return {
    messages,
    addMessage,
    fetchNextPage,
    hastNextPage,
  };
}
