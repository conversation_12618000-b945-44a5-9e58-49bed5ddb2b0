import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import { PATHS } from 'constants';
import { useNavigate } from 'react-router-dom';

export default function LoginBottomSheet({ hideBottomSheet, show, message }) {
  const navigate = useNavigate();
  const onLoginClick = () => {
    navigate(PATHS.enter_mobile);
  };

  return (
    <BottomSheet
      title="ورود به برنامه"
      hideBottomSheet={hideBottomSheet}
      onOutSideClick={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        {message}
      </BottomSheetMessage>
      <BottomSheetPrimaryButton onClick={onLoginClick}>ورود</BottomSheetPrimaryButton>
    </BottomSheet>
  );
}
