import { Box, Tab, Tabs } from '@mui/material';
import TabLabel from './components/TabLabel/TabLabel';

export default function MyTabs({
  tabs, selectedTab, setSelectedTab, variant, centered,
}) {
  return (
    <Box sx={{ width: '100%' }}>
      <Tabs
        variant={variant}
        scrollButtons="auto"
        value={selectedTab}
        onChange={(e, newValue) => setSelectedTab(newValue)}
        centered={centered}
      >
        {tabs.map((tab) => (
          <Tab
            key={tab.value}
            label={<TabLabel icon={tab.icon} label={tab.label} count={tab.count} />}
            value={tab.value}
          />
        ))}
      </Tabs>
    </Box>
  );
}
