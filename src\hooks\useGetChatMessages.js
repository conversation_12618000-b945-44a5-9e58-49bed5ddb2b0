import { getChatMessages } from 'apis/messenger';
import { Author, Message } from 'dtos/messenger';
import { useCallback, useEffect, useState } from 'react';
import { useQuery } from 'react-query';

function parseReplyTo(replyTo) {
  if (!replyTo) return null;

  const author = new Author(
    replyTo.author.id,
    replyTo.author.avatar,
    replyTo.author.first_name,
    replyTo.author.last_name,
    replyTo.author.display_username,
  );
  return new Message(
    author,
    replyTo.chat,
    replyTo.text,
    replyTo.type,
    null,
    replyTo.delivery_token,
    null,
    null,
    null,
    replyTo.file,
    null,
    null,
  );
}

function parseMessage(message) {
  /** Convert message received from service to Message dto. */
  const author = new Author(
    message.author.id,
    message.author.avatar,
    message.author.first_name,
    message.author.last_name,
    message.author.display_username,
  );

  let forwardedFrom = null;
  if (message.forwarded_from) {
    forwardedFrom = new Author(
      message.forwarded_from.id,
      message.forwarded_from.avatar,
      message.forwarded_from.first_name,
      message.forwarded_from.last_name,
      message.forwarded_from.display_username,
    );
  }

  return new Message(
    author,
    message.chat,
    message.text,
    message.type,
    message.delivery_status,
    message.delivery_token,
    message.created_at,
    message.sent_by_me,
    null,
    message.file,
    forwardedFrom,
    parseReplyTo(message.reply_to),
    message.display_text,
    message.extra_data,
  );
}

export default function useGetChatMessages(chatId, concatMessages) {
  const [page, setPage] = useState(0);
  const [hastNextPage, setHasNextPage] = useState(true);

  const { isLoading, data, error } = useQuery(
    ['get-chat-messages', page, chatId],
    () => getChatMessages({ chatId, page }),
    {
      cacheTime: 0,
      enabled: page !== 0, // do not auto fetch on mount
      retry: (failureCount, error) => error.response.status !== 404,
    },
  );

  useEffect(() => {
    if (error?.response?.status === 404) {
      setHasNextPage(false);
    }
  }, [error]);

  useEffect(() => {
    const messages = data?.data?.results;
    if (!isLoading && messages) {
      const parsedMessages = messages.map((msg) => parseMessage(msg));
      concatMessages(parsedMessages);

      if (data.data.next === null) {
        setHasNextPage(false);
      }
    }
  }, [data?.data, isLoading, concatMessages]);

  const fetchNextPage = useCallback(
    () => {
      if (hastNextPage) setPage((prevState) => prevState + 1);
    },
    [setPage, hastNextPage],
  );

  return { fetchNextPage, hastNextPage };
}
