import {
  Box,
  Button, IconButton, Stack,
} from '@mui/material';
import {
  useCallback, useRef, useState,
} from 'react';
import ReactCrop, { convertToPixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import { canvasToBlob, cropImage } from './canavsUtils';
import 'react-mobile-cropper/dist/style.css';

function CloseButton({ onClick }) {
  return (
    <IconButton
      sx={{
        position: 'fixed',
        zIndex: 1600,
        top: 24,
        right: 24,
        color: 'white',
        size: 'large',
      }}
      onClick={onClick}
    >
      <HighlightOffIcon size="large" />
    </IconButton>
  );
}

export default function MyCropper({ src, setCroppedImage, hideCropper }) {
  const [crop, setCrop] = useState({
    unit: '%', x: 0, y: 0, width: '100', height: '100',
  });
  const imageRef = useRef();

  const onConfirm = useCallback(async () => {
    const canvas = await cropImage(
      imageRef.current,
      convertToPixelCrop(
        crop,
        imageRef.current.width,
        imageRef.current.height,
      ),
    );
    const resizedImage = await canvasToBlob(canvas);
    setCroppedImage(resizedImage);
    hideCropper();
  }, [crop]);

  return (
    <>
      <CloseButton onClick={hideCropper} />

      <Stack
        sx={{
          position: 'fixed',
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: 1500,
          background: 'rgba(0,0,0,0.5)',
          p: 2,
        }}
        justifyContent="center"
      >
        <Stack sx={{
          flexGrow: 1,
          justifyContent: 'center',
          height: '90%',
        }}
        >
          <Box sx={{ margin: 'auto', height: '100%' }}>

            <ReactCrop crop={crop} keepSelection onChange={(c) => setCrop(c)}>
              <img src={src} alt="upload" ref={imageRef} />
            </ReactCrop>
          </Box>
        </Stack>

        <Stack direction="row" spacing={2}>
          <Button
            variant="contained"
            size="large"
            onClick={onConfirm}
            sx={{
              flexGrow: 2,
            }}
          >
            تایید

          </Button>
        </Stack>
      </Stack>
    </>
  );
}
