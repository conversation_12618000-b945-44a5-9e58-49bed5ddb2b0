import { Box, Stack } from '@mui/material';
import { DEFAULT_APPBAR_HEIGHT } from 'components/CustomAppBar/constants';
import { useLoaderData, useNavigate } from 'react-router-dom';
import { addParticipant, getChatParticipants } from 'apis/messenger';
import { useQuery } from 'react-query';
import { getContacts } from 'apis/contacts';
import { useEffect, useState } from 'react';
import Contact from 'components/Contact/Contact';
import { useDispatch } from 'react-redux';
import { setSnackbar } from 'store/layout';
import MyAppBar from './components/MyAppBar/MyAppBar';

export async function loader({ params }) {
  return params.chatId;
}

export default function AddParticipant() {
  const chatId = useLoaderData();

  const { isLoading: participantsLoading, data: participantsData } = useQuery(
    ['participants', chatId],
    () => getChatParticipants({ chatId }),
    { initialData: { data: [] } },
  );

  const { isLoading: contactsLoading, data: contactsData } = useQuery(
    'contacts',
    getContacts,
  );

  const [addableContacts, setAddableContacts] = useState([]);
  useEffect(() => {
    if (participantsLoading || contactsLoading) {
      return;
    }

    const contacts = contactsData.data?.results;
    const participantIds = participantsData.data.map(p => p.user_id);
    const addableContacts = contacts.filter(
      contact => !participantIds.includes(contact.user.id),
    );
    setAddableContacts(addableContacts);
  }, [participantsData, contactsData]);

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const onContactClick = userId => {
    navigate(-1);
    addParticipant({ chatId, participant: userId }).then(() => {
      dispatch(
        setSnackbar({
          message: 'مخاطب به گفت‌وگو اضافه شد.',
          severity: 'success',
        }),
      );
    });
  };

  return (
    <>
      <MyAppBar />
      <Stack
        sx={{ pt: DEFAULT_APPBAR_HEIGHT, height: '100%' }}
        alignItems="start"
        spacing={1}
      >
        {addableContacts.map(contact => (
          <Contact
            key={contact.id}
            contact={contact}
            onClick={() => onContactClick(contact.user.id)}
            EndButton={<Box />}
          />
        ))}
      </Stack>
    </>
  );
}
