import { createSelector, createSlice } from '@reduxjs/toolkit';
import dayjs from 'dayjs';

export const messengerSlice = createSlice({
    name: 'messenger',
    initialState: {
        chatIds: [],
        chats: {},
        chatMessages: {},
        messages: {},
        chatParticipants: {},
        users: {},
        selectedContacts: [],
        selectedMessage: {},
        contacts: {},  // userId : {contact}
        hasUnreadMessage: false,
    },
    reducers: {
        setChatIds: (state, action) => {
            state.chatIds = action.payload
        },

        setChat: (state, action) => {
            state.chats[action.payload.id] = action.payload
        },

        setChatMessages: (state, action) => {
            state.chatMessages[action.payload.chatId] = action.payload.messages
        },

        setMessage: (state, action) => {
            state.messages[action.payload.deliveryToken] = action.payload
        },

        addMessage: (state, action) => {
            const message = action.payload;
            state.messages[message.deliveryToken] = message

            if (!state.chatMessages[message.chatId]) {
                state.chatMessages[message.chatId] = []
            }

            if (!state.chatMessages[message.chatId].includes(message.deliveryToken)) {
                state.chatMessages[message.chatId].unshift(message.deliveryToken)
            }
        },

        addMessages: (state, action) => {
            const messages = action.payload
            const firstMessage = messages[0]
            if (!state.chatMessages[firstMessage.chatId]) {
                state.chatMessages[firstMessage.chatId] = []
            }

            messages.forEach(message => {
                if (!state.messages[message.deliveryToken]) {
                    state.messages[message.deliveryToken] = message
                    state.chatMessages[firstMessage.chatId].push(message.deliveryToken)
                }
            })
        },

        updateMessageDeliveryStatus: (state, action) => {
            const { deliveryStatus, deliveryToken } = action.payload
            state.messages[deliveryToken] = {
                ...state.messages[deliveryToken],
                deliveryStatus,
            }
        },

        progressMessageUpload: (state, action) => {
            const { uploadProgress, deliveryToken } = action.payload
            const message = state.messages[deliveryToken]
            state.messages[deliveryToken] = { ...message, uploadProgress }
        },

        setSelectedContacts: (state, action) => {
            state.selectedContacts = action.payload
        },

        setSelectedMessage: (state, action) => {
            const { chatId, message, reason } = action.payload
            state.selectedMessage[chatId] = { message, reason }
        },

        unsetSelectedMessage: (state, action) => {
            const { chatId } = action.payload
            delete state.selectedMessage[chatId]
        },

        addContact: (state, action) => {
            const { id, user, userFirstName, userLastName } = action.payload;
            state.contacts[user] = {
                id,
                userFirstName,
                userLastName,
            }
        },

        setHasUnreadMessage: (state, action) => {
            const { hasUnreadMessage } = action.payload
            state.hasUnreadMessage = hasUnreadMessage
        }
    },
});

export const {
    setChatIds,
    setChat,
    setChatMessages,
    setMessage,
    setChatBrief,
    addMessage,
    addMessages,
    setSelectedContacts,
    progressMessageUpload,
    deleteMessage,
    updateMessageDeliveryStatus,
    setSelectedMessage,
    unsetSelectedMessage,
    addContact,
    setHasUnreadMessage,
} = messengerSlice.actions;

export const selectChats = state => {
    return state.messenger.chats;
};

export const selectSortedChatsList = state => {
    const chatsDict = state.messenger.chats;
    const chatsList = Object.keys(chatsDict).map(key => chatsDict[key]);

    return chatsList.sort((a, b) => {
        return (
            dayjs(b.messages.at(0).createdAt) -
            dayjs(a.messages.at(0).createdAt)
        );
    });
};

export const selectSelectedContacts = state => state.messenger.selectedContacts

/** new selectors */
export const selectChatIds = (state) => state.messenger.chatIds
export const selectChat = (chatId) => (state) => state.messenger.chats[chatId]
export const selectChatMessages = (chatId) => (state) => state.messenger.chatMessages[chatId]
export const selectChatDisplayAvatar = (chatId) => state => state.messenger.chats[chatId]?.displayAvatar
export const selectChatDisplayName = (chatId) => state => state.messenger.chats[chatId]?.displayName
export const selectChatType = (chatId) => state => state.messenger.chats[chatId]?.type
export const selectChatCreator = (chatId) => state => state.messenger.chats[chatId]?.creator
export const selectChatParticipants = (chatId) => state => state.messenger.chats[chatId]?.participants
export const selectChatDataExists = (chatId) => state => !!state.messenger.chats[chatId]
export const selectMessageUploadProgress = (deliveryToken) => state => state.messenger.messages[deliveryToken]?.uploadProgress

export const selectMessage = (deliveryToken) => state => state.messenger.messages[deliveryToken]
export const selectSelectedMessage = (chatId) => state => state.messenger.selectedMessage[chatId]
export const selectContacts = (state) => state.messenger.contacts
export const selectContactWithUser = (userId) => state => state.messenger.contacts[userId]

export const makeSelectMessage = () => createSelector(
    (state) => state.messenger.messages,
    (_, deliveryToken) => deliveryToken,
    (messages, deliveryToken) => {
        return messages[deliveryToken]
    }
)

export const selectHasUnreadMessage = (state) => state.messenger.hasUnreadMessage

export default messengerSlice.reducer;
