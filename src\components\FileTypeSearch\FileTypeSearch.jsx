import { Button, Grid } from '@mui/material';

export default function FileTypeSearch({ type }) {
  return (
    <Grid xs={5} item container justifyContent="center">
      <Button
        component="div"
        align="center"
        color="inherit"
        sx={(theme) => ({
          backgroundColor: theme.palette.grey.A200,
          borderRadius: theme.shape.borderRadius / 2,
          width: '100%',
          height: '100px',
          lineHeight: '100px',
        })}
      >
        {type}
      </Button>
    </Grid>
  );
}
