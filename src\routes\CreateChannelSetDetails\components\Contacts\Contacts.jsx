import { Stack, Typography } from '@mui/material';
import Contact from 'components/Contact/Contact';

export default function Contacts({ contacts }) {
  return (
    <Stack spacing={2} sx={{ mt: 3 }}>
      <Typography sx={{ fontWeight: 400, fontSize: '14px' }}>
        {contacts.length}
        {' '}
        عضو
      </Typography>

      {contacts.map((contact) => <Contact contact={contact} selected />)}
    </Stack>
  );
}
