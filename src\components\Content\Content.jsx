import { PATHS, TILE_VIEW_MODE } from 'constants';
import TileContent from './components/TileContent/TileContent';
import ListContent from './components/ListContent/ListContent';

export default function Content({
  withTitle,
  withAnalytics,
  withTypeIcon,
  rounded,
  content,
  size,
  viewMode = TILE_VIEW_MODE,
  marfook = false,
}) {
  const contentLink = marfook
    ? PATHS.contentMarfook.replace(':contentId', content.id)
    : PATHS.content.replace(':contentId', content.id);

  return viewMode === TILE_VIEW_MODE ? (
    <TileContent
      size={size}
      rounded={rounded}
      content={content}
      withTypeIcon={withTypeIcon}
      withTitle={withTitle}
      withAnalytics={withAnalytics}
      link={contentLink}
    />
  ) : (
    <ListContent content={content} link={contentLink} />
  );
}
