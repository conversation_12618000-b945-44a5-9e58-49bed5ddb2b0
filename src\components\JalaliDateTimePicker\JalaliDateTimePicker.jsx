import { useState } from 'react';
import {
  LocalizationProvider,
  MobileDateTimePicker,
} from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { faIR } from '@mui/x-date-pickers/locales';
import dayjs from 'utils/mydayjs';

export default function JalaliDateTimePicker({
  label,
  inputName,
  defaultValue,
  setMinDateToday = false,
  size = 'small',
  onChange, // Added to pass ISO date to parent
  disabled = false,
}) {
  const faLocale = {
    ...faIR.components.MuiLocalizationProvider.defaultProps.localeText,
    okButtonLabel: 'تایید',
  };

  // Store selectedDate as ISO string or null
  const [selectedDate, setSelectedDate] = useState(
    defaultValue ? dayjs(defaultValue).toISOString() : null,
  );

  const handleDateChange = newDate => {
    // Convert newDate to ISO format
    const isoDate = newDate ? dayjs(newDate).toISOString() : null;
    setSelectedDate(isoDate);
    // Pass ISO date to parent component if onChange is provided
    if (onChange) {
      onChange(isoDate);
    }
  };

  const handleClear = () => {
    setSelectedDate(null);
    if (onChange) {
      onChange(null);
    }
  };

  const today = dayjs();

  return (
    <LocalizationProvider
      dateAdapter={AdapterDayjs}
      adapterLocale="fa"
      localeText={faLocale}
    >
      <MobileDateTimePicker
        label={label}
        value={selectedDate ? dayjs(selectedDate) : null}
        onChange={handleDateChange}
        // Display in Jalali format, but store in ISO
        format="YYYY/MM/DD HH:mm"
        minDate={setMinDateToday ? today : null}
        slotProps={{
          actionBar: {
            actions: ['clear', 'accept', 'cancel'],
            onClear: handleClear,
          },
          textField: {
            name: inputName,
            placeholder: '',
            size,
            fullWidth: true,
            // Include hidden input for form submission
            InputProps: {
              endAdornment: selectedDate ? (
                <input type="hidden" name={inputName} value={selectedDate} />
              ) : null,
            },
          },
        }}
        defaultValue={defaultValue ? dayjs(defaultValue) : null}
        disabled={disabled}
      />
    </LocalizationProvider>
  );
}
