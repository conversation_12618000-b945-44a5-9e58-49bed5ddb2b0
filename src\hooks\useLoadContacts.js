import { getContacts } from 'apis/contacts';
import { createContact } from 'dtos/messenger';
import { useEffect } from 'react';
import { useQuery } from 'react-query';
import { useDispatch } from 'react-redux';
import { addContact } from 'store/messenger';

export default function useLoadContacts() {
  const { data } = useQuery('contacts', getContacts);
  const contacts = data?.data?.results || [];

  const dispatch = useDispatch();
  useEffect(() => {
    Object.values(contacts).forEach(contact =>
      dispatch(
        addContact(
          createContact(
            contact.id,
            contact.user.id,
            contact.user_first_name,
            contact.user_last_name,
            contact.direct_chat_id,
            contact.user.is_online,
            contact.user.last_online,
          ),
        ),
      ),
    );
  }, [contacts]);

  return contacts;
}
