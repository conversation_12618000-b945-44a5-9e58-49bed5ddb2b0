import { FormControlLabel, Radio, RadioGroup } from '@mui/material';

export default function Order({ searchConfig }) {
  return (
    <RadioGroup value={searchConfig.sort} sx={{ mt: 2 }} name="sort" row>
      <FormControlLabel
        xs
        control={<Radio inputProps={{ type: 'submit' }} />}
        label="جدید ترین"
        value="desc"
        sx={{
          mt: 1,
          mb: 1,
        }}
      />
      <FormControlLabel
        xs
        control={<Radio inputProps={{ type: 'submit' }} />}
        label="قدیمی ترین"
        value="asc"
        sx={{
          mt: 1,
          mb: 1,
        }}
      />
    </RadioGroup>
  );
}
