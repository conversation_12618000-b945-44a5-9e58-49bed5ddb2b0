import { useEffect, useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import { Cancel, CheckBox, CheckBoxOutlineBlank } from '@mui/icons-material';
import {
  Autocomplete,
  Avatar,
  Checkbox,
  Chip,
  ListItem,
  ListItemAvatar,
  ListItemText,
  TextField,
} from '@mui/material';
import debounce from 'lodash.debounce';
import { listUsers } from 'apis/auth';

function SelectMarfookUser({ setSelectedMember }) {
  const [selectedOption, setSelectedOption] = useState(null);
  const [inputValue, setInputValue] = useState('');
  const [options, setOptions] = useState([]);

  // Function to transform data into the required format
  const transformData = data =>
    data.map(item => ({
      id: item.id,
      label: item.first_name
        ? `${item.first_name} ${item.last_name}`
        : item.display_username
        ? `${item.display_username}`
        : `${item.username}`,
      avatar: item.avatar || '',
      is_marfook_user:
        item.groups.includes('MarfookManager') === true ||
        item.groups.includes('MarfookExpert') === true ||
        item.groups.includes('MarfookEvaluator') === true,
    }));

  // Fetch users based on input value using react-query
  const { data, isSuccess, refetch } = useQuery(
    ['listUsers', inputValue],
    () => {
      const sanitizedInput =
        inputValue?.startsWith('0') || inputValue?.startsWith('۰')
          ? inputValue.slice(1)
          : inputValue;

      return listUsers(sanitizedInput);
    },
    {
      enabled: false, // Do not fetch automatically
    },
  );

  // Debounced input handler
  const debouncedFetch = useMemo(
    () =>
      debounce(value => {
        if (value.length >= 3) {
          refetch(); // Trigger the query manually when input is debounced
        }
      }, 400),
    [],
  );

  // Update options when data is fetched successfully
  useEffect(() => {
    if (isSuccess && data?.status === 200) {
      setOptions(transformData(data?.data?.results || []));
    }
  }, [data, isSuccess]);

  // Handle input change with debounced fetch
  useEffect(() => {
    if (inputValue === '') {
      setOptions([]);
    } else {
      debouncedFetch(inputValue);
    }
  }, [inputValue, debouncedFetch]);

  const handleChange = (event, newValue) => {
    setSelectedOption(newValue);
    setSelectedMember(newValue);
  };

  const renderTags = (value, getTagProps) => {
    const displayedTags = value.slice(0, 5);
    return [
      ...displayedTags
        .reverse()
        .map((option, index) => (
          <Chip
            key={option.label}
            avatar={<Avatar src={option.avatar} />}
            label={option.label}
            {...getTagProps({ index })}
            deleteIcon={<Cancel />}
          />
        )),
      value.length > 5 && <Chip key="more" label={`+${value.length - 5}`} />,
    ];
  };

  return (
    <Autocomplete
      options={options.length > 0 ? options : []}
      getOptionDisabled={option => option.is_marfook_user}
      getOptionLabel={option => (option?.label ? option?.label : option)}
      filterOptions={x => x}
      autoComplete
      includeInputInList
      // filterSelectedOptions
      // PaperComponent={CustomPaper}
      // disableCloseOnSelect="false"
      value={selectedOption}
      onChange={handleChange}
      onInputChange={(event, newInputValue) => {
        setInputValue(newInputValue);
      }}
      renderTags={renderTags}
      noOptionsText="موردی یافتن نشد"
      isOptionEqualToValue={(option, value) => option.id === value.id}
      renderOption={(props, option, { selected }) => (
        <li {...props}>
          {option.label && (
            <ListItem>
              {option.avatar && (
                <ListItemAvatar>
                  <Avatar src={option.avatar} />
                </ListItemAvatar>
              )}
              <ListItemText
                primary={
                  option.is_marfook_user
                    ? `${option.label} (عضو مرفوک است) `
                    : option.label
                }
              />
            </ListItem>
          )}
        </li>
      )}
      renderInput={params => (
        <TextField {...params} variant="outlined" placeholder="انتخاب کاربر" />
      )}
    />
  );
}

export default SelectMarfookUser;
