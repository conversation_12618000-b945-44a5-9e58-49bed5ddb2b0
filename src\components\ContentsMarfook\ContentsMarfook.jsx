import React, { useEffect, useState, useRef } from 'react';
import { SEARCH_PARAMS } from 'components/SearchBox/searchConfig';
import { useInfiniteQuery } from 'react-query';
import { useInView } from 'react-intersection-observer';
import TextProgress from 'components/TextProgress/TextProgress';
import {
  Box,
  Button,
  Chip,
  Divider,
  Grid,
  IconButton,
  Modal,
  TextField,
  Typography,
} from '@mui/material';
import ContentMarfook from 'components/ContentMarfook/ContentMarfook';
import {
  createContentMarfook,
  deleteContentMarfook,
  getContentsMarfook,
  publishContentMarfook,
  resetMarfookStatus,
  updateContentMarfook,
} from 'apis/contentMarfook';
import Labels from 'components/ContentForm/components/Labels/Labels';
import JalaliDateTimePicker from 'components/JalaliDateTimePicker/JalaliDateTimePicker';
import dayjs from 'dayjs';
import { isAudio, isImage, isVideo } from 'utils';
import Type from 'components/ContentForm/components/Type/Type';
import Category from 'components/ContentForm/components/Category/Category';
import MyReactPlayer from 'components/MyReactPlayer/MyReactPlayer';
import { selectMe } from 'store/auth';
import { useDispatch, useSelector } from 'react-redux';
import { setSnackbar } from 'store/layout';

function hasContent(pages) {
  /**
   * Check to see if response contains content
   */
  return pages[0].count > 0;
}

export default function ContentsMarfook({ sx, searchConfig, withTypeIcon }) {
  const { ref, inView } = useInView();
  const me = useSelector(selectMe);
  const formRef = useRef(null); // Added form reference

  const { status, data, fetchNextPage, hasNextPage, refetch } =
    useInfiniteQuery(
      ['contents', searchConfig],
      async ({ pageParam = 1 }) => {
        const params = {
          ...searchConfig,
          [SEARCH_PARAMS.PAGE]: pageParam,
        };
        let response = {};
        response = await getContentsMarfook(params);
        return response.data;
      },
      {
        getPreviousPageParam: firstPage => firstPage?.previous ?? undefined,
        getNextPageParam: lastPage => lastPage?.next ?? undefined,
        cacheTime: 0,
      },
    );

  useEffect(() => {
    if (inView) {
      fetchNextPage();
    }
  }, [inView]);

  useEffect(() => {
    console.log(searchConfig);

    fetchNextPage();
  }, [searchConfig]);

  const [open, setOpen] = useState(false);
  const [selectedContent, setSelectedContent] = useState(false);
  const [loading, setLoading] = useState(false);
  const [disableFields, setDisableFields] = useState(
    selectedContent.status !== 'MW',
  );

  useEffect(() => {
    setDisableFields(selectedContent.status !== 'MW');
  }, [selectedContent]);

  const errors = {};

  // Modal open/close handlers
  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    setOpen(false);
    setSelectedContent(false);
  };
  const dispatch = useDispatch();

  const isDesktop = true; // Mock (adjust based on your needs)
  const DIVIDER_MY = 2; // Mock constant

  const publishContent = async () => {
    setLoading(true);
    try {
      // Initialize FormData with selectedContent values
      const formData = new FormData();
      Object.entries(selectedContent).forEach(([key, value]) => {
        formData.append(key, value);
      });
      formData.set('author', selectedContent.author?.id);

      // Update with form data from formRef
      if (formRef.current) {
        const formElementData = new FormData(formRef.current);
        Array.from(formElementData.entries()).forEach(([key, value]) => {
          formData.set(key, value);
        });
      }

      if (formData.get('labels')) {
        try {
          formData.set(
            'labels',
            JSON.stringify(formData.get('labels').split(',')),
          );
        } catch {
          formData.set('labels', JSON.stringify([]));
        }
      }

      // Convert FormData to JSON
      const data = {};
      Array.from(formData.entries()).forEach(([key, value]) => {
        if (key === 'labels') {
          try {
            data[key] = JSON.parse(value);
          } catch {
            data[key] = [];
          }
        } else {
          data[key] = value;
        }
      });
      // Call API with JSON data
      await publishContentMarfook(selectedContent.id, data);

      dispatch(
        setSnackbar({
          message: 'محتوا با موفقیت منتشر شد!',
          severity: 'success',
        }),
      );
      handleClose();
    } catch (e) {
      console.error(e);
      dispatch(
        setSnackbar({ message: 'خطا در انتشار محتوا!', severity: 'error' }),
      );
    } finally {
      setLoading(false);
      refetch();
    }
  };

  const resetStatus = async () => {
    try {
      setLoading(true);
      await resetMarfookStatus(selectedContent.id, { status: 'MW' });

      dispatch(
        setSnackbar({
          message: 'وضعیت محتوا تغییر یافت',
          severity: 'success',
        }),
      );
    } catch (e) {
      dispatch(
        setSnackbar({
          message: 'خطا در تغییر وضعیت محتوا',
          severity: 'error',
        }),
      );
    } finally {
      refetch();
      setLoading(false);
      handleClose();
    }
  };

  const rejectContent = async () => {
    try {
      setLoading(true);
      await resetMarfookStatus(selectedContent.id, { status: 'MR' });

      dispatch(
        setSnackbar({
          message: 'محتوا رد شد',
          severity: 'success',
        }),
      );
      refetch();
    } catch (e) {
      dispatch(
        setSnackbar({
          message: 'خطا در رد محتوا',
          severity: 'error',
        }),
      );
    } finally {
      refetch();
      setLoading(false);
      handleClose();
    }
  };

  const deleteContent = async () => {
    try {
      setLoading(true);
      await deleteContentMarfook(selectedContent.id);
      dispatch(
        setSnackbar({
          message: 'محتوا حذف شد',
          severity: 'success',
        }),
      );
      refetch();
    } catch (e) {
      dispatch(
        setSnackbar({
          message: 'خطا در حذف محتوا',
          severity: 'error',
        }),
      );
    } finally {
      setLoading(false);
      refetch();
      handleClose();
    }
  };

  const editContent = async () => {
    setLoading(true);
    try {
      // Initialize FormData with selectedContent values
      const formData = new FormData();
      Object.entries(selectedContent).forEach(([key, value]) => {
        formData.append(key, value);
      });
      formData.set('author', selectedContent.author?.id);

      // Update with form data from formRef
      if (formRef.current) {
        const formElementData = new FormData(formRef.current);
        Array.from(formElementData.entries()).forEach(([key, value]) => {
          formData.set(key, value);
        });
      }

      if (formData.get('labels')) {
        try {
          formData.set(
            'labels',
            JSON.stringify(formData.get('labels').split(',')),
          );
        } catch {
          formData.set('labels', JSON.stringify([]));
        }
      }

      // Convert FormData to JSON
      const data = {};
      Array.from(formData.entries()).forEach(([key, value]) => {
        if (key === 'labels') {
          try {
            data[key] = JSON.parse(value);
          } catch {
            data[key] = [];
          }
        } else {
          data[key] = value;
        }
      });
      // Call API with JSON data
      await updateContentMarfook(selectedContent.id, data);

      dispatch(
        setSnackbar({
          message: 'محتوا با موفقیت ویرایش شد!',
          severity: 'success',
        }),
      );
      handleClose();
    } catch (e) {
      console.error(e);
      dispatch(
        setSnackbar({ message: 'خطا در ویرایش محتوا!', severity: 'error' }),
      );
    } finally {
      setLoading(false);
      refetch();
    }
  };

  const repostContent = e => {
    e.preventDefault();
    setDisableFields(false);
  };

  const submitForm = async event => {
    event.preventDefault();

    const formData = new FormData();
    if (selectedContent) {
      Object.keys(selectedContent).forEach(item => {
        formData.append(item, selectedContent[item]);
      });
      formData.set('author', selectedContent.author?.id || '');
    }

    const formElementData = new FormData(event.target);
    Array.from(formElementData.entries()).forEach(([key, value]) => {
      formData.set(key, value);
    });

    try {
      await createContentMarfook(formData);

      dispatch(
        setSnackbar({
          message: 'محتوا با موفقیت جهت بررسی ارسال شد!',
          severity: 'success',
        }),
      );
      refetch();
      handleClose();
    } catch (e) {
      console.error(e);
      dispatch(
        setSnackbar({ message: 'خطا در ثبت مرفوک!', severity: 'error' }),
      );
    }
  };

  return (
    <Grid
      container
      alignItems="flex-start"
      className="no-scrollbar"
      sx={{ overflowY: 'scroll', mt: 3, ...sx }}
    >
      {status === 'error' && <TextProgress status="خطا در بارگذاری." />}
      {status === 'success' && !hasContent(data.pages) && (
        <TextProgress status="محتوایی یافت نشد." />
      )}

      {status === 'success' &&
        data.pages?.map(page => (
          <React.Fragment key={page?.next}>
            {page.results?.map(content => (
              <ContentMarfook
                key={content.id}
                content={content}
                withTypeIcon={withTypeIcon}
                setOpen={setOpen}
                setSelectedContent={setSelectedContent}
              />
            ))}
          </React.Fragment>
        ))}

      {hasNextPage && <TextProgress status="در حال بارگذاری ..." ref={ref} />}

      {/* Modal */}
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: { xs: '90%', sm: '80%', md: '60%' },
            maxHeight: '80vh',
            overflowY: 'auto',
            bgcolor: 'background.paper',
            boxShadow: 24,
            p: 4,
            borderRadius: 2,
          }}
          className="no-scrollbar"
        >
          <Typography
            id="modal-title"
            variant="h6"
            component="h2"
            gutterBottom
            sx={{
              textAlign: 'center',
              fontWeight: 'bold',
              mb: 4,
              position: 'sticky',
            }}
          >
            {selectedContent.status === 'MW' && 'بررسی نهایی محتوا'}
            {selectedContent.status === 'MA' && 'محتوای منتشر شده'}
            {selectedContent.status === 'MR' && 'محتوای رد شده'}
            {selectedContent.status === 'MC' && 'محتوای آرشیو شده'}
          </Typography>
          <form ref={formRef} onSubmit={submitForm}>
            <Grid container columnSpacing={2}>
              <Grid item xs={12} lg={5}>
                <Box
                  sx={{
                    position: 'relative',
                    width: '100%',
                    aspectRatio: '3/4',
                    borderRadius: '8px',
                    border: '1px solid #D1D1D6',
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                  alignItems="center"
                >
                  {selectedContent && isImage(selectedContent.file_type) && (
                    <img
                      alt="preview"
                      src={selectedContent.file}
                      style={{
                        width: '100%',
                        borderRadius: '8px',
                      }}
                    />
                  )}
                  {selectedContent && isVideo(selectedContent.file_type) && (
                    <MyReactPlayer
                      url={selectedContent.file}
                      isAudio={isAudio(selectedContent.file_type)}
                    />
                  )}
                  <Box
                    sx={{
                      position: 'absolute',
                      height: '90px',
                      bottom: '1px',
                      left: '1px',
                      right: '1px',
                      background:
                        selectedContent.status === 'MR'
                          ? 'linear-gradient(360deg, rgba(255, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%)'
                          : selectedContent.status === 'MA'
                          ? 'linear-gradient(360deg, rgba(72, 255, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%)'
                          : selectedContent.status === 'MW'
                          ? 'linear-gradient(360deg, rgba(255, 208, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%)'
                          : 'linear-gradient(360deg, rgba(63, 63, 63, 0.6) 0%, rgba(0, 0, 0, 0) 100%)',
                      color: '#fff',
                      textAlign: 'center',
                      pt: 2,
                    }}
                  >
                    {selectedContent.status === 'MR' && (
                      <Chip label="رد شده" color="error" size="small" />
                    )}
                    {selectedContent.status === 'MA' && (
                      <Chip label="منتشر شده" color="success" size="small" />
                    )}
                    {selectedContent.status === 'MW' && (
                      <Chip
                        label="در انتظار"
                        color="warning"
                        size="small"
                        style={{ backgroundColor: '#dfd12c' }}
                      />
                    )}
                    {selectedContent.status === 'MC' && (
                      <Chip
                        label="آرشیو شده"
                        size="small"
                        sx={{ background: '#fff' }}
                      />
                    )}
                  </Box>
                </Box>
              </Grid>
              <Grid
                item
                xs={12}
                lg={7}
                sx={{
                  mt: isDesktop ? 0 : 2,
                  maxHeight: 600,
                  overflowY: 'scroll',
                  py: 2,
                  px: 2,
                }}
                className="scrollbar-thin"
              >
                <Type
                  type={selectedContent.type}
                  errors={errors.type}
                  selectedFileType={selectedContent.file_type}
                  disabled={disableFields}
                />

                <Category
                  value={selectedContent.category}
                  errors={errors.category}
                  disabled={disableFields}
                />

                <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

                <TextField
                  variant="outlined"
                  required
                  label="عنوان"
                  name="title"
                  defaultValue={selectedContent.title}
                  helperText={errors.title}
                  error={!!errors.title}
                  inputProps={{ maxLength: 100 }}
                  fullWidth
                  disabled={disableFields}
                />

                <TextField
                  sx={{ mt: 2 }}
                  label="توضیحات"
                  name="description"
                  defaultValue={selectedContent.description}
                  variant="outlined"
                  multiline
                  rows={4}
                  helperText={errors.description}
                  fullWidth
                  disabled={disableFields}
                />

                <Divider sx={{ mt: 2, mb: 2 }} />

                <TextField
                  variant="outlined"
                  required
                  label="محور"
                  name="subject"
                  defaultValue={selectedContent.subject}
                  helperText={errors.subject}
                  error={!!errors.subject}
                  inputProps={{ maxLength: 100 }}
                  fullWidth
                  disabled={disableFields}
                />

                <Divider sx={{ mt: 2, mb: 2 }} />

                <Typography
                  variant="body2"
                  style={{ fontSize: '13px' }}
                  color="textSecondary"
                  mb={1}
                >
                  ** مجوز انتشار این محتوا فقط در روز و ساعات تعیین شده زیر
                  خواهد بود
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <JalaliDateTimePicker
                      inputName="publish_date_from"
                      label="از تاریخ"
                      size="medium"
                      setMinDateToday
                      defaultValue={
                        selectedContent.publish_date_from || dayjs()
                      }
                      time
                      disabled={disableFields}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <JalaliDateTimePicker
                      inputName="publish_date_to"
                      label="تا تاریخ"
                      size="medium"
                      setMinDateToday
                      defaultValue={selectedContent.publish_date_to || dayjs()}
                      time
                      disabled={disableFields}
                    />
                  </Grid>
                </Grid>

                <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

                <Labels
                  defaultValue={selectedContent.labels}
                  disabled={disableFields}
                />

                <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

                <TextField
                  sx={{ mt: 2 }}
                  label="توضیحات کارشناس"
                  name="expert_description"
                  defaultValue={selectedContent.expert_description}
                  variant="outlined"
                  multiline
                  rows={4}
                  helperText={errors.expert_description}
                  disabled={disableFields}
                  fullWidth
                />

                {me.groups.includes('MarfookManager') &&
                  selectedContent.status === 'MW' && (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: '10px',
                      }}
                    >
                      <Button
                        variant="contained"
                        size="large"
                        fullWidth
                        sx={{ mt: 2 }}
                        onClick={publishContent}
                        disabled={loading}
                      >
                        تایید انتشار
                      </Button>
                      <Button
                        variant="contained"
                        size="large"
                        fullWidth
                        color="error"
                        sx={{ mt: 2 }}
                        onClick={rejectContent}
                        disabled={loading}
                      >
                        رد کردن
                      </Button>
                    </Box>
                  )}

                {me.groups.includes('MarfookExpert') &&
                  selectedContent.status === 'MC' && (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: '10px',
                      }}
                    >
                      {disableFields ? (
                        <Button
                          variant="outlined"
                          size="large"
                          fullWidth
                          sx={{ mt: 2 }}
                          onClick={repostContent}
                          disabled={loading}
                        >
                          درخواست انتشار مجدد
                        </Button>
                      ) : (
                        <Button
                          variant="contained"
                          size="large"
                          type="submit"
                          fullWidth
                          sx={{ mt: 2 }}
                          disabled={loading}
                        >
                          انتشار برای مرفوک
                        </Button>
                      )}
                    </Box>
                  )}

                {me.groups.includes('MarfookExpert') &&
                  selectedContent.status === 'MW' && (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: '10px',
                      }}
                    >
                      <Button
                        variant="contained"
                        size="large"
                        fullWidth
                        sx={{ mt: 2 }}
                        onClick={editContent}
                        disabled={loading}
                      >
                        ویرایش
                      </Button>
                      <Button
                        variant="contained"
                        size="large"
                        fullWidth
                        color="error"
                        sx={{ mt: 2 }}
                        onClick={deleteContent}
                        disabled={loading}
                      >
                        حذف
                      </Button>
                    </Box>
                  )}
                {me.groups.includes('MarfookManager') &&
                  selectedContent.status === 'MA' && (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: '10px',
                      }}
                    >
                      <Button
                        variant="contained"
                        size="large"
                        color="error"
                        fullWidth
                        sx={{ mt: 2 }}
                        onClick={resetStatus}
                        disabled={loading}
                      >
                        لغو انتشار
                      </Button>
                    </Box>
                  )}
                {me.groups.includes('MarfookManager') &&
                  (selectedContent.status === 'MR' ||
                    selectedContent.status === 'MC') && (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: '10px',
                      }}
                    >
                      <Button
                        variant="outlined"
                        size="large"
                        fullWidth
                        sx={{ mt: 2 }}
                        onClick={resetStatus}
                        disabled={loading}
                      >
                        تغییر وضعیت
                      </Button>
                    </Box>
                  )}
              </Grid>
            </Grid>
          </form>
        </Box>
      </Modal>
    </Grid>
  );
}
