import { Grid, Typography, useMediaQuery, useTheme } from '@mui/material';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import { useQuery } from 'react-query';
import { SEARCH_PARAMS } from 'components/SearchBox/searchConfig';
import { getContents } from 'apis/content';
import Content from 'components/Content/Content';
import MyLink from 'components/MyLink/MyLink';
import { PATHS, CONTENT_STATUS } from 'constants';

function Header({ height, type, title, link = '' }) {
  return (
    <Grid
      container
      xs={12}
      direction="row"
      justifyContent="space-between"
      sx={{
        bgcolor: '#23262F',
        borderRadius: '8px',
        pr: 1,
        pl: 1,
        height,
        mt: 2,
      }}
    >
      <Typography
        color="white"
        sx={{
          height,
          lineHeight: height,
          paddingLeft: '8px',
          fontSize: '14px',
        }}
      >
        {title}
      </Typography>

      <MyLink to={`${PATHS.search}?type=${type}${link}`}>
        <Typography
          color="white"
          sx={{
            height,
            lineHeight: height,
            fontSize: '10px !important',
          }}
        >
          لیست کامل
          <ArrowBackIosNewIcon
            sx={{
              fontSize: '12px !important',
              verticalAlign: 'middle',
              marginLeft: '4px',
            }}
          />
        </Typography>
      </MyLink>
    </Grid>
  );
}

function Contents({ contents }) {
  return (
    <Grid container spacing={2} mt={1}>
      {contents.map(content => (
        <Content
          key={content.id}
          withTitle
          withTypeIcon
          rounded
          content={content}
        />
      ))}
    </Grid>
  );
}

export default function TopItems({
  title,
  type,
  ordering,
  fromDate,
  link = '',
}) {
  const theme = useTheme();
  const isTablet = useMediaQuery(theme.breakpoints.up('sm'));

  const HEIGHT = isTablet ? '48px' : '30px';

  const { isLoading, data } = useQuery(['contents', type, ordering], () =>
    getContents({
      [SEARCH_PARAMS.TYPE]: type,
      [SEARCH_PARAMS.ORDERING]: ordering,
      [SEARCH_PARAMS.PAGE_SIZE]: 9,
      [SEARCH_PARAMS.STATUS]: CONTENT_STATUS.APPROVED,
      [SEARCH_PARAMS.DATE_FROM]: fromDate,
    }),
  );

  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
  let contents = isLoading ? [] : data.data.results;
  contents = isDesktop ? contents.slice(0, 6) : contents.slice(0, 3);

  return (
    !isLoading && (
      <>
        <Header height={HEIGHT} type={type} title={title} link={link} />
        <Contents contents={contents} />
      </>
    )
  );
}
