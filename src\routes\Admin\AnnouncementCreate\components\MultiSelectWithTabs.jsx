import React, { useState, useRef, useEffect } from 'react';
import {
  Checkbox,
  ListItemText,
  Popper,
  Tabs,
  Tab,
  Box,
  ClickAwayListener,
  Typography,
  IconButton,
  InputBase,
  MenuItem,
} from '@mui/material';
import { ArrowDropDown } from '@mui/icons-material';
import { styled } from '@mui/system';
import { useQuery } from 'react-query';
import SearchInput from '../../AnnouncementView/components/ViewersSection/components/SearchInput';
import { getOrganizations, getOrganizationUsers } from '../../../../apis/organization';

const CustomInputBase = styled(InputBase)(({ theme }) => ({
  border: '1px solid rgba(0,0,0,0.25)',
  borderRadius: 6,
  padding: '6px 8px',
  width: '100%',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
}));

const CustomTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  '& .MuiTabs-indicator': {
    backgroundColor: '#009688',
  },
}));

const CustomTab = styled(Tab)(({ theme }) => ({
  minWidth: 72,
  textTransform: 'none',
  fontWeight: theme.typography.fontWeightRegular,
  marginRight: 0,
  color: 'rgba(0, 0, 0, 0.85)',
  '&.Mui-selected': {
    color: '#009688',
    fontWeight: theme.typography.fontWeightMedium,
  },
  '&.Mui-focusVisible': {
    backgroundColor: 'rgba(100, 95, 228, 0.32)',
  },
}));

function TabPanel(props) {
  const {
    children, value, index, ...other
  } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 2 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function MultiSelectWithTabs({
  selectedMembers = [], setSelectedMembers, selectedOrganizations = [], setSelectedOrganizations,
}) {
  const [selectedTexts, setSelectedTexts] = useState([]);
  const [open, setOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const anchorRef = useRef(null);

  const [members, setMembers] = useState([]);
  const [organizations, setOrganizations] = useState([]);

  useEffect(() => {
    if (selectedMembers.length === 0 && selectedOrganizations.length === 0) {
      setSelectedTexts([]);
    }
  }, [selectedMembers, selectedOrganizations]);

  useQuery({
    queryKey: ['OrganizationUserKey'],
    queryFn: () => getOrganizationUsers(),
    onSuccess: (data) => {
      if (data.status === 200) {
        setMembers(data.data);
      }
    },
  });

  useQuery({
    queryKey: ['OrganizationKey'],
    queryFn: () => getOrganizations(),
    onSuccess: (data) => {
      if (data.status === 200) {
        setOrganizations(data.data);
      }
    },
  });
  const handleChange = (event, value) => {
    if (tabValue === 0) {
      setSelectedOrganizations((prevValues) =>
        (prevValues.includes(value.id)
          ? prevValues.filter((v) => v !== value.id)
          : [...prevValues, value.id]),
      );
    } else if (tabValue === 1) {
      setSelectedMembers((prevValues) =>
        (prevValues.includes(value.id)
          ? prevValues.filter((v) => v !== value.id)
          : [...prevValues, value.id]),
      );
    }
    setSelectedTexts((prevValues) =>
      (prevValues.includes(value.value)
        ? prevValues.filter((v) => v !== value.value)
        : [...prevValues, value.value]),
    );
  };

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setSearchText('');
    setSelectedMembers([]);
    setSelectedOrganizations([]);
    setSelectedTexts([]);
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  const [searchText, setSearchText] = useState('');
  const [filteredData, setFilteredData] = useState([]);

  useEffect(() => {
    const filtered = members.filter(item => (`${item.first_name?.toLowerCase()}${item.last_name?.toLowerCase()}0${item.username?.toLowerCase()}`).includes(searchText.toLowerCase()));
    setFilteredData(filtered);
  }, [searchText, members]);

  const handleSearchChange = (e) => {
    setSearchText(e.target.value);
  };

  const selectAll = () => {
    if (tabValue === 0) {
      const allOrganizationIds = organizations.map(org => org.id);
      const allOrganizationNames = organizations.map(org => org.name);
      if (selectedOrganizations.length === organizations.length) {
        setSelectedOrganizations([]);
        setSelectedTexts([]);
      } else {
        setSelectedOrganizations(allOrganizationIds);
        setSelectedTexts(allOrganizationNames);
      }
    } else {
      const allMemberIds = filteredData.map(member => member.id);
      const allMemberNames = filteredData.map(member => `${member.first_name} ${member.last_name}`);
      if (selectedMembers.length === filteredData.length) {
        setSelectedMembers([]);
        setSelectedTexts([]);
      } else {
        setSelectedMembers(allMemberIds);
        setSelectedTexts(allMemberNames);
      }
    }
  };

  return (
    <>
      <CustomInputBase
        ref={anchorRef}
        value={selectedTexts.join(', ')}
        onClick={handleToggle}
        endAdornment={(
          <IconButton onClick={handleToggle}>
            <ArrowDropDown />
          </IconButton>
        )}
        placeholder="انتخاب مخاطبین"
        readOnly
      />
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        placement="bottom-start"
        style={{ width: anchorRef.current ? anchorRef.current.clientWidth : undefined }}
      >
        <ClickAwayListener onClickAway={handleClose}>
          <Box sx={{
            border: 'none',
            bgcolor: 'background.paper',
            width: '100%',
            borderRadius: '8px',
            boxShadow: '0px 4px 20px 0px #0000001A',
            // p: 1,
          }}
          >
            <CustomTabs value={tabValue} onChange={handleTabChange} variant="fullWidth">
              <CustomTab label="سازمان‌ها" />
              <CustomTab label="اعضا" />
            </CustomTabs>

            <TabPanel value={tabValue} index={0} style={{ minHeight: '200px', maxHeight: '350px', overflowY: 'scroll' }}>
              {organizations.length > 0 ? (
                <>
                  <MenuItem onClick={selectAll}>
                    <Checkbox checked={selectedOrganizations.length === organizations.length} />
                    <ListItemText primary="انتخاب همه" />
                  </MenuItem>
                  {organizations.map((option, index) => (
                    <MenuItem value={option.id} onClick={(event) => handleChange(event, { id: option.id, value: option.name })}>
                      <Checkbox checked={selectedOrganizations.includes(option.id)} />
                      {option.avatar
                        && (
                        <Box sx={{ maxWidth: '40px', mr: 2 }}>
                          <img alt="avatar" width="40px" height="40px" src={option.avatar} style={{ borderRadius: '100px' }} />
                        </Box>
                        )}
                      <ListItemText primary={option.name} />
                    </MenuItem>
                  ))}
                </>
              ) : <Box sx={{ width: '100%', textAlign: 'center', py: 6 }}>موردی یافت نشد</Box>}

            </TabPanel>
            <TabPanel value={tabValue} index={1} style={{ minHeight: '200px', maxHeight: '350px', overflowY: 'scroll' }}>
              <SearchInput label="جستجو" onChange={handleSearchChange} sx={{ position: 'sticky', top: '15px', zIndex: '10' }} />
              {searchText !== '' ? (filteredData.length > 0 ? (
                <>
                  <MenuItem onClick={selectAll}>
                    <Checkbox checked={selectedMembers.length === filteredData.length} />
                    <ListItemText primary="انتخاب همه" />
                  </MenuItem>
                  {filteredData.slice(-10).map((option, index) => (
                    <MenuItem value={option.id} onClick={(event) => handleChange(event, { id: option.id, value: option.first_name !== '' ? (`${option.first_name} ${option.last_name}`) : option.username })}>
                      <Checkbox checked={selectedMembers.includes(option.id)} />
                      {option.avatar
                          && (
                          <Box sx={{ maxWidth: '40px', mr: 2 }}>
                            <img alt="avatar" width="40px" height="40px" src={option.avatar} style={{ borderRadius: '100px' }} />
                          </Box>
                          )}
                      <ListItemText primary={option.first_name !== '' ? (`${option.first_name} ${option.last_name} (0${option.username})`) : option.username} />
                    </MenuItem>
                  ))}
                </>
              ) : <Box sx={{ width: '100%', textAlign: 'center', py: 6 }}>موردی یافت نشد</Box>) : <Box sx={{ width: '100%', textAlign: 'center', py: 6 }}>نام یا شماره شخص مورد نظر را جستجو کنید</Box>}

            </TabPanel>
          </Box>
        </ClickAwayListener>
      </Popper>
    </>
  );
}

export default MultiSelectWithTabs;
