import { createSlice } from '@reduxjs/toolkit';

export const chatPlayerSlice = createSlice({
  name: 'chatPlayer',
  initialState: {
    currentlyPlaying: null,
  },
  reducers: {
    playAudio(state, action) {
      state.currentlyPlaying = action.payload;
    },
    stopAudio(state) {
      state.currentlyPlaying = null;
    },
  },
});

export const { playAudio, stopAudio } = chatPlayerSlice.actions;

export const selectCurrentlyPlaying = (state) => state.chatPlayer.currentlyPlaying;
export default chatPlayerSlice.reducer;
