import {
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
} from '@mui/material';
import { isAudio, isVideo, isImage } from 'utils';
import { CONTENT_TYPES } from 'constants';

function getValidTypes(selectedFileType) {
  if (isVideo(selectedFileType)) {
    return CONTENT_TYPES.filter(type => type.fileTypes.includes('video'));
  }
  if (isImage(selectedFileType)) {
    return CONTENT_TYPES.filter(type => type.fileTypes.includes('image'));
  }
  if (isAudio(selectedFileType)) {
    return CONTENT_TYPES.filter(type => type.fileTypes.includes('audio'));
  }

  return CONTENT_TYPES;
}

export default function Type({ type, errors, disabled, selectedFileType }) {
  const validTypes = selectedFileType
    ? getValidTypes(selectedFileType)
    : CONTENT_TYPES;

  return (
    <FormControl fullWidth>
      <InputLabel>نوع محتوا</InputLabel>
      <Select
        label="نوع محتوا"
        name="type"
        defaultValue={type || validTypes[0].value}
        disabled={disabled}
        required
      >
        {validTypes.map(type => (
          <MenuItem value={type.value} key={type.value}>
            {type.name}
          </MenuItem>
        ))}
      </Select>
      <FormHelperText>{errors}</FormHelperText>
    </FormControl>
  );
}
