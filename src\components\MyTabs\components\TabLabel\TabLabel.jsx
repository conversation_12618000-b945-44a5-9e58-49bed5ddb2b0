import { SvgIcon, Typography } from '@mui/material';

export default function TabLabel({ label, icon, count }) {
  return (
    <Typography sx={{ fontSize: '12px' }} noWrap>
      {icon && (
        <SvgIcon
          component={icon}
          sx={{
            fill: 'none',
            fontSize: '12px',
            marginRight: '8px',
            verticalAlign: 'middle',
          }}
        />
      )}

      {label}

      {count > 0 && (
        ` (${count}) `
      )}
    </Typography>
  );
}
