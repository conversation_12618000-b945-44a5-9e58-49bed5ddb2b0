import { Stack } from '@mui/material';
import { removeParticipant } from 'apis/messenger';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { setSnackbar } from 'store/layout';

export default function RemoveParticipantBottomSheet({
  show,
  hideBottomSheet,
  userId,
  chatId,
}) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  function onRemoveParticipant() {
    removeParticipant({ chatId, participant: userId }).then(() => {
      dispatch(setSnackbar({
        message: 'کاربر با موفقیت حذف شد',
        severity: 'success',
      }));

      navigate(-1);
    });
  }

  return (
    <BottomSheet
      title="حذف کاربر از گفت‌وگو"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        آیا تمایل به حذف کاربر از گفت‌وگو دارید؟
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            onRemoveParticipant();
            hideBottomSheet();
          }}
        >
          بله
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={hideBottomSheet}>
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
