import {
  <PERSON>,
  Chip,
  Divider,
  Grid,
  Stack,
  Tooltip,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useIsDesktop } from 'utils';
import DesktopAppBar, {
  DESKTOP_APPBAR_HEIGHT,
} from 'components/DesktopAppBar/DesktopAppBar';
import LoadingPage from 'components/LoadingPage/LoadingPage';
import { useSelector } from 'react-redux';
import { selectMe } from 'store/auth';
import ContentsMarfook from 'components/ContentsMarfook/ContentsMarfook';
import { DateRange, Timer, Timer10, TimerRounded } from '@mui/icons-material';
import SearchBoxMarfookFolder from 'components/SearchBoxMarfookFolder/SearchBox';
import { getContentsMarfookFolder } from 'apis/contentMarfook';
import { useQuery } from 'react-query';
import ContentMarfook from 'components/ContentMarfook/ContentMarfook';
import dayjs from 'dayjs';
import ContentPreview from 'components/ContentPreview/ContentPreview';
import MyLink from 'components/MyLink/MyLink';
import { PATHS } from 'constants';

export function TimeRemaining({ publish_date_to }) {
  const [timeRemaining, setTimeRemaining] = useState('');

  useEffect(() => {
    // Parse publish_date_to
    const publishDate = dayjs(publish_date_to);

    // Validate the date
    if (!publishDate.isValid()) {
      setTimeRemaining('نامعتبر');
      return;
    }
    // Check if publish_date_to is in the past
    if (publishDate.isBefore(dayjs(), 'day')) {
      setTimeRemaining('منقضی شده');
      return;
    }

    // Function to calculate and format remaining time until end of today
    const updateRemainingTime = () => {
      const now = dayjs();
      const diffMs = publishDate.diff(now); // Difference in milliseconds

      if (diffMs <= 0) {
        setTimeRemaining('منقضی شده');
        return;
      }

      const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
      );
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      const parts = [];
      if (days > 0) parts.push(`${days} روز`);
      if (hours > 0) parts.push(`${hours} ساعت`);
      if (minutes > 0) parts.push(`${minutes} دقیقه`);

      // If all values are zero, show a minimal message
      if (parts.length === 0) {
        setTimeRemaining('کمتر از یک دقیقه مانده');
      } else {
        setTimeRemaining(`${parts.join(' و ')} مانده`);
      }
    };

    // Update immediately
    updateRemainingTime();

    // Set up interval to update every second
    const interval = setInterval(updateRemainingTime, 1000);

    // Cleanup interval on component unmount
    return () => clearInterval(interval);
  }, [publish_date_to]);

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: 1,
      }}
    >
      <TimerRounded
        color="primary"
        size="small"
        sx={{ fontSize: '18px', mr: '2px' }}
      />
      <Typography sx={{ fontSize: '11.5px', pt: '2px' }}>
        {timeRemaining}
      </Typography>
    </Box>
  );
}

export default function MarfookFolder() {
  const isDesktop = useIsDesktop();

  const { status, data, refetch } = useQuery(
    ['contentsMarfookFolder'],
    async () => {
      let response = {};
      response = await getContentsMarfookFolder();

      return response.data;
    },
  );

  const me = useSelector(selectMe);

  if (!me || !data) return <LoadingPage />;

  return (
    <>
      {isDesktop && <DesktopAppBar />}

      <Stack
        sx={{
          height: '100%',
          marginTop: isDesktop ? DESKTOP_APPBAR_HEIGHT : 0,
        }}
      >
        {/* <SearchBoxMarfookFolder searchConfig={{}} setfilter={() => {}} /> */}

        {data.length === 0 ? (
          <Typography variant="h6" fontWeight="bold" textAlign="center" mb={2}>
            محتوایی جهت انتشار وجود ندارد
          </Typography>
        ) : (
          <Typography variant="h6" fontWeight="bold" textAlign="center" mb={2}>
            محتوای قابل انتشار مرفوک
          </Typography>
        )}

        {!!data &&
          data.length > 0 &&
          data.map((date, _index) => (
            <>
              {/* <Divider sx={{ my: 3 }} /> */}
              <Typography
                variant="h6"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  borderLeft: '7px solid #11a6a1',
                  mt: 2,
                  px: 2,
                  py: 1,
                  fontSize: '16px',
                  background: 'rgba(52, 177, 173, 0.31)',
                  borderRadius: '5px',
                }}
              >
                <DateRange color="primary" sx={{ mr: 1 }} />
                تاریخ: {dayjs(date.date).format('YYYY/MM/DD')}
              </Typography>
              <Grid
                container
                alignItems="flex-start"
                sx={{ overflowY: 'scroll', mt: 3 }}
                className="no-scrollbar"
              >
                {!!date.events &&
                  date.events?.map((content, __index) => (
                    <Grid item xs={4} lg={2}>
                      <MyLink
                        to={PATHS.contentMarfook.replace(
                          ':contentId',
                          content.id,
                        )}
                      >
                        <Stack
                          sx={{
                            position: 'relative',
                            cursor: 'pointer',
                          }}
                        >
                          <ContentPreview
                            rounded
                            preview={content.preview}
                            fileType={content.file_type}
                            withTypeIcon
                            status={content.status}
                          />
                          <Box
                            sx={{
                              position: 'absolute',
                              height: '60px',
                              bottom: '1px',
                              left: '1px',
                              right: '1px',
                              background:
                                'linear-gradient(360deg, rgba(72, 255, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%)',
                              color: '#fff',
                              textAlign: 'center',
                              pt: 2,
                            }}
                          >
                            <Tooltip title={`محور: ${content.subject}`}>
                              <Chip
                                label={`${content.subject}`}
                                size="small"
                                sx={{ background: '#fff', maxWidth: '95%' }}
                              />
                            </Tooltip>
                          </Box>
                        </Stack>
                      </MyLink>

                      <TimeRemaining
                        publish_date_to={content.publish_date_to}
                      />
                    </Grid>
                  ))}
              </Grid>
            </>
          ))}

        {/* <Divider sx={{ my: 3 }} />
        <Typography
          variant="h6"
          sx={{
            display: 'flex',
            alignItems: 'center',
            borderLeft: '7px solid #11a6a1',
            mt: 2,
            px: 2,
            py: 1,
            fontSize: '16px',
            background: 'rgba(52, 177, 173, 0.31)',
            borderRadius: '5px',
          }}
        >
          <DateRange color="primary" sx={{ mr: 1 }} />
          تاریخ: ۱۴۰۴/۲/۳۱
        </Typography>

        <ContentsMarfook searchConfig={{}} withTypeIcon /> */}
        {/* <SearchBoxMarfook searchConfig={filter} />

        <ContentsMarfook
          searchConfig={filter}
          withTypeIcon
        /> */}
      </Stack>
    </>
  );
}
