import axios from 'axios';
import { BASE_URL, PATHS } from '../constants';
import { getToken, removeToken } from 'utils';

const myAxios = axios.create({ baseURL: BASE_URL, withCredentials: true });

myAxios.interceptors.response.use(
    response => response,
    error => {
        if (error.response?.status === 401) {
            removeToken()
            window.location.href = PATHS.enter_mobile;
        } else {
            return Promise.reject(error);
        }
    },
);

myAxios.interceptors.request.use(
    config => {
        const token = getToken();

        if (token) {
            config.headers.Authorization = `Token ${token}`;
        }

        return config;
    },
    error => Promise.reject(error),
);

export default myAxios;
