import { useState } from 'react';
import Button from '@mui/material/Button';
import ButtonGroup from '@mui/material/ButtonGroup';
import { styled } from '@mui/material/styles';

const StyledButton = styled(Button)(({ theme, active }) => ({
  backgroundColor: active ? '#E7EAF4' : '#E1E8EF40',
  color: active ? '#000' : '#737373',
  borderRadius: '6px',
  height: '26px',
  padding: '4px 8px 4px 8px',
  borderRight: 'none!important',
  borderTopRightRadius: '6px!important',
  borderTopLeftRadius: '6px!important',
  borderBottomRightRadius: '6px!important',
  borderBottomLeftRadius: '6px!important',
  fontSize: '12px',
  boxShadow: 'none',
  margin: '0 3px',
  '&:hover': {
    backgroundColor: '#E7EAF4',
    color: '#000',
  },
}));

function CustomButtonGroup({ labelList, onButtonClick }) {
  const [activeLabel, setActiveLabel] = useState(labelList[0]);

  const handleButtonClick = (label) => {
    setActiveLabel(label);
    onButtonClick(label);
  };

  return (
    <ButtonGroup variant="contained" sx={{ boxShadow: 'none' }}>
      {labelList.map((label) => (
        <StyledButton
          key={label}
          active={label === activeLabel}
          onClick={() => handleButtonClick(label)}
        >
          {label}
        </StyledButton>
      ))}
    </ButtonGroup>
  );
}

export default CustomButtonGroup;
