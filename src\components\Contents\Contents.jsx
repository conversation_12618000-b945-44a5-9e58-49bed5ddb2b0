import React, { useEffect } from 'react';
import { SEARCH_PARAMS } from 'components/SearchBox/searchConfig';
import { useInfiniteQuery } from 'react-query';
import { useInView } from 'react-intersection-observer';
import { getContents } from 'apis/content';
import { listUsers } from 'apis/auth';
import TextProgress from 'components/TextProgress/TextProgress';
import { Grid } from '@mui/material';
import Content from '../Content/Content';
import User from '../User/User';

function hasContent(pages) {
  /**
   * Check to see if response contains content
   */

  return pages[0].count > 0;
}

export default function Contents({
  sx,
  searchConfig,
  contentType,
  author,
  interactionType,
  withAnalytics,
  withTypeIcon,
  ordering,
  viewMode,
}) {
  const { ref, inView } = useInView();

  const { status, data, fetchNextPage, hasNextPage } = useInfiniteQuery(
    ['contents', searchConfig, contentType, interactionType, author],
    async ({ pageParam = 1 }) => {
      const type =
        contentType !== undefined
          ? contentType
          : searchConfig[SEARCH_PARAMS.TYPE];

      const params = {
        ...searchConfig,
        author,
        interaction_type: interactionType,
        [SEARCH_PARAMS.ORDERING]: ordering || searchConfig.ordering,
        [SEARCH_PARAMS.TYPE]: type,
        [SEARCH_PARAMS.PAGE]: pageParam,
      };

      let response = {};
      if (contentType === 'U') {
        if (searchConfig[SEARCH_PARAMS.SEARCH]) {
          let searchParam = searchConfig[SEARCH_PARAMS.SEARCH];
          if (searchParam.startsWith('0') || searchParam.startsWith('۰')) {
            searchParam = searchParam.slice(1, searchParam.length);
          }
          response = await listUsers(searchParam);
        }
      } else {
        response = await getContents(params);
      }
      return response.data;
    },
    {
      getPreviousPageParam: firstPage => firstPage?.previous ?? undefined,
      getNextPageParam: lastPage => lastPage?.next ?? undefined,
      cacheTime: 0,
    },
  );

  useEffect(() => {
    if (inView) {
      fetchNextPage();
    }
  }, [inView]);

  useEffect(() => {
    fetchNextPage();
  }, [searchConfig, contentType]);

  return (
    <Grid
      container
      alignItems="flex-start"
      className="no-scrollbar"
      sx={{ overflowY: 'scroll', ...sx }}
    >
      {status === 'error' && <TextProgress status="خطا در بارگذاری." />}
      {status === 'success' &&
        contentType !== 'U' &&
        !hasContent(data.pages) && <TextProgress status="محتوایی یافت نشد." />}

      {status === 'success' &&
        (contentType !== 'U'
          ? data.pages?.map(page => (
              <React.Fragment key={page?.next}>
                {page.results?.map(content => (
                  <Content
                    key={content.id}
                    content={content}
                    withAnalytics={withAnalytics}
                    withTypeIcon={withTypeIcon}
                    viewMode={viewMode}
                  />
                ))}
              </React.Fragment>
            ))
          : data.pages?.[0]?.results?.map(user => (
              <User key={user.id} user={user} />
            )))}

      {hasNextPage && <TextProgress status="در حال بارگذاری ..." ref={ref} />}
    </Grid>
  );
}
