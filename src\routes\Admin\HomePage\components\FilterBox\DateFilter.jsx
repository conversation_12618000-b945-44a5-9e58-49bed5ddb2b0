import { Button, Stack, Typography } from '@mui/material';
import dayjs from 'dayjs';
import { useState } from 'react';
import JalaliDatePicker from '../../../../../components/SearchBox/components/JalaliDatePicker/JalaliDatePicker';

export default function DateFilter() {
  const [selectedDate, setSelectedDate] = useState({
    from: null,
    to: null,
    index: 1,
  });

  return (
    <Stack
      flex={1}
      direction="column"
      spacing={2}
      justifyContent="flex-start"
      sx={{
        mt: 0,
        border: '2px solid #ccc',
        borderRadius: '8px',
        padding: '18px 8px',
      }}
    >
      <Typography
        sx={{
          fontSize: '16px',
          width: '100%',
          paddingTop: '9px',
          fontWeight: '700',
        }}
      >
        انتخاب تاریخ گزارش:
      </Typography>
      <Stack direction="row" spacing={1}>
        <JalaliDatePicker
          label="از تاریخ"
          size="small"
          defaultValue={dayjs().subtract(1, 'month')}
        />
        <JalaliDatePicker
          label="تا تاریخ"
          size="small"
          defaultValue={dayjs()}
        />
      </Stack>
      <Stack direction="column" spacing={1}>
        <Stack flex={1} direction="row" spacing={1}>
          <Button
            variant={selectedDate.index === 0 ? 'contained' : 'outlined'}
            sx={{ flex: 1 }}
            onClick={() => setSelectedDate({ from: null, to: null, index: 0 })}
          >
            امروز
          </Button>
          <Button
            variant={selectedDate.index === 1 ? 'contained' : 'outlined'}
            sx={{ flex: 1 }}
            onClick={() => setSelectedDate({ from: null, to: null, index: 1 })}
          >
            ۲۴ ساعت گذشته
          </Button>
          <Button
            variant={selectedDate.index === 2 ? 'contained' : 'outlined'}
            sx={{ flex: 1 }}
            onClick={() => setSelectedDate({ from: null, to: null, index: 2 })}
          >
            ۳ روز گذشته
          </Button>
        </Stack>
        <Stack flex={1} direction="row" spacing={1}>
          <Button
            variant={selectedDate.index === 3 ? 'contained' : 'outlined'}
            sx={{ flex: 1 }}
            onClick={() => setSelectedDate({ from: null, to: null, index: 3 })}
          >
            هفته گذشته
          </Button>
          <Button
            variant={selectedDate.index === 4 ? 'contained' : 'outlined'}
            sx={{ flex: 1 }}
            onClick={() => setSelectedDate({ from: null, to: null, index: 4 })}
          >
            ۱۰ روز گذشته
          </Button>
          <Button
            variant={selectedDate.index === 5 ? 'contained' : 'outlined'}
            sx={{ flex: 1 }}
            onClick={() => setSelectedDate({ from: null, to: null, index: 5 })}
          >
            ماه گذشته
          </Button>
        </Stack>
      </Stack>
    </Stack>
  );
}
