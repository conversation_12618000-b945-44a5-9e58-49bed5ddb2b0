import { Stack, Grid, Box, Chip } from '@mui/material';
import ContentPreview from 'components/ContentPreview/ContentPreview';

const BORDER_SIEZE = '1px';

export default function TileContent({
  content,
  withTypeIcon,
  setOpen,
  setSelectedContent,
}) {
  return (
    <Grid item xs={4} lg={2}>
      <Stack
        sx={{
          position: 'relative',
          cursor: 'pointer',
        }}
        onClick={() => {
          setSelectedContent(content);
          setOpen(true);
        }}
      >
        <ContentPreview
          rounded
          preview={content.preview}
          fileType={content.file_type}
          withTypeIcon={withTypeIcon}
          status={content.status}
          disable={content.status !== 'MW'}
        />
        <Box
          sx={{
            position: 'absolute',
            height: '60px',
            bottom: BORDER_SIEZE,
            left: BORDER_SIEZE,
            right: BORDER_SIEZE,
            background:
              content.status === 'MR'
                ? 'linear-gradient(360deg, rgba(255, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%)'
                : content.status === 'MA'
                ? 'linear-gradient(360deg, rgba(72, 255, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%)'
                : content.status === 'MW'
                ? 'linear-gradient(360deg, rgba(255, 208, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%)'
                : 'linear-gradient(360deg, rgba(63, 63, 63, 0.6) 0%, rgba(0, 0, 0, 0) 100%)',
            color: '#fff',
            textAlign: 'center',
            pt: 2,
          }}
        >
          {content.status === 'MR' && (
            <Chip label="رد شده" color="error" size="small" />
          )}
          {content.status === 'MA' && (
            <Chip label="منتشر شده" color="success" size="small" />
          )}
          {content.status === 'MW' && (
            <Chip
              label="در انتظار"
              color="warning"
              size="small"
              style={{ backgroundColor: '#dfd12c' }}
            />
          )}
          {content.status === 'MC' && (
            <Chip label="آرشیو شده" size="small" sx={{ background: '#fff' }} />
          )}
        </Box>
      </Stack>
    </Grid>
  );
}
