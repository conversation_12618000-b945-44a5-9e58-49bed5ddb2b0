import { IconButton } from '@mui/material';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';

export default function RightArrowButton({ scrollContainer, scrollSize }) {
  const scroll = () => {
    /* eslint  no-param-reassign: "error" */
    scrollContainer.current.scrollLeft += scrollSize;
  };
  return (
    <IconButton
      sx={{
        color: 'white', m: 0, pr: 1, pl: 0,
      }}
      edge="start"
      onClick={scroll}
    >
      <ChevronRightIcon />
    </IconButton>
  );
}
