import {
  Grid, Stack, SvgIcon, Typography, useTheme,
} from '@mui/material';
import MyLink from 'components/MyLink/MyLink';
import { CONTENT_TYPES, PATHS } from 'constants';
import { useRef } from 'react';
import IconBg from '../IconBg/IconBg';

function getViewWidth() {
  /**
   * Get width of view
   */

  const theme = useTheme();
  const ARROW_BUTTONS_WIDTH = 64;
  return Math.min(theme.breakpoints.values.sm, window.innerWidth) - ARROW_BUTTONS_WIDTH;
}

function calcItemSize() {
  return getViewWidth() * 0.2;
}

function TypeItem({
  type, icon, color, value,
}) {
  const link = `${PATHS.search}?type=${value}`;
  return (
    <MyLink to={link}>
      <Stack alignItems="center">
        <IconBg size={calcItemSize()}>
          <SvgIcon
            component={icon}
            sx={{
              fill: 'none',
              width: '50%',
              height: '50%',
              color,
            }}
          />
        </IconBg>

        <Typography
          noWrap
          color="white"
          align="center"
          sx={{ width: calcItemSize(), fontSize: '14px' }}
        >
          {type}
        </Typography>
      </Stack>
    </MyLink>
  );
}

export default function ContentTypeMenu() {
  const scrollContainer = useRef(null);

  return (
    <Grid
      ref={scrollContainer}
      container
      xs={12}
      item
      spacing={2}
      alignItems="center"
      columns={8}
    >
      {CONTENT_TYPES.map((type) => (
        <Grid item lg={1} xs={2}>

          <TypeItem
            key={type.name}
            type={type.name}
            icon={type.icon}
            color={type.color}
            value={type.value}
          />
        </Grid>
      ))}
    </Grid>
  );
}
