import axios from 'axios';
import myAxios from './myAxios';

export async function createContentMarfook(data) {
  return myAxios.post('/marfook/content/', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

export async function getContentsMarfook(searchConfig) {
  let q = '';

  Object.keys(searchConfig).map(item => {
    if (item === 'status') {
      searchConfig[item].forEach(x => {
        q += `${item}=${x}&`;
      });
    } else {
      q += `${item}=${searchConfig[item]}&`;
    }
  });

  return myAxios.get(`/marfook/content/?${q}`);
}

export async function getContentsMarfookFolder() {
  return myAxios.get('/marfook/content/list/published/');
}

export async function getContentMarfook(id) {
  return myAxios.get(`/marfook/content/${id}/`);
}

export async function publishContentMarfook(id, data) {
  return myAxios.put(`/marfook/content/${id}/approved/`, data);
}

export async function rejectContentMarfook(id) {
  return myAxios.patch(`/marfook/content/${id}/rejected/`);
}

export async function deleteContentMarfook(id) {
  return myAxios.delete(`/marfook/content/${id}/`);
}

export async function updateContentMarfook(id, data) {
  return myAxios.put(`/marfook/content/${id}/`, data);
}

export async function resetMarfookStatus(id, data) {
  return myAxios.patch(`/marfook/content/${id}/state/`, data);
}
