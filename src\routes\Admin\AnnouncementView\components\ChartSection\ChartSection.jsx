import { Box, Grid, Typography } from '@mui/material';
import {
  Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis,
} from 'recharts';
import dayjs from 'dayjs';
import CustomButtonGroup from '../CustomButtonGroup';

function ChartSection({ data = [] }) {
  const clearedData = [];
  for (const item of data) {
    clearedData.push({
      بازدید: item.read_number,
      name: dayjs(item.day).format('YYYY/MM/DD'),
    });
  }

  const labels = ['همه زمان‌ها', '۳۰ روز گذشته', '۷ روز گذشته'];

  const handleButtonClick = (label) => {
    console.log(`Button ${label} clicked`);
  };

  return (
    <Grid
      item
      container
      xs={12}
      sx={{
        py: 2,
        px: 3,
        mb: 3,
        borderRadius: '8px',
        background: 'white',
        boxShadow: '0px 2px 20px 0px #00000012',
      }}
    >

      <Box display="flex" width="100%" justifyContent="space-between" mb={5}>
        <Typography variant="h6" fontWeight="bold" fontSize={18}>روند بازدید</Typography>
        {/* <Box><CustomButtonGroup labelList={labels} onButtonClick={handleButtonClick} /></Box> */}
      </Box>
      <ResponsiveContainer width="100%" height="100%" aspect={16 / 6}>
        <AreaChart
          width="100%"
          height="400px"
          data={clearedData}
          margin={{
            top: 10,
            right: 0,
            left: 0,
            bottom: 0,
          }}
        >
          <defs>
            <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
              <stop offset="50%" stopColor="#0FA6A14D" stopOpacity={0.8} />
              <stop offset="95%" stopColor="#0FA6A14D" stopOpacity={0} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="2 2" vertical={false} />
          <XAxis dataKey="name" fontSize={12} color="#737373" />
          <YAxis strokeWidth={0} direction="ltr" fontSize={12} color="#737373" />
          <Tooltip />
          <Area type="monotone" dataKey="بازدید" stroke="#0FA6A1" strokeWidth={3} fill="url(#colorUv)" />
        </AreaChart>
      </ResponsiveContainer>
    </Grid>
  );
}

export default ChartSection;
