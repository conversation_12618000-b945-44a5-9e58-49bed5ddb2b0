import { IconButton, styled } from '@mui/material';
import { grey } from '@mui/material/colors';
import { alpha, Box } from '@mui/system';
import CloseIcon from '@mui/icons-material/Close';
import { TransformComponent, TransformWrapper } from 'react-zoom-pan-pinch';

const Img = styled('img')({
  width: '90%',
  height: '90%',
  margin: 'auto',
  objectFit: 'contain',
});

export default function FullScreenImage({ show, unShow, img }) {
  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: show ? 'flex' : 'none',
        position: 'fixed',

        margin: '0px !important',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,

        zIndex: 100000,

        backgroundColor: alpha(grey[600], 0.8),
      }}
      onClick={unShow}
    >
      <IconButton
        sx={{
          position: 'absolute',
          top: '16px',
          right: '16px',
          zIndex: 100,
        }}
        onClick={unShow}
      >
        <CloseIcon sx={{ color: 'white' }} />
      </IconButton>

      <Box sx={{ margin: 'auto', height: '100%' }}>
        <TransformWrapper maxScale={2}>
          <TransformComponent
            contentStyle={{ height: '100%' }}
            wrapperStyle={{ height: '100%' }}
          >
            <Img src={img} />
          </TransformComponent>
        </TransformWrapper>
      </Box>
    </Box>
  );
}
