import { Grid, Typography } from '@mui/material';
import { useQuery } from 'react-query';
import { getMe, GET_ME_URL } from 'apis/auth';
import LoadingPage from 'components/LoadingPage/LoadingPage';
import { Box } from '@mui/system';
import ReactPlayer from 'react-player';

export default function AdminHomePage() {
  const { isLoading, data } = useQuery(GET_ME_URL, () => getMe());

  if (isLoading) {
    return <LoadingPage />;
  }

  return (
    <Grid container sx={{ p: 2 }} xs={12} lg={12}>
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        textAlign="center"
        flexDirection="column"
        sx={{ flexGrow: 1 }}
      >
        <Typography
          variant="h5"
          sx={{ fontWeight: 'bold', marginTop: '30px', marginBottom: '46px' }}
        >
          ویدیو آموزش پنل مدیریت تام
        </Typography>
        <Box
          sx={{
            width: '100%',
            height: '70vh',
          }}
        >
          {/* <ReactPlayer
            url="https://tam-front-production.s3.ir-thr-at1.arvanstorage.ir/tutorial%2Fadmin_tutorial.mp4?versionId="
            controls
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              margin: 'auto',
              border: '1px solid #0FA6A1',
              borderRadius: '5px',
            }}
            width="80%"
            maxWidth="100%"
            height="100%"
            playing
          /> */}
        </Box>
      </Box>
    </Grid>
  );
}
