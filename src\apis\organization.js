import myAxios from './myAxios';

export async function createOrganization(data, onProgress, abortController) {
  return myAxios.post('/auth/organizations/', data, {
    signal: abortController.signal,
    headers: { 'Content-Type': 'application/json' },
    onUploadProgress: e => {
      onProgress(Math.floor((e.loaded / e.total) * 100));
    },
  });
}

export async function getOrganizations() {
  return myAxios.get('/auth/organizations/');
}

export async function getOrganization(id) {
  return myAxios.get(`/auth/organizations/${id}/`);
}

export async function getOrganizationGraph() {
  return myAxios.get('/auth/organizations/traverse/');
}

export async function updateOrganization(id, data) {
  return myAxios.put(`/auth/organizations/${id}/`, data);
}
export async function deleteOrganization(id) {
  return myAxios.delete(`/auth/organizations/${id}/`);
}

export function getOrganizationUsers() {
  return myAxios.get('/auth/organizations/users/');
}

export function getNotAllocatedUsers() {
  return myAxios.get('/auth/organizations/not-allocated-users/');
}
