import { Stack } from '@mui/material';
import BottomSheet from 'components/BottomSheet/BottomSheet';
import BottomSheetMessage from 'components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from 'components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from 'components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';

export default function ForwardBottomSheet({
  show,
  hideBottomSheet,
  onConfirm,
}) {
  return (
    <BottomSheet
      title="باز ارسال پیام"
      hideBottomSheet={hideBottomSheet}
      show={show}
    >
      <BottomSheetMessage>
        آیا تمایل به باز ارسال پیام دارید؟
      </BottomSheetMessage>

      <Stack direction="row">
        <BottomSheetPrimaryButton
          onClick={() => {
            onConfirm();
            hideBottomSheet();
          }}
        >
          بله
        </BottomSheetPrimaryButton>
        <BottomSheetSecondaryButton onClick={hideBottomSheet}>
          خیر
        </BottomSheetSecondaryButton>
      </Stack>
    </BottomSheet>
  );
}
