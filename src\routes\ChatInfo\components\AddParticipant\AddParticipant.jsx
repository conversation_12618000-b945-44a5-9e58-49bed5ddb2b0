import { Button } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { useNavigate } from 'react-router-dom';
import { setPathParam } from 'utils';
import { PATHS } from 'constants';

export default function AddParticipant({ chatId }) {
  const navigate = useNavigate();
  const onClick = () => {
    navigate(setPathParam(PATHS.addParticipant, 'chatId', chatId));
  };

  return (
    <Button startIcon={<AddIcon />} onClick={onClick} sx={{ p: 0 }}>
      افزودن عضو
    </Button>
  );
}
