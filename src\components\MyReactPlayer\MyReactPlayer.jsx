import ReactPlayer from 'react-player';

export default function MyReactPlayer({
  url, isAudio, preview, playing = true, controls = true,
  onPlay, onPause, sx,
}) {
  return (
    <ReactPlayer
      url={url}
      controls={controls}
      style={{
        maxWidth: '100%',
        maxHeight: '100%',
        margin: 'auto',
        ...sx,
      }}
      maxWidth="100%"
      light={preview}
      height={isAudio ? '64px' : '100%'}
      playing={playing}
      onPlay={onPlay}
      onPause={onPause}
    />
  );
}
