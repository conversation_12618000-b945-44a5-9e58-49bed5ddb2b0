import {
  Avatar, Stack, Typography, styled,
} from '@mui/material';
import { useSelector } from 'react-redux';
import { selectMyId } from 'store/auth';
import { buildFullName } from 'utils';
import {
  canBlockUser, canMakeAdmin, canMakeUnAdmin, canRemoveUser, canUnBlockUser,
} from './utils';
import RemoveUserButton from '../RemoveUserButton/RemoveUserButton';
import MakeAdminButton from '../MakeAdminButton/MakeAdminButton';
import MakeUnAdminButton from '../MakeUnAdminButton/MakeUnAdminButton';
import BlockUserButton from '../BlockUserButton/BlockUserButton';
import UnBlockUserButton from '../UnBlockUserButton/UnBlockUserButton';

const Name = styled(Typography)(() => ({
  fontSize: '14px',
  fontWeight: 500,
  flexGrow: 1,
  margin: 'auto',
}));

function RoleBadge({ isCreator, isAdmin, sx }) {
  let role = '';
  if (isCreator) role = 'سازنده';
  else if (isAdmin) role = 'مدیر';

  return (
    <Typography
      sx={{
        color: 'secondary.main', fontSize: '14px', fontStyle: 'italic', display: 'inline', margin: 'auto', ...sx,
      }}
      component="span"
    >
      {role}
    </Typography>
  );
}

export default function Participant({
  userId,
  firstName,
  lastName,
  avatar, onClick, creator, admins, chatId, displayUsername, isBlocked,
}) {
  const name = (firstName || lastName) ? buildFullName(firstName, lastName) : displayUsername;
  const myId = useSelector(selectMyId);

  const isAdmin = admins.includes(userId);
  const isCreator = creator === userId;

  return (
    <Stack
      direction="row"
      alignItems="center"
      justifyContent="space-between"
    >
      <Avatar src={avatar} />

      <Stack flexGrow={1} sx={{ ml: 2 }} direction="row" justifyContent="space-between">
        <Name component="span" onClick={onClick}>{name}</Name>

        {(isCreator || isAdmin)
          && <RoleBadge isCreator={isCreator} isAdmin={isAdmin} sx={{ mr: 1 }} />}

        {canRemoveUser(myId, admins, creator, userId)
          && <RemoveUserButton chatId={chatId} userId={userId} />}

        {canMakeAdmin(myId, admins, creator, userId)
          && <MakeAdminButton chatId={chatId} userId={userId} />}

        {canMakeUnAdmin(myId, admins, creator, userId)
          && <MakeUnAdminButton chatId={chatId} userId={userId} />}

        {canBlockUser(myId, admins, userId, isBlocked)
          && <BlockUserButton chatId={chatId} userId={userId} />}

        {canUnBlockUser(myId, admins, isBlocked)
          && <UnBlockUserButton chatId={chatId} userId={userId} />}

      </Stack>
    </Stack>
  );
}
