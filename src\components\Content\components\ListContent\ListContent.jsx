import {
  Typography, Grid, Stack,
} from '@mui/material';
import ContentPreview from 'components/ContentPreview/ContentPreview';
import MyLink from 'components/MyLink/MyLink';
import { formatNumber } from 'utils';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import FavoriteIcon from '@mui/icons-material/Favorite';
import VisibilityIcon from '@mui/icons-material/Visibility';
import DownloadForOfflineIcon from '@mui/icons-material/DownloadForOffline';

function Analytic({ value, icon }) {
  return (
    <Stack
      direction="row"
      sx={{ color: '#AEAEB2' }}
      spacing={0.5}
      justifyContent="center"
    >
      {icon}
      <Typography sx={{ fontSize: '12px' }}>{value}</Typography>
    </Stack>
  );
}

function Analytics({
  views, likes, favorite, downloads,
}) {
  return (
    <Stack
      direction="row-reverse"
      spacing={1.5}
      sx={{ position: 'relative', marginTop: '10px' }}
    >
      <Analytic value={favorite} icon={<BookmarkIcon fontSize="10px" />} />
      <Analytic value={likes} icon={<FavoriteIcon fontSize="10px" />} />
      <Analytic value={views} icon={<VisibilityIcon fontSize="10px" />} />
      <Analytic value={downloads} icon={<DownloadForOfflineIcon fontSize="10px" />} />
    </Stack>
  );
}

function Labels({ labels }) {
  const jointLabels = labels.map((label) => `#${label}`).join(' ');

  return (
    <Typography
      sx={{
        fontWeight: 400,
        fontSize: '14px',
        color: '#7A7A7A',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        flexGrow: 1,
      }}
    >
      {jointLabels}
    </Typography>
  );
}

export default function ListContent({ content, link }) {
  return (
    <Grid container alignItems="stretch" sx={{ borderBottom: '1px solid rgba(232, 232, 232, 1)', padding: '7px 0' }}>

      <Grid item xs={3}>
        <MyLink to={link}>
          <ContentPreview
            preview={content.preview}
            fileType={content.file_type}
            withTypeIcon
          />
        </MyLink>
      </Grid>

      <Grid
        item
        xs={8}
        sx={{
          ml: 2, position: 'relative', justifyContent: 'flex-end', alignItems: 'end',
        }}
      >
        <Stack direction="column" justifyContent="space-between" sx={{ height: '100%' }}>

          <Typography
            sx={{ fontWeight: 400, fontSize: '16px' }}
          >
            {content.title}
          </Typography>

          <Labels labels={content.labels} />

          <Analytics
            views={content.views_count}
            likes={content.likes_count}
            downloads={content.downloads_count}
            favorite={formatNumber(content.favorites_count)}
          />
        </Stack>
      </Grid>
    </Grid>
  );
}
