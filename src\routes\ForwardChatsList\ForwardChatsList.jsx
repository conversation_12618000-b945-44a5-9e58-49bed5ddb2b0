import { Stack } from '@mui/material';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { selectChatIds } from 'store/messenger';
import ChatItem from 'components/ChatItem/ChatItem';
import { useLoaderData, useNavigate } from 'react-router-dom';
import useForwardMessage from 'hooks/useForwardMessage';
import MyAppBar from './components/MyAppBar/MyAppBar';
import ForwardBottomSheet from './components/ForwardBottomSheet/ForwardBottomSheet';

const APPBAR_HEIGHT = '90px';

export async function loader({ params }) {
  return params.messageDeliveryToken;
}

export default function ForwardChatsList() {
  const chatIds = useSelector(selectChatIds);
  const messageDeliveryToken = useLoaderData();
  const [searchValue, setSearchValue] = useState('');

  const handleSearchChange = (event) => {
    setSearchValue(event.target.value);
  };

  const navigate = useNavigate();
  const sendForwardMessage = useForwardMessage();
  const forwardMessage = (chatId) => {
    sendForwardMessage(chatId, messageDeliveryToken);
    navigate(-1);
  };

  const [selectedChatId, setSelectedChatId] = useState(null);

  return (
    <>
      <MyAppBar onSearchChange={handleSearchChange} />
      <Stack
        sx={{
          paddingTop: APPBAR_HEIGHT,
          pb: 2,
          overflowY: 'scroll',
          height: '100%',
        }}
        spacing={1}
      >
        {chatIds.map((chatId) => (
          <ChatItem
            key={chatId}
            chatId={chatId}
            searchedValue={searchValue}
            onClick={() => setSelectedChatId(chatId)}
          />
        ))}
      </Stack>

      <ForwardBottomSheet
        show={!!selectedChatId}
        hideBottomSheet={() => setSelectedChatId(null)}
        onConfirm={() => forwardMessage(selectedChatId)}
      />
    </>
  );
}
