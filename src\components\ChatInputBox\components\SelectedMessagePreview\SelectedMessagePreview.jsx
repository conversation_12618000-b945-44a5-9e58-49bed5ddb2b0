import MessagePreview from 'components/MessagePreview/MessagePreview';
import { SELECT_MESSAGE_REASONS } from 'constants';
import { useDispatch } from 'react-redux';
import { unsetSelectedMessage } from 'store/messenger';

function translateReason(reason) {
  if (reason === SELECT_MESSAGE_REASONS.REPLY) return 'پاسخ';
  if (reason === SELECT_MESSAGE_REASONS.EDIT) return 'ویرایش';
  return '';
}

export default function SelectedMessagePreview({ selectedMessage }) {
  const { message, reason } = selectedMessage || {};
  const dispatch = useDispatch();

  return message
    ? (
      <MessagePreview
        message={message}
        title={translateReason(reason)}
        withClose
        onClose={() => dispatch(unsetSelectedMessage({ chatId: message.chatId }))}
        bgColor="white"
      />
    )
    : '';
}
