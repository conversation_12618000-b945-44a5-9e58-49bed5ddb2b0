import { Box, FormControl, InputLabel, MenuItem, Select } from '@mui/material';

export default function Order({ searchConfig, setFilter }) {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'row', gap: 2 }}>
      <FormControl fullWidth sx={{ mt: 2 }}>
        <InputLabel id="demo-simple-select-label">ترتیب زمانی</InputLabel>
        <Select
          labelId="time-order"
          label="ترتیب زمانی"
          size="medium"
          value={searchConfig.timeOrder}
          onChange={e => {
            setFilter({
              ...searchConfig,
              ordering: e.target.value,
            });
          }}
        >
          <MenuItem value="-created_at">جدید ترین</MenuItem>
          <MenuItem value="created_at">قدیمی ترین</MenuItem>
        </Select>
      </FormControl>
      <FormControl fullWidth sx={{ mt: 2 }}>
        <InputLabel id="demo-simple-select-label">ترتیب وضعیتی</InputLabel>
        <Select
          labelId="status-order"
          label="ترتیب وضعیتی"
          size="medium"
          value={searchConfig.statusOrder}
          onChange={() => {}}
        >
          <MenuItem value="-MW">براساس در انتظار</MenuItem>
          <MenuItem value="-MA">براساس تایید شده‌</MenuItem>
          <MenuItem value="-MR">براساس رد شده‌</MenuItem>
          <MenuItem value="-MC">براساس آرشیو</MenuItem>
        </Select>
      </FormControl>
    </Box>
  );
}
