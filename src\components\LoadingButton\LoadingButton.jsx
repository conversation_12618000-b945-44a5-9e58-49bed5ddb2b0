import { Button, CircularProgress } from '@mui/material';

export default function LoadingButton({
  children,
  loading,
  loadingVariant = 'indeterminate',
  progress = '0',
  ...props
}) {
  const submitting = loading || progress > 0;
  return (
    <Button {...props} disabled={submitting}>
      {submitting ? (
        <CircularProgress
          variant={loadingVariant}
          value={progress}
          color="inherit"
          size="26px"
        />
      ) : (
        children
      )}
    </Button>
  );
}
