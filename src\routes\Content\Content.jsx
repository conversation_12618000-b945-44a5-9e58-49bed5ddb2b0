import {
  <PERSON><PERSON>,
  <PERSON>ack,
  Typography,
  <PERSON>con<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@mui/material';
import { useLoaderData, useNavigate } from 'react-router-dom';
import { ContentInteraction } from 'routes/Content/components/ContentInteraction/ContentInteraction';
import { ContentAnalytics } from 'routes/Content/components/ContentAnalytics/ContentAnalytics';
import MyAvatar from 'components/MyAvatar/MyAvatar';
import { getContent, deleteContent as deleteContentReq } from 'apis/content';
import { useDispatch, useSelector } from 'react-redux';
import { setAppbarMenu, setTitle } from 'store/layout';
import { useEffect, useState } from 'react';
import { useQuery } from 'react-query';
import {
  buildFullName,
  isAudio,
  isImage,
  isVideo,
  setPathParam,
  useIsDesktop,
} from 'utils';
import { PATHS } from 'constants';
import LoadingPage from 'components/LoadingPage/LoadingPage';
import { selectMe } from 'store/auth';
import ContentPreview from 'components/ContentPreview/ContentPreview';
import MyReactPlayer from 'components/MyReactPlayer/MyReactPlayer';
import LabelsList from 'components/LabelsList/LabelsList';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import LayersOutlinedIcon from '@mui/icons-material/LayersOutlined';
import DesktopAppBar from 'components/DesktopAppBar/DesktopAppBar';
import InteractionRequest from 'components/InteractionRequest/InteractionRequest';
import AppbarMenu from './components/AppbarMenu/AppbarMenu';
import FullScreenImage from '../../components/FullScreenImage/FullScreenImage';
import Comments from './components/Comments/Comments';
import Description from './components/Description/Description';
import ContentDetail from './components/ContentDetail/ContentDetail';
import DeleteBottomSheet from './components/DeleteBottomSheet/DeleteBottomSheet';
import ReportBottomSheet from './components/ReportBottomSheet/ReportBottomSheet';
import Title from './components/Title/Title';
import ShareOutlinedIcon from '@mui/icons-material/ShareOutlined';
import { Box } from '@mui/system';
import TelegramLogo from '../../assets/icons/TelegramLogo.png';
import WhatsappLogo from '../../assets/icons/WhatsappLogo.png';
import XTwitter from '../../assets/icons/XTwitter.png';
import eeta from '../../assets/icons/eeta.png';
import ReelIcon from '../../assets/icons/reel.png';

export async function loader({ params }) {
  return params.contentId;
}

function Header({ title, sx, hasCopy = false, copyAction }) {
  return (
    <Typography sx={{ fontSize: '16px', fontWeight: '700', ...sx }}>
      {title}
      {!!hasCopy && (
        <IconButton sx={{ display: 'inline-block' }} onClick={copyAction}>
          <FileCopyIcon />
        </IconButton>
      )}
    </Typography>
  );
}

export default function Content() {
  const contentId = useLoaderData();
  const dispatch = useDispatch();
  const [fullScreen, setFullScreen] = useState(false);
  const [showDeleteBottomSheet, setShowDeleteBottomSheet] = useState(false);
  const [showReportBottomSheet, setShowReportBottomSheet] = useState(false);
  const me = useSelector(selectMe);
  const [isCopied, setIsCopied] = useState(false);
  const isDesktop = useIsDesktop();

  const navigate = useNavigate();

  dispatch(setTitle(''));
  const { isLoading, data } = useQuery({
    queryKey: [PATHS.content, contentId],
    queryFn: () => getContent(contentId),
  });

  useEffect(() => () => dispatch(setAppbarMenu(null)), []);

  if (isLoading) return <LoadingPage />;

  const content = data.data;
  dispatch(setTitle(content.title));

  const fullName = buildFullName(
    content.author.first_name,
    content.author.last_name,
  );

  const isMine = !!me && me.id === content.author.id;

  dispatch(
    setAppbarMenu(
      <AppbarMenu
        contentId={content.id}
        showDeleteBottomSheet={() => setShowDeleteBottomSheet(true)}
        showReportBottomSheet={() => setShowReportBottomSheet(true)}
        isMine={isMine}
        evaluationRequested={content.evaluation_requested}
        total_score={content.total_score}
      />,
    ),
  );

  const showContentFullScreen = () => {
    if (isImage(content.file_type)) {
      setFullScreen(true);
    }
  };

  const isVideoOrAudio =
    isVideo(content.file_type) || isAudio(content.file_type);

  const handleCopy = () => {
    navigator.clipboard.writeText(content.description).then(() => {
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000); // Reset copy feedback after 3 seconds
    });
  };

  return (
    <>
      {isDesktop && <DesktopAppBar />}

      <Grid
        container
        sx={{
          height: '100%',
          overflowY: 'scroll',
          pb: 2,
          mt: 2,
        }}
        columnSpacing={2}
        className="no-scrollbar"
      >
        <Grid item xs={12} lg={6}>
          <div
            style={{
              position: 'absolute',
              zIndex: 10,
              padding: 5,
            }}
          >
            {/* <IconButton
              onClick={() =>
                navigate(
                  setPathParam(PATHS.contentReels, 'contentId', contentId),
                )
              }
              style={{
                margin: '5px',
                background: '#f1f2f9',
                boxShadow:
                  'rgba(0, 0, 0, 0.2) 0px 3px 1px -2px, rgba(0, 0, 0, 0.14) 0px 2px 2px 0px, rgba(0, 0, 0, 0.12) 0px 1px 5px 0px',
              }}
            >
              <img
                src={ReelIcon}
                alt="icon"
                style={{ width: 40, height: 40 }}
              />
            </IconButton> */}
          </div>
          {isVideoOrAudio ? (
            <MyReactPlayer
              url={content.file}
              isAudio={isAudio(content.file_type)}
            />
          ) : (
            <ContentPreview
              fileType={content.file_type}
              preview={content.preview}
              onClick={showContentFullScreen}
              rounded
            />
          )}
        </Grid>

        <FullScreenImage
          img={content.file}
          show={fullScreen}
          unShow={() => setFullScreen(false)}
        />

        <Grid item lg={6} xs={12}>
          <Stack spacing={4}>
            {isDesktop && (
              <Title
                title={content.title}
                contentId={content.id}
                showDeleteBottomSheet={() => setShowDeleteBottomSheet(true)}
                showReportBottomSheet={() => setShowReportBottomSheet(true)}
                isMine={isMine}
                evaluationRequested={content.evaluation_requested}
                total_score={content.total_score}
              />
            )}
            <ContentAnalytics
              interactions={content.interactions}
              contentId={content.id}
              contentUrl={content.file}
              contentName={content.name}
              contentType={content.file_type}
              like={content.likes_count}
              dislike={content.dislikes_count}
              favorite={content.favorites_count}
              view={content.views_count}
              download={content.downloads_count}
              elected={content.elected}
              evaluationRequested={content.evaluation_requested}
              total_score={content.total_score}
              evaluator_count={content.evaluator_count}
            />

            <ContentInteraction
              contentId={content.id}
              contentUrl={content.file}
              contentName={content.name}
              contentType={content.file_type}
              contentDesc={content.description}
              contentLabels={content.labels}
              size={content.size}
              evaluationRequested={content.evaluation_requested}
              myScore={content.my_score || 0}
              marfook
            />

            {content.is_open_layer && (
              <Typography
                component="span"
                sx={{
                  display: 'flex',
                  width: '100%',
                  height: '36px',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#000',
                  background: '#F8F8F8',
                }}
              >
                <LayersOutlinedIcon />
                <Typography
                  sx={{ marginLeft: '10px!important', fontSize: '14px' }}
                >
                  فایل لایه باز است
                </Typography>
              </Typography>
            )}

            <Stack direction="row" justifyContent="space-between">
              <MyAvatar
                avatar={content.author.avatar}
                fullName={fullName}
                small
                sx={{ mt: 8 }}
                username={content.author.display_username}
                userId={content.author.id}
                withLink
              />
            </Stack>

            <ContentDetail
              type={content.type}
              category={content.category}
              createdAt={content.created_at}
              isOpenLayer={content.is_open_layer}
            />

            {content.description && (
              <>
                <Header
                  title="توضیحات"
                  sx={{ marginTop: '32px !important' }}
                  hasCopy
                  copyAction={handleCopy}
                />
                {isCopied && (
                  <Typography sx={{ fontSize: '10px', color: '#11a6a1' }}>
                    توضیحات کپی شد.
                  </Typography>
                )}
                <Description dsc={content.description} />
              </>
            )}

            {content.labels.length > 0 && (
              <LabelsList labels={content.labels} />
            )}

            {content.interaction_requests?.length > 0 && (
              <>
                <Divider />
                <Stack spacing={2}>
                  <Header title="حمایت از پست مرجع:" />
                  {content.interaction_requests.map(ir => (
                    <InteractionRequest
                      id={ir.id}
                      contentId={contentId}
                      url={ir.url}
                      followRequest={ir.follow_request}
                      likeRequest={ir.like_request}
                      commentRequest={ir.comment_request}
                      followed={ir.followed}
                      liked={ir.liked}
                      commented={ir.commented}
                      note={ir.note}
                      noteId={ir.note_id}
                    />
                  ))}
                </Stack>
              </>
            )}

            <Divider />

            <Header
              title="نظرات کاربران"
              sx={{ marginTop: '32px !important' }}
            />
            <Comments contentId={contentId} />
          </Stack>
        </Grid>

        <DeleteBottomSheet
          show={showDeleteBottomSheet}
          hideBottomSheet={() => setShowDeleteBottomSheet(false)}
          onPrimary={() => {
            deleteContentReq(contentId);
            navigate(-1);
          }}
        />

        <ReportBottomSheet
          show={showReportBottomSheet}
          hideBottomSheet={() => setShowReportBottomSheet(false)}
          contentId={contentId}
        />
      </Grid>
    </>
  );
}
