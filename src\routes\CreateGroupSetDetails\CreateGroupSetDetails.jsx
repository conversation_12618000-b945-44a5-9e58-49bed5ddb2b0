import { Stack, TextField } from '@mui/material';
import { DEFAULT_APPBAR_HEIGHT } from 'components/CustomAppBar/constants';
import { useDispatch, useSelector } from 'react-redux';
import { selectSelectedContacts } from 'store/messenger';
import IconSax from 'components/IconSax/IconSax';
import EditableAvatar from 'components/EditableAvatar/EditableAvatar';
import { useState } from 'react';
import { useMutation } from 'react-query';
import { createChat } from 'apis/chat';
import { CHAT_TYPES, PATHS } from 'constants';
import { setSnackbar } from 'store/layout';
import { useNavigate } from 'react-router-dom';
import uploadFile from 'apis/storage';
import MyAppBar from './components/MyAppBar/MyAppBar';
import Contacts from './components/Contacts/Contacts';

export default function CreateGroupSetDetails() {
  const contacts = useSelector(selectSelectedContacts);
  const [groupIcon, setGroupIcon] = useState(null);
  const [groupName, setGroupName] = useState('');

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const createChatMutation = useMutation(async () => {
    let avatarId = null;
    if (groupIcon) {
      const uploadedAvatar = await uploadFile(groupIcon, () => { });
      avatarId = uploadedAvatar.data.id;
    }

    const contactsIds = contacts.map((contact) => contact.user.id);
    return createChat(contactsIds, groupName, avatarId, CHAT_TYPES.GROUP);
  }, {
    onSuccess: () => {
      dispatch(setSnackbar({ message: 'گروه با موفقیت ساخته شد', severity: 'success' }));
      navigate(PATHS.chats);
    },
    onError: () => {
      dispatch(setSnackbar({ message: 'خطا در ساخت گروه', severity: 'error' }));
    },
    onSettled: () => { },
  });

  return (
    <>
      <MyAppBar onCreateClick={() => createChatMutation.mutate()} />
      <Stack sx={{ pt: DEFAULT_APPBAR_HEIGHT }}>

        <Stack direction="row" alignItems="center" sx={{ mt: 2 }}>
          <EditableAvatar
            size="56px"
            placeholderIcon={<IconSax name="bold/camera" />}
            avatar={groupIcon}
            setAvatar={setGroupIcon}
          />
          <TextField
            sx={{ flexGrow: 1, mr: 1, ml: 2 }}
            label="نام گروه را وارد کنید"
            value={groupName}
            onChange={(e) => setGroupName(e.target.value)}
          />
        </Stack>

        <Contacts contacts={contacts} />
      </Stack>
    </>
  );
}
