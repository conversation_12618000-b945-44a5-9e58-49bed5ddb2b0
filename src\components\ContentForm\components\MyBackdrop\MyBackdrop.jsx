import { Backdrop } from '@mui/material';
import { useEffect } from 'react';

function disablePullToRefresh() {
  document.body.style.overscrollBehaviorY = 'contain';
}

function enablePullToRefresh() {
  document.body.style.overscrollBehaviorY = 'auto';
}

export default function MyBackdrop({ open, children }) {
  useEffect(() => disablePullToRefresh, [enablePullToRefresh]);

  return (
    <Backdrop
      sx={{
        color: '#fff',
        zIndex: (theme) => theme.zIndex.drawer + 1,
      }}
      open={open}
    >
      {children}
    </Backdrop>
  );
}
