import {
  Avatar, IconButton, Stack, Typography, styled,
} from '@mui/material';
import IconSax from 'components/IconSax/IconSax';
import { genUserOnlineStatus } from 'utils/chat';
import SelectedAvatar from './components/SelectedAvatar/SelectedAvatar';

const Name = styled(Typography)(() => ({
  fontSize: '14px',
  fontWeight: 500,
}));

const LastSeen = styled(Typography)(() => ({
  fontSize: '12px',
  fontWeight: 400,
  color: '#64676A',
}));

function SendButton() {
  return (
    <IconButton>
      <IconSax name="send-2" />
    </IconButton>
  );
}

export default function Contact({
  contact, onClick, selected, selectable, EndButton = <SendButton />,
}) {
  const name = `${contact.user_first_name} ${contact.user_last_name}`;
  const clickable = !!onClick;

  return (
    <Stack
      direction="row"
      alignItems="center"
      justifyContent="space-between"
      onClick={() => onClick(contact)}
      sx={{ cursor: clickable ? 'pointer' : '' }}
    >
      {selected ? <SelectedAvatar /> : <Avatar />}

      <Stack flexGrow={1} sx={{ ml: 2 }}>
        <Name>{name}</Name>
        <LastSeen>{`آخرین بازدید ${genUserOnlineStatus(contact.user)}`}</LastSeen>
      </Stack>

      {(clickable && !selectable) && EndButton}
    </Stack>
  );
}
