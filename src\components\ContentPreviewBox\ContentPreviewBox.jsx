import { Box } from '@mui/material';
import { styled } from '@mui/system';
import { CONTENT_ASPECT_RATIO } from '../../constants';

const Img = styled('img')(({ theme }) => ({
  backgroundColor: theme.palette.grey.A100,
  borderRadius: theme.shape.borderRadius * 2,
  objectFit: 'scale-down',
  width: '100%',
  height: '100%',
}));

export default function ContentPreviewBox({ img, onClick }) {
  return (
    <Box
      sx={{
        aspectRatio: `${CONTENT_ASPECT_RATIO}`,
        width: '100%',
        position: 'relative',
      }}
      onClick={onClick}
    >
      <Img src={img} />
    </Box>
  );
}
