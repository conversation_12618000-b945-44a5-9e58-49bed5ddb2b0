import Container from '@mui/material/Container';
import { useNavigate, Outlet, useLocation } from 'react-router-dom';
import { Box, alpha, useMediaQuery, useTheme } from '@mui/material';
import { PATHS } from 'constants';
import { useDispatch, useSelector } from 'react-redux';
import { selectTitle } from 'store/layout';
import { isLoggedIn } from 'utils';
import { useQuery } from 'react-query';
import { GET_ME_URL, getMe } from 'apis/auth';
import { setMe } from 'store/auth';
import { useEffect, useState } from 'react';
import MyBottomNavigation from '../MyBottomNavigation/MyBottomNavigation';
import MyAppBar from './components/MyAppBar/MyAppBar';
import MySnackbar from './components/MySnackbar/MySnackbar';

const APP_NAME = 'تام';
const CONTENT_PAGE_REGEX = /content.+\d$/;
const CONTENT_ANALYTICS_PAGE = /content.+\d.analytics$/;
const CONTENT_REELS_PAGE = /content.+\d.reels$/;
const CHAT_PAGE_REGEX = /chat.+\d$/;
const CHAT_INFO_PAGE_REGEX = /chat.+\d.info$/;
const CONTENT_INTERACTIONS_PAGE = /content.+\d.interactions.+$/;

function isContentPage(path) {
  return path.search(CONTENT_PAGE_REGEX) > 0;
}

function isContentReelsPage(path) {
  return path.search(CONTENT_REELS_PAGE) > 0;
}

function isContentAnalyticsPage(path) {
  return path.search(CONTENT_ANALYTICS_PAGE) > 0;
}

function isContentInteractionsPage(path) {
  return path.search(CONTENT_INTERACTIONS_PAGE) > 0;
}

function isChatPage(path) {
  return path.search(CHAT_PAGE_REGEX) > 0;
}

function isChatInfoPage(path) {
  return path.search(CHAT_INFO_PAGE_REGEX) > 0;
}

function isAddParticipantPage(path) {
  return path.endsWith('add-participant');
}

function isForwardContentPage(path) {
  return path.endsWith('forward');
}

function getLayoutConfig(path) {
  const config = {
    background: 'white',
    appbar: {
      hasAppBar: true,
      title: APP_NAME,
      titleCenter: true,
      titleVariant: 'h6',
      hasBack: false,
      hasLogo: false,
    },
    hasBottomNav: true,
    containerGutter: true,
  };

  if (isContentPage(path)) {
    config.appbar.hasBack = true;
    config.appbar.title = '';
    config.appbar.titleCenter = false;
    config.appbar.titleVariant = '';
    config.hasBottomNav = false;
    return config;
  }

  if (isContentAnalyticsPage(path)) {
    config.appbar.hasAppBar = false;
    config.hasBottomNav = false;
    return config;
  }

  if (isContentInteractionsPage(path)) {
    config.appbar.hasAppBar = false;
    config.hasBottomNav = false;
    return config;
  }

  if (path.startsWith('/profile')) {
    config.appbar.hasAppBar = false;
    return config;
  }

  if (isChatPage(path)) {
    config.background = '#DFDFDF';
    config.hasBottomNav = false;
    config.containerGutter = false;
    config.appbar.hasAppBar = false;
    return config;
  }

  if (isChatInfoPage(path)) {
    config.appbar.hasAppBar = false;
    config.background = '#D2DBE2';
    config.hasBottomNav = false;
    config.containerGutter = false;
    return config;
  }

  if (path.startsWith(PATHS.createChatSelectUser)) {
    config.appbar.hasAppBar = false;
    config.background = alpha('#DFDFDF', 0.2);
    config.hasBottomNav = false;
    return config;
  }

  if (isAddParticipantPage(path)) {
    config.appbar.hasAppBar = false;
    config.background = alpha('#DFDFDF', 0.2);
    config.hasBottomNav = false;
    return config;
  }

  if (path.startsWith(PATHS.forwardChatsList)) {
    config.appbar.hasAppBar = false;
    return config;
  }

  if (isForwardContentPage(path)) {
    config.appbar.hasAppBar = false;
    return config;
  }

  if (isContentReelsPage(path)) {
    config.background = 'black';
    config.appbar.hasAppBar = false;
    config.containerGutter = false;
    return config;
  }

  switch (path) {
    case PATHS.home:
      config.background = 'black';
      config.appbar.hasAppBar = false;
      return config;

    case PATHS.contentReels:
      config.background = 'black';
      config.appbar.hasAppBar = false;
      return config;

    case PATHS.createContent:
      config.appbar.title = 'بارگذاری پست جدید';
      config.appbar.hasBack = true;
      config.appbar.titleCenter = false;
      config.appbar.titleVariant = '';
      config.hasBottomNav = false;
      return config;

    case PATHS.search:
      config.appbar.hasAppBar = false;
      return config;

    case PATHS.settings:
      config.appbar.title = 'تنظیمات';
      config.appbar.titleCenter = false;
      config.appbar.titleVariant = '';
      return config;

    case PATHS.announcement:
      config.appbar.title = 'اعلانات';
      config.background = '#F9F8FC';
      config.appbar.hasBack = true;
      config.appbar.titleCenter = false;
      config.appbar.titleVariant = '';
      return config;

    case PATHS.terms:
      config.background = 'black';
      config.appbar.hasBack = true;
      config.appbar.title = 'مقررات و آموزش بهره‌برداری';
      config.appbar.titleCenter = false;
      config.appbar.titleVariant = '';
      config.containerGutter = false;
      config.hasBottomNav = false;
      return config;

    case PATHS.faq:
      config.background = 'black';
      config.appbar.hasBack = true;
      config.appbar.title = 'سوالات متداول و راهنمایی';
      config.appbar.titleCenter = false;
      config.appbar.titleVariant = '';
      config.containerGutter = false;
      config.hasBottomNav = false;
      config.background = alpha('#DFDFDF', 0.2);
      return config;

    case PATHS.chats:
      config.appbar.hasLogo = true;
      config.background = alpha('#DFDFDF', 0.2);
      return config;

    case PATHS.createChat:
      config.appbar.hasAppBar = false;
      config.background = alpha('#DFDFDF', 0.2);
      config.hasBottomNav = false;
      return config;

    case PATHS.createGroupSetDetail:
      config.appbar.hasAppBar = false;
      config.background = alpha('#DFDFDF', 0.2);
      config.hasBottomNav = false;
      return config;

    case PATHS.createChannelSetDetail:
      config.appbar.hasAppBar = false;
      config.background = alpha('#DFDFDF', 0.2);
      config.hasBottomNav = false;
      return config;

    case PATHS.createContact:
      config.appbar.hasAppBar = false;
      config.hasBottomNav = false;
      return config;

    case PATHS.login_please:
      config.appbar.hasAppBar = false;
      return config;

    default:
      return config;
  }
}

function useGetLayoutConfig(pathname) {
  const [config, setConfig] = useState({
    background: 'white',
    appbar: {
      hasAppBar: true,
      title: APP_NAME,
      titleCenter: true,
      titleVariant: 'h6',
      hasBack: false,
      hasLogo: false,
    },
    hasBottomNav: true,
    containerGutter: true,
  });

  useEffect(() => {
    setConfig(getLayoutConfig(pathname));
  }, [pathname]);

  return config;
}

export default function Layout({ children }) {
  const location = useLocation();
  const config = useGetLayoutConfig(location.pathname);

  const title = useSelector(selectTitle);
  if (isContentPage(location.pathname)) {
    config.appbar.title = title;
  }

  const bottomPadding = (() => {
    if (config.hasBottomNav) return 7;
    if (config.containerGutter) return 2;
    return 0;
  })();

  const navigate = useNavigate();
  if (isLoggedIn()) {
    const dispatch = useDispatch();
    useQuery(GET_ME_URL, getMe, {
      onSuccess: data => {
        dispatch(setMe(data.data));
        if (data.data && !data.data.filled_registration_form) {
          navigate(PATHS.registration_form);
        }
      },
    });
  }

  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));

  return (
    <Box
      sx={{
        position: 'fixed',
        pb: isDesktop ? 0 : bottomPadding,
        pt: config.appbar.hasAppBar ? 7 : 2,
        bottom: 0,
        top: 0,
        right: 0,
        left: 0,
        background: config.background,
        height: '100%',
        overflowY: 'scroll',
      }}
    >
      {config.appbar.hasAppBar && !isDesktop && <MyAppBar {...config.appbar} />}
      <Container maxWidth="lg" disableGutters={!config.containerGutter}>
        <Outlet />
        {children}
      </Container>
      {config.hasBottomNav && !isDesktop && <MyBottomNavigation />}

      <MySnackbar />
    </Box>
  );
}
