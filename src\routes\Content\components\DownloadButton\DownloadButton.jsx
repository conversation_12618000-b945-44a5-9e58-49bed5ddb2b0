import { Button, CircularProgress, Typography } from '@mui/material';
import { addInteraction } from 'apis/content';
import { DOWNLOAD } from 'constants';
import IconSax from 'components/IconSax/IconSax';
import useDownloader from 'react-use-downloader';
import { useEffect } from 'react';

export default function DownloadButton({
  id,
  url,
  name,
  size,
  sx,
}) {
  const {
    percentage,
    download,
    cancel,
    isInProgress,
  } = useDownloader();

  // Cancel download on unmount
  useEffect(() => () => cancel(), []);

  function onDownloadClick() {
    addInteraction({ contentId: id, interaction: DOWNLOAD });

    if (isInProgress) {
      cancel();
    } else {
      download(url, name);
    }
  }

  const color = isInProgress ? 'red' : '#64676A';
  const startIcon = isInProgress ? (
    <CircularProgress
      variant="determinate"
      value={percentage < 5 ? 5 : percentage}
      sx={{
        color: 'red',
        border: '1px solid #ccc',
        borderRadius: '50%',
      }}
      size={20}
      thickness={5}
    />
  ) : (
    <IconSax name="direct-inbox" />
  );

  return (
    <Button
      onClick={onDownloadClick}
      sx={{
        color,
        height: '40px',
        backgroundColor: '#F1F2F9',
        borderRadius: '6px',
        border: '1px solid #D6D6D6',
        ...sx,
      }}
      startIcon={startIcon}
    >
      <Typography sx={{ fontSize: '14px', fontWeight: 'bold', fontStyle: 'bold' }}>
        دانلود
      </Typography>
      <Typography sx={{
        fontSize: '12px', color: '#737373', fontWeight: 500, ml: 1,
      }}
      >
        (
        {size}
        )
      </Typography>
    </Button>
  );
}
