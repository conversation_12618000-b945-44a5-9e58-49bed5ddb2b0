import {
  FormControlLabel,
  FormGroup,
  Grid,
  Stack,
  TextField,
  Typography,
  Checkbox,
  Button,
} from '@mui/material';
import { SEARCH_PARAMS } from 'components/SearchBox/searchConfig';
import { SEARCH_LOCATIONS } from 'constants';
import { useSelector } from 'react-redux';
import { selectMe } from 'store/auth';
import JalaliDatePicker from '../JalaliDatePicker/JalaliDatePicker';

function Title({ title, sx }) {
  return <Typography sx={{ fontSize: '14px', ...sx }}>{title}</Typography>;
}

function SearchLocation({ defaultValue = '' }) {
  const searchLocations = SEARCH_LOCATIONS.map((location) => ({
    ...location,
    checked: defaultValue.includes(location.value),
  }));

  return (
    <Stack>
      <Title title="جست‌وجو بر روی موارد زیر انجام می‌شود" />
      <FormGroup>
        <Grid container>
          {searchLocations.map((location) => (
            <Grid item xs={3} key={location.value}>
              <FormControlLabel
                label={location.name}
                control={(
                  <Checkbox
                    name={location.value}
                    defaultChecked={location.checked}
                    size="small"
                  />
                )}
                sx={{
                  fontSize: '14px',
                  '& span': {
                    fontSize: '14px',
                  },
                }}
              />
            </Grid>
          ))}
        </Grid>
      </FormGroup>
    </Stack>
  );
}

function RangeFilter({
  title, fromName, toName, defaultFrom, defaultTo,
}) {
  return (
    <Stack>
      <Stack direction="row" spacing={1} sx={{ mt: 0 }}>
        <Title title={title} sx={{ width: '80px', paddingTop: '9px' }} />
        <TextField
          variant="outlined"
          type="number"
          label="از"
          name={fromName}
          defaultValue={defaultFrom}
          fullWidth
          size="small"
        />

        <TextField
          variant="outlined"
          type="number"
          label="تا"
          name={toName}
          defaultValue={defaultTo}
          fullWidth
          size="small"
        />
      </Stack>
    </Stack>
  );
}

function DateRangeFilter({ defaultFrom, defaultTo }) {
  return (
    <Stack>
      <Stack direction="row" spacing={1} sx={{ mt: 0 }}>
        <Title title="تاریخ" sx={{ width: '80px', paddingTop: '9px' }} />
        <JalaliDatePicker
          label="از"
          inputName={SEARCH_PARAMS.DATE_FROM}
          defaultValue={defaultFrom}
        />
        <JalaliDatePicker
          label="تا"
          inputName={SEARCH_PARAMS.DATE_TO}
          defaultValue={defaultTo}
        />
      </Stack>
    </Stack>
  );
}

function CheckboxFormGroup({ defaultValue, name, label }) {
  return (
    <FormGroup>
      <FormControlLabel
        componentsProps={{ typography: { fontSize: '14px' } }}
        sx={{ justifyContent: 'space-between', m: 0 }}
        control={(
          <Checkbox
            defaultChecked={defaultValue}
            sx={{ pr: 0 }}
            name={name}
            id={name}
            size="small"
          />
        )}
        label={label}
        labelPlacement="start"
      />
    </FormGroup>
  );
}

function JustElectedOnes({ elected }) {
  return (
    <FormGroup>
      <FormControlLabel
        componentsProps={{ typography: { fontSize: '14px' } }}
        sx={{ justifyContent: 'space-between', m: 0 }}
        control={(
          <Checkbox
            defaultChecked={elected}
            sx={{ pr: 0 }}
            name="elected"
            id="elected"
            size="small"
          />
        )}
        label="فقط منتخب‌ها"
        labelPlacement="start"
      />
    </FormGroup>
  );
}

export default function AdvancedSearch({ searchConfig }) {
  const me = useSelector(selectMe);

  return (
    <Stack spacing={2} sx={{ mt: 1, mb: 1 }}>
      {me && <JustElectedOnes elected={searchConfig[SEARCH_PARAMS.ELECTED]} />}
      {me && (
        <CheckboxFormGroup
          defaultValue={searchConfig[SEARCH_PARAMS.SUPPORT_REQUEST]}
          name="support_request"
          label="بازی جنگ"
        />
      )}
      {/* {me && (
        <CheckboxFormGroup
          defaultValue={searchConfig[SEARCH_PARAMS.LIKE_REQUEST]}
          name="like_request"
          label="پسندیدن"
        />
      )}
      {me && (
        <CheckboxFormGroup
          defaultValue={searchConfig[SEARCH_PARAMS.COMMENT_REQUEST]}
          name="comment_request"
          label="نظر گذاشتن"
        />
      )} */}

      <SearchLocation
        defaultValue={searchConfig[SEARCH_PARAMS.SEARCH_FIELDS]}
      />

      <DateRangeFilter
        defaultFrom={searchConfig[SEARCH_PARAMS.DATE_FROM]}
        defaultTo={searchConfig[SEARCH_PARAMS.DATE_TO]}
      />

      <RangeFilter
        title="لایک"
        fromName={SEARCH_PARAMS.LIKES_FROM}
        toName={SEARCH_PARAMS.LIKES_TO}
        defaultFrom={searchConfig[SEARCH_PARAMS.LIKES_FROM]}
        defaultTo={searchConfig[SEARCH_PARAMS.LIKES_TO]}
      />

      <RangeFilter
        title="دانلود"
        fromName={SEARCH_PARAMS.DOWNLOADS_FROM}
        toName={SEARCH_PARAMS.DOWNLOADS_TO}
        defaultFrom={searchConfig[SEARCH_PARAMS.DOWNLOADS_FROM]}
        defaultTo={searchConfig[SEARCH_PARAMS.DOWNLOADS_TO]}
      />

      <Button variant="contained" type="submit">
        اعمال
      </Button>
    </Stack>
  );
}
