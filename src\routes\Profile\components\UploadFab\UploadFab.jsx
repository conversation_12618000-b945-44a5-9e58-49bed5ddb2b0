import { Fab } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import AddIcon from '@mui/icons-material/Add';
import { PATHS } from '../../../../constants';

export default function UploadFab() {
  const navigate = useNavigate();

  return (
    <Fab
      color="primary"
      sx={{ position: 'fixed', bottom: '64px' }}
      size="medium"
      onClick={() => navigate(PATHS.createContent)}
    >
      <AddIcon />
    </Fab>
  );
}
