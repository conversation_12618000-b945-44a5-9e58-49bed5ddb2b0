import { InputBase } from '@mui/material';
import { useRef } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { createComment } from '../../../../apis/content';
import LoadingButton from '../../../../components/LoadingButton/LoadingButton';
import SendCommentContainer from '../SendCommentContainer/SendCommentContainer';

export default function SendComment({ contentId }) {
  const commentRef = useRef();
  const queryClient = useQueryClient();

  const mutation = useMutation(async () => {
    if (!commentRef.current.value) return;
    await createComment({
      comment: commentRef.current.value,
      content: contentId,
    });
    commentRef.current.value = '';
    queryClient.invalidateQueries({ queryKey: ['comments', contentId] });
  });

  return (
    <SendCommentContainer>
      <InputBase
        fullWidth
        placeholder="نظر خود را بنویسید"
        multiline
        rows={3}
        inputRef={commentRef}
      />
      <LoadingButton
        variant="contained"
        sx={{ width: '100%', mt: 2 }}
        onClick={mutation.mutate}
        loading={mutation.isLoading}
      >
        ارسال نظر
      </LoadingButton>
    </SendCommentContainer>
  );
}
