import { Button, Stack, Typography } from '@mui/material';
import IconSax from 'components/IconSax/IconSax';
import { PATHS } from 'constants';
import { useNavigate } from 'react-router-dom';

export default function LoginPlease() {
  const navigate = useNavigate();
  const redirectToLogin = () => navigate(PATHS.enter_mobile);

  return (
    <Stack justifyContent="center" alignItems="center" sx={{ height: '100%' }}>
      <IconSax name="shield-security" sx={{ width: 64, height: 64 }} />
      <Typography sx={{ mt: 2 }}>
        برای مشاهده‌ی صفحه لطفا
        وارد شوید
      </Typography>
      <Button sx={{ mt: 2 }} onClick={redirectToLogin}>ورود به برنامه</Button>
    </Stack>
  );
}
