import {
  Box,
  Typography,
  Grid,
  TextField,
  InputAdornment,
} from '@mui/material';
import { useState } from 'react';
import { useQuery } from 'react-query';
import { Search } from '@mui/icons-material';
import { getMarfookInstructionsList } from 'apis/marfook';
import LoadingPage from 'components/LoadingPage/LoadingPage';
import PermissionRequired from 'components/PermissionRequired/PermissionRequired';
import DesktopAppBar from 'components/DesktopAppBar/DesktopAppBar';
import { useIsDesktop } from 'utils';
import InstructionCard from './components/InstructionCard/InstructionCard';
import InstructionModal from './components/InstructionModal/InstructionModal';

function MarfookInstructionsFolderContent() {
  const isDesktop = useIsDesktop();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedInstruction, setSelectedInstruction] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);

  const { isLoading, data, error } = useQuery(
    ['marfookInstructionsList'],
    () => getMarfookInstructionsList(),
  );

  const instructions = data?.data || [];

  // Filter instructions based on search term
  const filteredInstructions = instructions.filter(instruction => {
    const searchLower = searchTerm.toLowerCase();
    return (
      instruction.code?.toLowerCase().includes(searchLower) ||
      instruction.title?.toLowerCase().includes(searchLower) ||
      instruction.subject?.some(subj =>
        subj.title?.toLowerCase().includes(searchLower),
      )
    );
  });

  const handleViewInstruction = instruction => {
    setSelectedInstruction(instruction);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setSelectedInstruction(null);
  };

  if (isLoading) {
    return <LoadingPage />;
  }

  if (error) {
    return (
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        <Typography variant="h6" color="error">
          خطا در بارگذاری دستورالعمل‌ها
        </Typography>
      </Box>
    );
  }

  return (
    <>
      {isDesktop && <DesktopAppBar />}
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 2 }}>
            تابلو اعلانات مرفوک
          </Typography>
          <Typography variant="body1" color="text.secondary">
            دستورالعمل‌های ثبت شده توسط ادمین سامانه
          </Typography>
        </Box>

        {/* Search Box */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'center' }}>
          <TextField
            placeholder="جستجو بر اساس شماره، عنوان یا محور..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            sx={{ maxWidth: 500, width: '100%' }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </Box>

        {/* Instructions Grid */}
        {filteredInstructions.length === 0 ? (
          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <Typography variant="h6" color="text.secondary">
              {searchTerm ? 'نتیجه‌ای یافت نشد' : 'دستورالعملی موجود نیست'}
            </Typography>
          </Box>
        ) : (
          <Grid container spacing={3}>
            {filteredInstructions.map(instruction => (
              <Grid item xs={12} sm={6} md={4} key={instruction.id}>
                <InstructionCard
                  instruction={instruction}
                  onView={() => handleViewInstruction(instruction)}
                />
              </Grid>
            ))}
          </Grid>
        )}

        {/* Instruction Details Modal */}
        <InstructionModal
          open={modalOpen}
          instruction={selectedInstruction}
          onClose={handleCloseModal}
        />
      </Box>
    </>
  );
}

export default function MarfookInstructionsFolder() {
  return (
    <PermissionRequired requiredPermission="MarfookInstructionViewer">
      <MarfookInstructionsFolderContent />
    </PermissionRequired>
  );
}
